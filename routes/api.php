<?php

use App\Http\Controllers\ErrorTestController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\LogHealthController;
use App\Http\Controllers\RedisTestController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// 线索相关路由
Route::prefix('leads')->group(function () {
    Route::get('/', [LeadController::class, 'list']);
    Route::post('/', [LeadController::class, 'store']);
    Route::get('/{id}', [LeadController::class, 'show']);
    Route::put('/{id}', [LeadController::class, 'update']);
    Route::delete('/{id}', [LeadController::class, 'destroy']);
    Route::patch('/batch-status', [LeadController::class, 'batchUpdateStatus']);
});

// 日志健康检查路由（建议在生产环境中添加认证中间件）
Route::prefix('system')->group(function () {
    Route::get('/log-health', [LogHealthController::class, 'health']);
    Route::get('/log-health/resilient-status', [LogHealthController::class, 'resilientStatus']);
    Route::get('/log-health/history', [LogHealthController::class, 'healthHistory']);
    Route::post('/log-health/check', [LogHealthController::class, 'triggerHealthCheck']);
    Route::post('/log-health/reset-circuit-breaker', [LogHealthController::class, 'resetCircuitBreaker']);
    Route::post('/log-health/test-logging', [LogHealthController::class, 'testLogging']);
});
// Redis 服务测试路由（仅在非生产环境启用）
if (! app()->environment('production')) {
    Route::prefix('test/redis')->group(function () {
        Route::get('/ping', [RedisTestController::class, 'ping']);
        Route::post('/basic', [RedisTestController::class, 'basicOperations']);
        Route::get('/batch', [RedisTestController::class, 'batchOperations']);
        Route::get('/counter', [RedisTestController::class, 'counterOperations']);
        Route::get('/pattern', [RedisTestController::class, 'patternMatching']);
        Route::get('/comprehensive', [RedisTestController::class, 'comprehensiveTest']);
    });
}

// 错误测试路由（仅在非生产环境启用）
if (! app()->environment('production')) {
    Route::prefix('test/errors')->group(function () {
        Route::get('/', [ErrorTestController::class, 'getTestList']);
        Route::get('/normal', [ErrorTestController::class, 'testNormal']);
        Route::get('/fatal-error', [ErrorTestController::class, 'testFatalError']);
        Route::get('/parse-error', [ErrorTestController::class, 'testParseError']);
        Route::get('/warning', [ErrorTestController::class, 'testWarning']);
        Route::get('/notice', [ErrorTestController::class, 'testNotice']);
        Route::get('/exception', [ErrorTestController::class, 'testException']);
        Route::get('/business-exception', [ErrorTestController::class, 'testBusinessException']);
        Route::get('/memory-error', [ErrorTestController::class, 'testMemoryError']);
        Route::get('/division-by-zero', [ErrorTestController::class, 'testDivisionByZero']);
        Route::get('/type-error', [ErrorTestController::class, 'testTypeError']);
        Route::get('/array-error', [ErrorTestController::class, 'testArrayError']);
        Route::get('/stack-overflow', [ErrorTestController::class, 'testStackOverflow']);
    });

    // Telescope 查询监控测试路由
    Route::prefix('test/telescope')->group(function () {
        Route::get('/queries', function () {
            // 模拟多个查询来测试 Telescope 监控
            $leads = \App\Models\Lead::take(5)->get();

            // 模拟 N+1 查询问题
            foreach ($leads as $lead) {
                $contacts = $lead->contacts; // 这会触发 N+1 查询
                // 使用 $contacts 变量避免未使用变量警告
                unset($contacts);
            }

            // 模拟慢查询
            \DB::select('SELECT SLEEP(0.1)');

            return response()->json([
                'message' => 'Telescope 查询监控测试完成',
                'leads_count' => $leads->count(),
                'note' => '请访问 /telescope 查看查询监控数据',
            ]);
        });

        Route::get('/performance', function () {
            // 模拟性能测试
            $start = microtime(true);

            // 执行多个查询
            \App\Models\Lead::count();
            \App\Models\Contact::count();

            // 模拟复杂查询
            \App\Models\Lead::with(['contacts', 'users'])->take(10)->get();

            $end = microtime(true);
            $duration = ($end - $start) * 1000;

            return response()->json([
                'message' => '性能测试完成',
                'duration_ms' => round($duration, 2),
                'note' => '请在 Telescope 中查看详细的查询分析',
            ]);
        });
    });
}

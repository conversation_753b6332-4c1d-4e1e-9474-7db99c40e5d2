# 开发流程规则

## 任务管理规范

### 1. 任务文档创建

#### 1.1 任务文档要求

- **位置**: 所有任务文档存储在 `.cursor/tasks/` 目录
- **命名**: 使用 `{功能}-{类型}-task.md` 格式
- **内容**: 包含任务概述、实施步骤、验证结果

```markdown
# 正确示例文件名
.cursor/tasks/lead-dto-refactor-task.md
.cursor/tasks/phpstan-enhancement-task.md
.cursor/tasks/api-optimization-task.md

# 错误示例
.cursor/tasks/task1.md              // 不够描述性
.cursor/tasks/lead_task.md          // 格式错误
docs/lead-task.md                   // 位置错误
```

#### 1.2 任务文档结构

```markdown
# {功能名称} 任务

## 任务概述
简洁描述任务目标和背景

## 当前状态
- ✅ 已完成项目
- 🔄 进行中项目
- ❌ 待处理项目

## 具体实施步骤
### 1. 步骤一
详细描述实施内容

### 2. 步骤二
详细描述实施内容

## 预期效果
列出预期达成的目标

## 验证结果
- ✅ 验证项目一
- ✅ 验证项目二
```



## 代码质量检查

### 1. 静态分析要求

#### 1.1 PHPStan 检查

- **级别**: 必须通过 Level 8 检查
- **命令**: `make analyze` 或 `./vendor/bin/phpstan analyse`
- **基线**: 使用基线文件管理现有问题

```bash
# 运行静态分析
make analyze

# 生成基线文件（仅在必要时）
make analyze-baseline

# 清除缓存
make analyze-clear-cache
```

#### 1.2 代码格式检查

- **工具**: Laravel Pint (PSR-12 标准)
- **检查**: `make lint`
- **修复**: `make lint-fix`

```bash
# 检查代码格式
make lint

# 自动修复格式问题
make lint-fix
```

### 2. 测试验证

#### 2.1 测试执行

- **单元测试**: `make test-unit`
- **功能测试**: `make test-feature`
- **全部测试**: `make test`

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 生成覆盖率报告
make test-coverage
```

#### 2.2 测试要求

- **覆盖率**: 核心业务逻辑 >= 80%
- **边界测试**: 必须测试边界条件
- **异常测试**: 测试异常处理逻辑

### 3. 提交前检查清单

#### 3.1 必检项目

- [ ] 通过 PHPStan Level 8 检查
- [ ] 通过 Laravel Pint 格式检查
- [ ] 相关测试通过
- [ ] 更新相关文档
- [ ] 提交信息清晰

#### 3.2 检查命令

```bash
# 完整的提交前检查
make analyze && make lint && make test
```

## 文档管理规范

### 1. 文档输出要求

#### 1.1 文档位置

- **技术文档**: 存储在 `docs/` 目录
- **任务文档**: 存储在 `.cursor/tasks/` 目录

#### 1.2 文档命名

- **技术文档**: `{功能}-{类型}.md`
- **架构文档**: `{模块}-architecture.md`
- **API 文档**: `{模块}-api.md`

```markdown
# 正确示例
docs/lead-dto-architecture.md
docs/mysql-compatibility-analysis.md
docs/api-design-guide.md

# 错误示例
docs/doc1.md                    // 不够描述性
docs/lead_architecture.md      // 格式错误
```

#### 1.3 文档内容要求

- **语言**: 使用中文编写
- **格式**: Markdown 格式
- **结构**: 清晰的层次结构
- **示例**: 包含代码示例和对比

### 2. 文档更新流程

#### 2.1 功能开发完成后

1. 创建技术文档在 `docs/` 目录
2. 更新相关的 API 文档
3. 更新 README.md（如需要）

#### 2.2 文档审查

- **准确性**: 确保文档与代码一致
- **完整性**: 覆盖所有重要功能点
- **可读性**: 结构清晰，易于理解

## 性能和安全检查

### 1. 性能监控

#### 1.1 查询优化

- **N+1 查询**: 使用 `with()` 预加载关联数据
- **索引检查**: 确保查询字段有适当索引
- **批量操作**: 大量数据使用批量处理

```php
// ✅ 正确示例：预加载关联数据
$leads = Lead::with(['contacts', 'users'])->get();

// ❌ 错误示例：N+1 查询
$leads = Lead::all();
foreach ($leads as $lead) {
    $contacts = $lead->contacts; // 每次循环都查询数据库
}
```

#### 1.2 缓存策略

- **查询缓存**: 缓存频繁查询的结果
- **配置缓存**: 生产环境启用配置缓存
- **路由缓存**: 生产环境启用路由缓存

### 2. 安全检查

#### 2.1 输入验证

- **Request 验证**: 所有输入必须通过 Request 类验证
- **类型检查**: 使用强类型约束
- **业务验证**: 在 Service 层进行业务规则验证

#### 2.2 数据安全

- **SQL 注入**: 使用 Eloquent ORM 或参数化查询
- **XSS 防护**: 输出数据使用适当的转义
- **敏感信息**: 不在日志中记录敏感信息

## 环境管理

### 1. 开发环境

#### 1.1 环境配置

- **PHP**: >= 8.2
- **Laravel**: 10.x
- **MySQL**: >= 5.7.28
- **Redis**: >= 7.2.4

#### 1.2 开发工具

- **PHPStan**: Level 8
- **Laravel Pint**: PSR-12
- **PHPUnit**: ^10.1
- **Laravel Telescope**: 开发环境启用

### 2. 生产环境准备

#### 2.1 优化配置

```bash
# 生产环境优化
composer install --no-dev --optimize-autoloader
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### 2.2 健康检查

- **数据库连接**: 验证数据库可访问
- **缓存服务**: 验证 Redis 连接
- **文件权限**: 检查存储目录权限

## 错误处理和日志

### 1. 日志记录

#### 1.1 日志级别

- **debug**: 调试信息
- **info**: 一般信息（业务操作）
- **warning**: 警告信息
- **error**: 错误信息

#### 1.2 日志格式

```php
// ✅ 正确示例：结构化日志
Log::info('线索创建成功', [
    'lead_id' => $lead->id,
    'company_name' => $lead->company_full_name,
    'creator_id' => $lead->creator_id,
    'trace_id' => request()->header('X-Trace-ID'),
]);

// ❌ 错误示例：非结构化日志
Log::info("线索 {$lead->id} 创建成功");
```

### 2. 异常处理

#### 2.1 异常分类

- **业务异常**: 继承 BusinessException
- **系统异常**: 继承标准异常类
- **验证异常**: Laravel 验证异常

#### 2.2 异常响应

- **统一格式**: 使用 ApiResponse 统一响应格式
- **错误码**: 使用配置化的错误码
- **上下文信息**: 提供足够的调试信息

## 持续改进

### 1. 代码审查

#### 1.1 审查要点

- **架构合规**: 是否遵循分层架构
- **代码质量**: 命名、注释、类型约束
- **性能考虑**: 查询优化、缓存使用
- **安全检查**: 输入验证、权限控制

#### 1.2 改进建议

- **重构机会**: 识别重复代码和改进点
- **性能优化**: 发现性能瓶颈
- **架构演进**: 提出架构改进建议

### 2. 技术债务管理

#### 2.1 债务识别

- **代码异味**: 长方法、大类、重复代码
- **技术过时**: 使用过时的技术或模式
- **文档缺失**: 缺少必要的文档

#### 2.2 债务处理

- **优先级**: 根据影响程度排优先级
- **计划**: 制定技术债务清理计划
- **跟踪**: 使用任务文档跟踪进度

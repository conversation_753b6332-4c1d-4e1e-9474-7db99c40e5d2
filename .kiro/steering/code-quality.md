# 代码质量规则

## 命名约定规范

### 1. 类命名规范

#### 1.1 基本规则

- **格式**: PascalCase（首字母大写的驼峰命名）
- **描述性**: 名称应清晰描述类的职责
- **避免缩写**: 除非是广泛认知的缩写

```php
// ✅ 正确示例
class LeadService { }
class LeadRepository { }
class CreateLeadRequest { }
class LeadCreateDTO { }

// ❌ 错误示例
class leadservice { }        // 格式错误
class LS { }                 // 过度缩写
class LeadMgr { }           // 不清晰的缩写
```

#### 1.2 特定类型命名

- **Controller**: `{Entity}Controller`
- **Service**: `{Entity}Service`
- **Repository**: `{Entity}Repository`
- **DTO**: `{Entity}{Action}DTO`
- **Request**: `{Action}{Entity}Request`
- **Resource**: `{Entity}Resource`
- **Exception**: `{Purpose}Exception`

### 2. 方法命名规范

#### 2.1 基本规则

- **格式**: camelCase（首字母小写的驼峰命名）
- **动词开头**: 方法名应以动词开头
- **描述行为**: 清晰描述方法的行为

```php
// ✅ 正确示例
public function createLead(LeadCreateDTO $dto): Lead { }
public function findById(int $id): ?Lead { }
public function existsByCompanyName(string $name): bool { }
public function getActiveLeads(): Collection { }

// ❌ 错误示例
public function lead_create($data) { }     // 格式错误
public function process($data) { }         // 不够描述性
public function doStuff() { }             // 含糊不清
```

#### 2.2 特定用途命名

- **查询方法**: `find*`, `get*`, `exists*`, `has*`
- **操作方法**: `create*`, `update*`, `delete*`, `save*`
- **验证方法**: `validate*`, `check*`, `is*`
- **转换方法**: `to*`, `from*`, `convert*`

### 3. 变量命名规范

#### 3.1 基本规则

- **格式**: camelCase
- **描述性**: 变量名应描述其内容或用途
- **避免单字母**: 除了循环计数器

```php
// ✅ 正确示例
$leadData = $request->validated();
$companyName = $dto->companyFullName;
$isActive = $lead->status === Lead::STATUS_ACTIVE;
$userList = $this->userRepository->getActiveUsers();

// ❌ 错误示例
$data = $request->validated();        // 不够描述性
$x = $dto->companyFullName;          // 单字母变量
$temp = $lead->status;               // 临时变量名
```

#### 3.2 特定类型变量

- **集合**: 使用复数形式 `$leads`, `$users`
- **布尔值**: 使用 `is*`, `has*`, `can*` 前缀
- **计数**: 使用 `*Count` 后缀
- **配置**: 使用 `*Config` 后缀

### 4. 常量命名规范

#### 4.1 基本规则

- **格式**: UPPER_SNAKE_CASE（全大写下划线分隔）
- **分组**: 相关常量应分组定义
- **前缀**: 使用类名或功能前缀

```php
// ✅ 正确示例
class Lead extends Model
{
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;
    public const STATUS_DELETED = 3;
    
    public const REGION_NORTH = 1;
    public const REGION_SOUTH = 2;
    
    public const MAX_RETRY_COUNT = 3;
    public const DEFAULT_PAGE_SIZE = 20;
}

// ❌ 错误示例
public const active = 1;              // 格式错误
public const STATUS1 = 1;            // 不够描述性
public const SOME_CONSTANT = 'value'; // 含糊不清
```

## 类型注解规范

### 1. PHPDoc 注释要求

#### 1.1 类注释

- **必须**: 所有类都必须有类级别的 PHPDoc
- **内容**: 简洁描述类的职责和用途
- **不包含**: 作者、时间等元信息

```php
// ✅ 正确示例
/**
 * 线索业务服务类
 * 
 * 处理线索相关的业务逻辑，包括创建、更新、删除和查询操作
 */
class LeadService
{
    // ...
}

// ❌ 错误示例
/**
 * LeadService
 * 
 * <AUTHOR> Doe
 * @created 2024-01-01
 */
class LeadService
{
    // ...
}
```

#### 1.2 方法注释

- **必须**: 所有 public 和 protected 方法必须有注释
- **参数**: 详细描述每个参数的类型和用途
- **返回值**: 明确返回值类型和含义
- **异常**: 列出可能抛出的异常

```php
// ✅ 正确示例
/**
 * 创建新的线索
 * 
 * @param LeadCreateDTO $dto 线索创建数据传输对象
 * @return Lead 创建的线索实例
 * @throws BusinessException 当业务规则验证失败时
 */
public function createLead(LeadCreateDTO $dto): Lead
{
    // ...
}
```

#### 1.3 复杂数组类型注解

- **数组结构**: 使用 `array{key: type, ...}` 格式
- **简单数组**: 使用 `array<type>` 格式
- **可选字段**: 使用 `?` 标记

```php
// ✅ 正确示例
/**
 * @param array{
 *     company_full_name: string,
 *     company_short_name: string,
 *     internal_name?: string|null,
 *     region: int,
 *     status?: int|null
 * } $data 线索数据
 */
public function processLeadData(array $data): void
{
    // ...
}

/**
 * @param array<int> $ids 线索ID列表
 * @return array<Lead> 线索对象列表
 */
public function findByIds(array $ids): array
{
    // ...
}
```

### 2. PHP 类型声明

#### 2.1 参数类型声明

- **必须**: 所有方法参数必须有类型声明
- **严格**: 使用最具体的类型
- **联合类型**: PHP 8.0+ 支持联合类型

```php
// ✅ 正确示例
public function createLead(LeadCreateDTO $dto): Lead { }
public function findById(int $id): ?Lead { }
public function updateStatus(int $leadId, int $status): bool { }
public function processData(string|array $data): void { } // PHP 8.0+

// ❌ 错误示例
public function createLead($dto) { }           // 缺少类型声明
public function findById($id): Lead { }        // 参数类型缺失
public function updateStatus($leadId, $status) { } // 全部缺失
```

#### 2.2 返回类型声明

- **必须**: 所有方法必须有返回类型声明
- **可空**: 使用 `?Type` 表示可空类型
- **void**: 无返回值使用 `void`

```php
// ✅ 正确示例
public function createLead(LeadCreateDTO $dto): Lead { }
public function findById(int $id): ?Lead { }
public function deleteById(int $id): bool { }
public function logActivity(string $message): void { }

// ❌ 错误示例
public function createLead(LeadCreateDTO $dto) { }     // 缺少返回类型
public function findById(int $id): Lead { }            // 应该是 ?Lead
```

#### 2.3 属性类型声明

- **必须**: 所有类属性必须有类型声明
- **只读**: DTO 使用 `readonly` 属性
- **可空**: 使用 `?Type` 表示可空属性

```php
// ✅ 正确示例
class LeadCreateDTO
{
    public readonly string $companyFullName;
    public readonly ?string $internalName;
    public readonly int $region;
}

class LeadService
{
    private LeadRepositoryInterface $leadRepository;
    private TransactionManagerInterface $transactionManager;
}

// ❌ 错误示例
class LeadCreateDTO
{
    public $companyFullName;        // 缺少类型声明
    public readonly $internalName;  // 缺少类型声明
}
```

## 异常处理规范

### 1. 异常类型定义

#### 1.1 业务异常

- **继承**: 继承 `BusinessException` 基类
- **配置化**: 使用配置文件定义错误码和消息
- **上下文**: 提供异常上下文信息

```php
// ✅ 正确示例
class BusinessException extends Exception
{
    public static function fromErrorCode(string $errorCode, array $context = []): static
    {
        $config = config("business.errors.{$errorCode}");
        $exception = new static($config['message'], $config['code']);
        $exception->context = $context;
        return $exception;
    }
}

// 使用示例
throw BusinessException::fromErrorCode('Lead.company_already_exists', [
    'company_name' => $dto->companyFullName
]);
```

#### 1.2 系统异常

- **继承**: 继承标准异常类
- **分类**: 按功能分类定义异常
- **详细信息**: 提供足够的调试信息

```php
// ✅ 正确示例
class TransactionException extends RuntimeException
{
    public static function rollbackFailed(string $reason): static
    {
        return new static("事务回滚失败: {$reason}");
    }
}

class QueryOptimizationException extends RuntimeException
{
    public static function indexMissing(string $table, string $column): static
    {
        return new static("表 {$table} 的列 {$column} 缺少索引");
    }
}
```

### 2. 异常处理策略

#### 2.1 Service 层异常处理

- **捕获**: 捕获底层异常并转换为业务异常
- **日志**: 记录异常详细信息
- **重抛**: 重新抛出业务异常

```php
// ✅ 正确示例
public function createLead(LeadCreateDTO $dto): Lead
{
    try {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务逻辑
            return $this->leadRepository->create($dto->toArray());
        });
    } catch (QueryException $e) {
        Log::error('线索创建数据库错误', [
            'error' => $e->getMessage(),
            'dto' => $dto->toArray()
        ]);
        throw BusinessException::fromErrorCode('Lead.create_failed');
    }
}
```

#### 2.2 Controller 层异常处理

- **不捕获**: 让异常向上传播到全局处理器
- **统一处理**: 在 Handler 中统一处理异常响应

```php
// ✅ 正确示例 - Controller 不处理异常
public function store(CreateLeadRequest $request): JsonResponse
{
    $dto = LeadCreateDTO::fromRequest($request);
    $lead = $this->leadService->createLead($dto); // 异常向上传播
    return ApiResponse::success(new LeadResource($lead));
}

// ✅ 正确示例 - Handler 统一处理
public function render($request, Throwable $exception)
{
    if ($exception instanceof BusinessException) {
        return ApiResponse::error($exception->getMessage(), $exception->getCode());
    }
    
    return parent::render($request, $exception);
}
```

## 测试要求

### 1. 测试覆盖率

- **核心业务逻辑**: 覆盖率 >= 80%
- **关键路径**: 覆盖率 >= 95%
- **边界条件**: 必须测试边界情况

### 2. 测试类型

- **单元测试**: 测试单个类或方法
- **集成测试**: 测试多个组件协作
- **功能测试**: 测试完整的业务流程

### 3. 测试命名

- **类命名**: `{ClassName}Test`
- **方法命名**: `test_{method_name}_{scenario}`

```php
// ✅ 正确示例
class LeadServiceTest extends TestCase
{
    public function test_create_lead_with_valid_data(): void { }
    public function test_create_lead_throws_exception_when_company_exists(): void { }
    public function test_find_by_id_returns_null_when_not_found(): void { }
}
```

## 代码格式规范

### 1. PSR-12 标准

- **必须**: 严格遵循 PSR-12 编码标准
- **工具**: 使用 Laravel Pint 自动格式化
- **验证**: 提交前必须通过格式检查

### 2. 额外规范

- **行长度**: 每行不超过 120 字符
- **缩进**: 使用 4 个空格缩进
- **空行**: 合理使用空行分隔逻辑块

### 3. 注释规范

- **单行注释**: 使用 `//`
- **多行注释**: 使用 `/* */`
- **文档注释**: 使用 `/** */`
- **TODO 注释**: 使用 `// TODO: 描述`

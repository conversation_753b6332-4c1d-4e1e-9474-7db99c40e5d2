# MySQL 查询统计日志功能实现计划

-   [x] 1. 修改 ApiLoggingMiddleware 集成查询收集器

    -   通过构造函数注入 DatabaseQueryCollectorInterface 依赖
    -   在请求开始时启动查询收集
    -   在请求结束时获取查询统计并添加到日志数据中
    -   确保在请求完成后重置收集器状态
    -   添加异常处理确保查询收集失败不影响正常请求处理
    -   _需求: 1.1, 1.2, 1.3, 3.2, 3.3_

-   [ ] 2. 配置服务容器绑定

    -   在 AppServiceProvider 中绑定 DatabaseQueryCollectorInterface 到 DatabaseQueryCollector 实现
    -   确保依赖注入正确工作
    -   _需求: 1.1_

-   [ ] 3. 优化 DatabaseQueryCollector 的查询记录格式

    -   确保查询按执行顺序记录（基于 timestamp 字段）
    -   优化 getQueryStatistics 方法返回格式以适配日志输出
    -   确保慢查询摘要按执行时间排序
    -   _需求: 2.1, 2.2, 4.2_

-   [ ] 4. 添加配置开关控制查询统计功能

    -   在数据库配置中添加查询统计启用开关
    -   修改 ApiLoggingMiddleware 根据配置决定是否启用查询统计
    -   确保功能禁用时不影响现有日志格式
    -   _需求: 5.3_

-   [ ] 5. 增强错误处理和日志记录

    -   在 ApiLoggingMiddleware 中添加查询收集异常处理
    -   确保查询统计生成失败时返回默认结构
    -   添加相关错误日志记录，包含 trace_id 用于问题追踪
    -   _需求: 3.2_

-   [x] 6. 编写集成测试验证功能

    -   创建 ApiLoggingMiddleware 的功能测试
    -   测试查询统计正确集成到 API 日志中
    -   测试配置开关的有效性
    -   验证日志格式兼容性和查询执行顺序
    -   _需求: 2.1, 2.2, 5.1, 5.2_


-   [ ] 8. 创建性能测试和监控
    -   编写性能测试验证中间件增加的处理时间
    -   测试大量查询场景下的内存使用
    -   验证最大查询数限制的保护机制
    -   _需求: 3.1, 3.3_

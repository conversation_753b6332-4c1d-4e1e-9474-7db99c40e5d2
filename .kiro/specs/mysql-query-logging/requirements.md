# MySQL查询统计日志功能需求文档

## 介绍

为当前的API链路日志系统添加MySQL查询执行统计功能，在每个API请求的日志中包含该请求期间执行的所有SQL语句及其执行耗时，按执行顺序记录，便于性能分析和问题排查。

## 需求

### 需求1：集成查询统计到API日志

**用户故事：** 作为开发人员，我希望在API请求日志中看到该请求执行的所有SQL语句和耗时，以便快速定位性能问题。

#### 验收标准

1. WHEN API请求开始处理 THEN 系统 SHALL 自动开始收集该请求的数据库查询统计
2. WHEN API请求处理完成 THEN 系统 SHALL 将查询统计信息包含在API日志中
3. WHEN 查询统计信息记录到日志 THEN 系统 SHALL 包含以下信息：
   - 总查询数量
   - 总执行时间
   - 慢查询数量
   - 按执行顺序排列的SQL语句列表
   - 每个SQL语句的执行耗时

### 需求2：SQL语句按执行顺序记录

**用户故事：** 作为开发人员，我希望SQL语句按照实际执行顺序记录，以便理解业务逻辑的执行流程。

#### 验收标准

1. WHEN 记录SQL查询 THEN 系统 SHALL 保持查询的执行顺序
2. WHEN 输出查询统计 THEN 系统 SHALL 按时间戳顺序排列查询记录
3. WHEN 查询包含参数绑定 THEN 系统 SHALL  生成最终的包含真实参数值的sql语句

### 需求3：性能优化和资源控制

**用户故事：** 作为系统管理员，我希望查询统计功能不会显著影响系统性能，并且有合理的资源使用限制。

#### 验收标准

1. WHEN 单个请求的查询数量超过配置限制 THEN 系统 SHALL 停止记录新查询并记录警告
2. WHEN 查询统计功能出现异常 THEN 系统 SHALL 记录错误日志但不影响正常业务流程
3. WHEN API请求完成 THEN 系统 SHALL 自动清理该请求的查询统计数据

### 需求4：可配置的慢查询阈值

**用户故事：** 作为开发人员，我希望能够配置慢查询的判定阈值，以便根据业务需求调整性能监控标准。

#### 验收标准

1. WHEN 查询执行时间超过配置阈值 THEN 系统 SHALL 标记为慢查询
2. WHEN 存在慢查询 THEN 系统 SHALL 在日志中特别标注慢查询信息
3. WHEN 配置慢查询阈值 THEN 系统 SHALL 支持通过环境变量动态调整

### 需求5：日志格式兼容性

**用户故事：** 作为运维人员，我希望新的查询统计信息能够与现有的日志格式兼容，不破坏现有的日志分析工具。

#### 验收标准

1. WHEN 添加查询统计信息 THEN 系统 SHALL 保持现有API日志的基本结构
2. WHEN 输出查询统计 THEN 系统 SHALL 将统计信息作为新字段添加到日志记录中
3. WHEN 查询统计功能禁用 THEN 系统 SHALL 正常记录API日志但不包含查询统计信息
# MySQL查询统计日志功能设计文档

## 概述

本设计文档描述如何将现有的数据库查询收集器集成到API日志中间件中，实现在每个API请求的日志中自动包含MySQL查询执行统计信息。设计基于现有的`DatabaseQueryCollector`服务和`ApiLoggingMiddleware`中间件，通过依赖注入的方式实现松耦合集成。

## 架构

### 整体架构图

```mermaid
graph TD
    A[HTTP Request] --> B[ApiLoggingMiddleware]
    B --> C[DatabaseQueryCollector.startCollection]
    B --> D[Controller/Service Layer]
    D --> E[Database Queries]
    E --> F[DB::listen Event]
    F --> G[DatabaseQueryCollector.recordQuery]
    D --> H[Response]
    H --> I[DatabaseQueryCollector.getQueryStatistics]
    I --> J[Log with Query Stats]
    J --> K[HTTP Response]
```

### 组件关系

- **ApiLoggingMiddleware**: 负责请求生命周期管理和日志记录
- **DatabaseQueryCollector**: 负责查询统计数据收集和处理
- **Laravel DB Events**: 提供查询执行事件监听机制
- **Log Channel**: 输出包含查询统计的结构化日志

## 组件和接口

### 1. ApiLoggingMiddleware 增强

#### 职责
- 在请求开始时启动查询收集
- 在请求结束时获取查询统计并记录到日志
- 确保查询收集器状态正确重置

#### 新增依赖
```php
use App\Contracts\DatabaseQueryCollectorInterface;
```

#### 构造函数注入
```php
public function __construct(
    private DatabaseQueryCollectorInterface $queryCollector
) {}
```

#### 处理流程
1. 请求开始 → 启动查询收集
2. 执行业务逻辑 → 自动收集查询
3. 请求结束 → 获取统计信息
4. 记录日志 → 包含查询统计
5. 清理状态 → 重置收集器

### 2. DatabaseQueryCollector 优化

#### 现有功能保持不变
- `startCollection()`: 开始收集查询
- `stopCollection()`: 停止收集查询  
- `getQueryStatistics()`: 获取统计信息
- `reset()`: 重置状态

#### 查询记录格式
```php
[
    'sql' => string,           // SQL语句
    'bindings' => array,       // 参数绑定
    'execution_time' => float, // 执行时间(ms)
    'is_slow' => bool,         // 是否慢查询
    'timestamp' => float       // 执行时间戳
]
```

#### 统计信息格式
```php
[
    'total_queries' => int,        // 总查询数
    'total_time_ms' => float,      // 总执行时间
    'slow_queries_count' => int,   // 慢查询数量
    'queries' => array,            // 查询详情列表
    'slow_queries_summary' => array // 慢查询摘要
]
```

### 3. 服务容器绑定

#### ServiceProvider 配置
在 `AppServiceProvider` 中绑定接口实现：

```php
$this->app->bind(
    DatabaseQueryCollectorInterface::class,
    DatabaseQueryCollector::class
);
```

## 数据模型

### 日志输出格式

#### 原有日志结构保持不变
```json
{
    "trace_id": "uuid",
    "method": "GET",
    "uri": "/api/users",
    "status": 200,
    "duration_ms": 150.25,
    "ip": "127.0.0.1",
    "user_agent": "...",
    "input": {}
}
```

#### 新增查询统计字段
```json
{
    "trace_id": "uuid",
    "method": "GET", 
    "uri": "/api/users",
    "status": 200,
    "duration_ms": 150.25,
    "ip": "127.0.0.1",
    "user_agent": "...",
    "input": {},
    "database_queries": {
        "total_queries": 3,
        "total_time_ms": 45.67,
        "slow_queries_count": 1,
        "queries": [
            {
                "sql": "SELECT * FROM users WHERE id = ?",
                "bindings": [123],
                "execution_time": 12.34,
                "is_slow": false,
                "timestamp": 1640995200.123
            }
        ],
        "slow_queries_summary": [
            {
                "sql": "SELECT * FROM posts WHERE user_id = ?",
                "time_ms": 156.78
            }
        ]
    }
}
```

### 配置数据模型

#### 数据库配置扩展
现有配置保持不变，支持以下环境变量：

```env
DB_QUERY_LOGGING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=100
DB_MAX_QUERIES_LOGGED=50
```

## 错误处理

### 异常处理策略

#### 1. 查询收集失败
- **场景**: DB::listen 注册失败
- **处理**: 记录警告日志，继续正常请求处理
- **影响**: 该请求不包含查询统计信息

#### 2. 统计生成失败  
- **场景**: getQueryStatistics() 抛出异常
- **处理**: 返回默认统计结构，记录错误日志
- **影响**: 日志包含错误标识但不影响请求

#### 3. 内存限制保护
- **场景**: 查询数量超过配置限制
- **处理**: 停止记录新查询，记录警告
- **影响**: 只记录前N个查询的统计信息

### 错误日志格式
```php
Log::warning('Database query collection failed', [
    'trace_id' => $traceId,
    'error' => $exception->getMessage(),
    'context' => 'api_logging_middleware'
]);
```

## 测试策略

### 单元测试

#### 1. ApiLoggingMiddleware 测试
- 测试查询收集器正确注入
- 测试查询统计正确添加到日志
- 测试异常情况下的降级处理
- 测试收集器状态正确重置

#### 2. DatabaseQueryCollector 集成测试
- 测试在中间件环境下的查询收集
- 测试多个并发请求的隔离性
- 测试配置变更的动态生效

#### 3. 日志格式测试
- 验证日志结构兼容性
- 验证查询统计字段完整性
- 验证敏感信息过滤

### 性能测试

#### 1. 内存使用测试
- 测试大量查询场景下的内存占用
- 验证最大查询数限制的有效性
- 测试长时间运行的内存泄漏

#### 2. 执行时间测试  
- 测量中间件增加的处理时间
- 验证查询收集对数据库性能的影响
- 测试高并发场景下的性能表现

## 部署和配置

### 环境变量配置

#### 生产环境建议
```env
DB_QUERY_LOGGING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=200
DB_MAX_QUERIES_LOGGED=30
```

#### 开发环境建议
```env
DB_QUERY_LOGGING_ENABLED=true  
DB_SLOW_QUERY_THRESHOLD=50
DB_MAX_QUERIES_LOGGED=100
```

### 中间件注册

#### 全局中间件
在 `app/Http/Kernel.php` 中注册：
```php
protected $middleware = [
    // ...
    \App\Http\Middleware\ApiLoggingMiddleware::class,
];
```

#### 路由组中间件
```php
Route::middleware(['api.logging'])->group(function () {
    // API routes
});
```

### 日志通道配置

#### config/logging.php
确保 `api` 通道正确配置：
```php
'channels' => [
    'api' => [
        'driver' => 'daily',
        'path' => storage_path('logs/api.log'),
        'level' => 'info',
        'days' => 14,
    ],
],
```

## 监控和维护

### 关键指标监控

#### 1. 查询统计质量
- 查询收集成功率
- 统计生成失败率
- 内存限制触发频率

#### 2. 性能影响监控
- 中间件处理时间增量
- 内存使用量变化
- 日志文件大小增长

### 故障排查

#### 1. 查询统计缺失
- 检查 `DB_QUERY_LOGGING_ENABLED` 配置
- 查看错误日志中的异常信息
- 验证服务容器绑定配置

#### 2. 性能问题
- 调整 `DB_MAX_QUERIES_LOGGED` 限制
- 检查慢查询阈值设置
- 监控数据库连接池状态

#### 3. 日志格式问题
- 验证日志通道配置
- 检查序列化异常
- 确认敏感数据过滤规则

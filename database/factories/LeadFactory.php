<?php

namespace Database\Factories;

use App\Models\Lead;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Lead 模型工厂
 */
class LeadFactory extends Factory
{
    /**
     * 模型类名
     *
     * @var string
     */
    protected $model = Lead::class;

    /**
     * 定义模型的默认状态
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_full_name' => $this->faker->unique()->company(),
            'company_short_name' => $this->faker->companySuffix(),
            'internal_name' => $this->faker->optional()->word(),
            'region' => $this->faker->numberBetween(1, 7),
            'source' => $this->faker->numberBetween(1, 8),
            'industry' => $this->faker->numberBetween(1, 12),
            'status' => $this->faker->numberBetween(1, 6),
            'stage' => $this->faker->numberBetween(1, 6),
            'address' => $this->faker->optional()->address(),
            'creator_id' => 1, // 默认创建人ID
            'last_followed_at' => $this->faker->optional()->dateTimeBetween('-30 days', 'now'),
            'remark' => $this->faker->optional()->text(200),
        ];
    }

    /**
     * 指定状态的线索
     *
     * @param  int  $status  状态值
     */
    public function withStatus(int $status): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $status,
        ]);
    }

    /**
     * 新建状态的线索
     */
    public function newStatus(): static
    {
        return $this->withStatus(1);
    }

    /**
     * 已联系状态的线索
     */
    public function contacted(): static
    {
        return $this->withStatus(2);
    }

    /**
     * 已确认状态的线索
     */
    public function confirmed(): static
    {
        return $this->withStatus(3);
    }
}

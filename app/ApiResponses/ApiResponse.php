<?php

namespace App\ApiResponses;

use Illuminate\Http\JsonResponse;

/**
 * 统一API响应格式封装
 */
class ApiResponse
{
    /**
     * 成功响应
     *
     * @param  mixed  $data  响应数据
     * @param  string  $message  响应消息
     * @param  int  $code  响应码
     */
    public static function success($data = null, string $message = 'success', int $code = 200): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    /**
     * 失败响应
     *
     * @param  string  $message  错误消息
     * @param  int  $code  错误码
     * @param  mixed  $data  错误数据
     */
    public static function error(string $message = 'error', int $code = 400, $data = null): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    /**
     * 错误响应（使用 errors 字段）
     *
     * @param  string  $message  错误消息
     * @param  int  $code  错误码
     * @param  mixed  $errors  错误详情
     * @param  string|null  $traceId  追踪ID
     */
    public static function errorWithErrors(string $message = 'error', int $code = 400, $errors = null, ?string $traceId = null): JsonResponse
    {
        // 确保 errors 为数组或对象
        if ($errors === null) {
            $errors = [];
        } elseif (! is_array($errors) && ! is_object($errors)) {
            $errors = [];
        }

        // 将 trace_id 添加到 errors 中
        if ($traceId) {
            if (is_array($errors)) {
                $errors['trace_id'] = $traceId;
            } elseif (is_object($errors) && $errors instanceof \stdClass) {
                // 对于 stdClass 对象，直接设置属性
                $errors->trace_id = $traceId;
            }
        }

        // 如果 errors 为空数组，转换为空对象
        if (is_array($errors) && empty($errors)) {
            $errors = new \stdClass;
        }

        return response()->json([
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
        ], $code);
    }

    /**
     * 分页响应
     *
     * @param  mixed  $data  分页数据
     * @param  string  $message  响应消息
     * @param  int  $code  响应码
     */
    public static function paginated($data, string $message = 'success', int $code = 200): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    /**
     * 验证错误响应
     *
     * @param  array<string, mixed>  $errors  验证错误数据
     * @param  string  $message  错误消息
     * @param  string|null  $traceId  追踪ID
     */
    public static function validationError(array $errors, string $message = '数据验证失败', ?string $traceId = null): JsonResponse
    {
        // 将 trace_id 添加到 errors 对象中
        if ($traceId) {
            $errors['trace_id'] = $traceId;
        }

        return response()->json([
            'code' => 422,
            'message' => $message,
            'errors' => $errors,
        ], 422);
    }

    /**
     * 系统错误响应（专门用于系统级错误）
     *
     * @param  string  $message  错误消息
     * @param  int  $code  错误码
     * @param  array<string, mixed>  $errorDetails  错误详情
     * @param  string|null  $traceId  追踪ID
     */
    public static function systemError(
        string $message = '服务器内部错误',
        int $code = 500,
        array $errorDetails = [],
        ?string $traceId = null
    ): JsonResponse {
        // 构建 errors 对象，确保始终为对象类型
        $errors = empty($errorDetails) ? [] : $errorDetails;

        // 将 trace_id 添加到 errors 对象中
        if ($traceId) {
            $errors['trace_id'] = $traceId;
        }

        // 如果 errors 为空，确保返回空对象而不是空数组
        if (empty($errors)) {
            $errors = new \stdClass;
        }

        $response = [
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
        ];

        return response()->json($response, $code);
    }
}

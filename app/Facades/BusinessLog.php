<?php

namespace App\Facades;

use App\Services\BusinessLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Facade;
use Throwable;

/**
 * 业务日志 Facade
 *
 * 提供简化的业务日志记录接口，支持多种日志级别，自动包含 trace_id
 *
 * @method static BusinessLogService setTraceId(string $traceId) 设置当前请求的 trace_id
 * @method static string getTraceId() 获取当前请求的 trace_id
 * @method static void log(array $data, string $level = 'info') 记录指定级别的日志
 * @method static void debug(array $data) 记录 debug 级别日志
 * @method static void info(array $data) 记录 info 级别日志
 * @method static void warning(array $data) 记录 warning 级别日志
 * @method static void error(array $data) 记录 error 级别日志
 * @method static void critical(array $data) 记录 critical 级别日志
 * @method static void logException(string $message, Throwable|null $exception = null, array $additionalData = []) 记录异常日志
 * @method static array extractRequestInfo(Request|null $request = null) 提取请求信息
 *
 * @see \App\Services\BusinessLogService
 *
 * @example
 * // 基础使用 - 记录 info 级别日志
 * BusinessLog::info([
 *     'message' => '用户登录成功',
 *     'module' => 'auth',
 *     'action' => 'login',
 *     'user_id' => 123,
 *     'ip' => '*************'
 * ]);
 * @example
 * // 记录线索操作
 * BusinessLog::info([
 *     'message' => '创建线索',
 *     'module' => 'Lead',
 *     'action' => 'create',
 *     'lead_id' => 456,
 *     'data' => [
 *         'company_name' => '示例公司',
 *         'status' => 1
 *     ]
 * ]);
 * @example
 * // 记录错误日志
 * BusinessLog::error([
 *     'message' => '数据库连接失败',
 *     'module' => 'database',
 *     'error_code' => 'DB_CONNECTION_FAILED',
 *     'details' => '连接超时'
 * ]);
 * @example
 * // 记录异常
 * try {
 *     // 业务逻辑
 * } catch (\Exception $e) {
 *     BusinessLog::logException('业务处理失败', $e, [
 *         'module' => 'Lead',
 *         'action' => 'process',
 *         'lead_id' => 789
 *     ]);
 * }
 * @example
 * // 使用自定义 trace_id
 * BusinessLog::setTraceId('custom-trace-123');
 * BusinessLog::info(['message' => '自定义追踪的操作']);
 */
class BusinessLog extends Facade
{
    /**
     * 获取 Facade 对应的服务容器绑定名称
     */
    protected static function getFacadeAccessor(): string
    {
        return 'business-log';
    }
}

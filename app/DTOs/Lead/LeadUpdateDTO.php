<?php

namespace App\DTOs\Lead;

use App\DTOs\BaseDTO;
use App\Http\Requests\Lead\UpdateLeadRequest;
use App\Models\Lead;

/**
 * 线索更新数据传输对象
 *
 * 封装线索更新所需的数据，支持部分字段更新
 */
class LeadUpdateDTO extends BaseDTO
{
    /**
     * 公司全称
     */
    public readonly ?string $companyFullName;

    /**
     * 公司简称
     */
    public readonly ?string $companyShortName;

    /**
     * 内部统称
     */
    public readonly ?string $internalName;

    /**
     * 所属区域
     */
    public readonly ?int $region;

    /**
     * 线索来源
     */
    public readonly ?int $source;

    /**
     * 所属行业
     */
    public readonly ?int $industry;

    /**
     * 线索状态
     */
    public readonly ?int $status;

    /**
     * 线索阶段
     */
    public readonly ?int $stage;

    /**
     * 详细地址
     */
    public readonly ?string $address;

    /**
     * 创建人用户ID
     */
    public readonly ?int $creatorId;

    /**
     * 最近跟进时间
     */
    public readonly ?string $lastFollowedAt;

    /**
     * 备注
     */
    public readonly ?string $remark;

    /**
     * 构造函数
     *
     * @param  array<string, mixed>  $data  验证后的请求数据
     */
    public function __construct(array $data)
    {
        $this->companyFullName = isset($data['company_full_name']) ? $data['company_full_name'] : null;
        $this->companyShortName = isset($data['company_short_name']) ? $data['company_short_name'] : null;
        $this->internalName = isset($data['internal_name']) ? $this->toString($data['internal_name']) : null;
        $this->region = isset($data['region']) ? $this->toInt($data['region']) : null;
        $this->source = isset($data['source']) ? $this->toInt($data['source']) : null;
        $this->industry = isset($data['industry']) ? $this->toInt($data['industry']) : null;
        $this->status = isset($data['status']) ? $this->toInt($data['status']) : null;
        $this->stage = isset($data['stage']) ? $this->toInt($data['stage']) : null;
        $this->address = isset($data['address']) ? $this->toString($data['address']) : null;
        $this->creatorId = isset($data['creator_id']) ? $this->toInt($data['creator_id']) : null;
        $this->lastFollowedAt = isset($data['last_followed_at']) ? $this->toDateTime($data['last_followed_at']) : null;
        $this->remark = isset($data['remark']) ? $this->toString($data['remark']) : null;
    }

    /**
     * 从 UpdateLeadRequest 创建 DTO 实例
     */
    public static function fromRequest(UpdateLeadRequest $request): self
    {
        return new self($request->validated());
    }

    /**
     * 检查是否有任何字段需要更新
     */
    public function hasUpdates(): bool
    {
        return ! empty($this->toArray());
    }

    /**
     * 转换为数组格式（仅包含非空字段）
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return array_filter([
            'company_full_name' => $this->companyFullName,
            'company_short_name' => $this->companyShortName,
            'internal_name' => $this->internalName,
            'region' => $this->region,
            'source' => $this->source,
            'industry' => $this->industry,
            'status' => $this->status,
            'stage' => $this->stage,
            'address' => $this->address,
            'creator_id' => $this->creatorId,
            'last_followed_at' => $this->lastFollowedAt,
            'remark' => $this->remark,
        ], fn ($value) => $value !== null);
    }

    /**
     * 获取更新的字段列表
     *
     * @return array<string>
     */
    public function getUpdatedFields(): array
    {
        return array_keys($this->toArray());
    }

    /**
     * 检查是否更新了公司名称
     */
    public function isCompanyNameUpdated(): bool
    {
        return $this->companyFullName !== null;
    }

    /**
     * 检查是否更新了状态
     */
    public function isStatusUpdated(): bool
    {
        return $this->status !== null;
    }

    /**
     * 检查是否更新了阶段
     */
    public function isStageUpdated(): bool
    {
        return $this->stage !== null;
    }

    /**
     * 获取状态标签（如果有更新）
     */
    public function getStatusLabel(): ?string
    {
        return $this->status !== null ? (Lead::STATUS_LABELS[$this->status] ?? '未知状态') : null;
    }

    /**
     * 获取阶段标签（如果有更新）
     */
    public function getStageLabel(): ?string
    {
        return $this->stage !== null ? (Lead::STAGE_LABELS[$this->stage] ?? '未知阶段') : null;
    }
}

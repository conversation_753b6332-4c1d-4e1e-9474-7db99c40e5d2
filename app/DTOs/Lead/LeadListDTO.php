<?php

namespace App\DTOs\Lead;

/**
 * 线索列表查询DTO
 */
class LeadListDTO
{
    /**
     * @var int 页码
     */
    public int $page;

    /**
     * @var int 每页数量
     */
    public int $pageSize;

    /**
     * @var string|null 公司名称搜索
     */
    public ?string $companyName;

    /**
     * @var int|null 所属区域
     */
    public ?int $region;

    /**
     * @var int|null 线索来源
     */
    public ?int $source;

    /**
     * @var int|null 所属行业
     */
    public ?int $industry;

    /**
     * @var int|null 线索状态
     */
    public ?int $status;

    /**
     * @var int|null 线索阶段
     */
    public ?int $stage;

    /**
     * @var int|null 创建人ID
     */
    public ?int $creatorId;

    /**
     * @var string|null 排序字段
     */
    public ?string $sortBy;

    /**
     * @var string|null 排序方向
     */
    public ?string $sortDirection;

    /**
     * 构造函数
     *
     * @param  array<string, mixed>  $data  请求数据
     */
    public function __construct(array $data = [])
    {
        $this->page = $data['page'] ?? 1;
        $this->pageSize = $data['page_size'] ?? config('business.system.pagination.default_page_size', 20);
        $this->companyName = $data['company_name'] ?? null;
        $this->region = isset($data['region']) ? (int) $data['region'] : null;
        $this->source = isset($data['source']) ? (int) $data['source'] : null;
        $this->industry = isset($data['industry']) ? (int) $data['industry'] : null;
        $this->status = isset($data['status']) ? (int) $data['status'] : null;
        $this->stage = isset($data['stage']) ? (int) $data['stage'] : null;
        $this->creatorId = isset($data['creator_id']) ? (int) $data['creator_id'] : null;
        $this->sortBy = $data['sort_by'] ?? config('business.system.sorting.default_sort_by', 'created_at');
        $this->sortDirection = $data['sort_direction'] ?? config('business.system.sorting.default_sort_direction', 'desc');
    }

    /**
     * 转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'page' => $this->page,
            'per_page' => $this->pageSize,
            'company_name' => $this->companyName,
            'region' => $this->region,
            'source' => $this->source,
            'industry' => $this->industry,
            'status' => $this->status,
            'stage' => $this->stage,
            'creator_id' => $this->creatorId,
            'sort_by' => $this->sortBy,
            'sort_direction' => $this->sortDirection,
        ];
    }
}

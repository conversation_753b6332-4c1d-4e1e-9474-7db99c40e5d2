<?php

namespace App\DTOs\Lead;

use App\DTOs\BaseDTO;
use App\Http\Requests\Lead\CreateLeadRequest;
use App\Models\Lead;

/**
 * 线索创建数据传输对象
 *
 * 封装线索创建所需的所有数据，提供类型安全和业务逻辑
 */
class LeadCreateDTO extends BaseDTO
{
    /**
     * 公司全称
     */
    public readonly string $companyFullName;

    /**
     * 公司简称
     */
    public readonly string $companyShortName;

    /**
     * 内部统称
     */
    public readonly ?string $internalName;

    /**
     * 所属区域
     */
    public readonly int $region;

    /**
     * 线索来源
     */
    public readonly int $source;

    /**
     * 所属行业
     */
    public readonly int $industry;

    /**
     * 线索状态
     */
    public readonly ?int $status;

    /**
     * 线索阶段
     */
    public readonly ?int $stage;

    /**
     * 详细地址
     */
    public readonly ?string $address;

    /**
     * 创建人用户ID
     */
    public readonly int $creatorId;

    /**
     * 最近跟进时间
     */
    public readonly ?string $lastFollowedAt;

    /**
     * 备注
     */
    public readonly ?string $remark;

    /**
     * 构造函数
     *
     * @param  array<string, mixed>  $data  验证后的请求数据
     */
    public function __construct(array $data)
    {
        $this->companyFullName = $data['company_full_name'];
        $this->companyShortName = $data['company_short_name'];
        $this->internalName = $this->toString($data['internal_name'] ?? null);
        $this->region = (int) $data['region'];
        $this->source = (int) $data['source'];
        $this->industry = (int) $data['industry'];
        $this->status = $this->toInt($data['status'] ?? null) ?? $this->getDefaultStatus();
        $this->stage = $this->toInt($data['stage'] ?? null) ?? $this->getDefaultStage();
        $this->address = $this->toString($data['address'] ?? null);
        $this->creatorId = (int) $data['creator_id'];
        $this->lastFollowedAt = $this->toDateTime($data['last_followed_at'] ?? null);
        $this->remark = $this->toString($data['remark'] ?? null);
    }

    /**
     * 获取默认线索状态
     */
    private function getDefaultStatus(): int
    {
        // 返回第一个可用的状态作为默认值
        $statusKeys = array_keys(Lead::STATUS_LABELS);

        return $statusKeys[0];
    }

    /**
     * 获取默认线索阶段
     */
    private function getDefaultStage(): int
    {
        // 返回第一个可用的阶段作为默认值
        $stageKeys = array_keys(Lead::STAGE_LABELS);

        return $stageKeys[0];
    }

    /**
     * 从 CreateLeadRequest 创建 DTO 实例
     */
    public static function fromRequest(CreateLeadRequest $request): self
    {
        return new self($request->validated());
    }

    /**
     * 转换为数组格式
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'company_full_name' => $this->companyFullName,
            'company_short_name' => $this->companyShortName,
            'internal_name' => $this->internalName,
            'region' => $this->region,
            'source' => $this->source,
            'industry' => $this->industry,
            'status' => $this->status,
            'stage' => $this->stage,
            'address' => $this->address,
            'creator_id' => $this->creatorId,
            'last_followed_at' => $this->lastFollowedAt,
            'remark' => $this->remark,
        ];
    }

    /**
     * 检查是否有跟进时间
     */
    public function hasFollowUpTime(): bool
    {
        return $this->lastFollowedAt !== null;
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabel(): string
    {
        return Lead::STATUS_LABELS[$this->status] ?? '未知状态';
    }

    /**
     * 获取区域标签
     */
    public function getRegionLabel(): string
    {
        return Lead::REGION_LABELS[$this->region] ?? '未知区域';
    }

    /**
     * 获取来源标签
     */
    public function getSourceLabel(): string
    {
        return Lead::SOURCE_LABELS[$this->source] ?? '未知来源';
    }

    /**
     * 获取行业标签
     */
    public function getIndustryLabel(): string
    {
        return Lead::INDUSTRY_LABELS[$this->industry] ?? '未知行业';
    }
}

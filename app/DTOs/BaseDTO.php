<?php

namespace App\DTOs;

use JsonSerializable;

/**
 * DTO 基类
 *
 * 提供通用的数据传输对象功能，包括数据转换、验证和序列化
 */
abstract class BaseDTO implements JsonSerializable
{
    /**
     * 从数组数据创建 DTO 实例
     *
     * @param  array<string, mixed>  $data
     */
    public static function fromArray(array $data): static
    {
        /** @var static */
        return new static($data);
    }

    /**
     * 转换为数组格式
     *
     * @return array<string, mixed>
     */
    abstract public function toArray(): array;

    /**
     * JSON 序列化
     *
     * @return array<string, mixed>
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    /**
     * 获取非空属性数组
     *
     * @return array<string, mixed>
     */
    protected function getNonNullProperties(): array
    {
        $result = [];
        $reflection = new \ReflectionClass($this);

        foreach ($reflection->getProperties(\ReflectionProperty::IS_PUBLIC) as $property) {
            $value = $property->getValue($this);
            if ($value !== null) {
                $result[$this->convertPropertyName($property->getName())] = $value;
            }
        }

        return $result;
    }

    /**
     * 转换属性名为下划线格式
     */
    protected function convertPropertyName(string $propertyName): string
    {
        $converted = preg_replace('/([a-z])([A-Z])/', '$1_$2', $propertyName);

        return strtolower($converted ?? $propertyName);
    }

    /**
     * 安全的整数转换
     */
    protected function toInt(mixed $value): ?int
    {
        if ($value === null || $value === '') {
            return null;
        }

        return is_numeric($value) ? (int) $value : null;
    }

    /**
     * 安全的字符串转换
     */
    protected function toString(mixed $value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        return (string) $value;
    }

    /**
     * 安全的日期时间转换
     */
    protected function toDateTime(mixed $value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        if ($value instanceof \DateTimeInterface) {
            return $value->format('Y-m-d H:i:s');
        }

        try {
            $date = new \DateTime($value);

            return $date->format('Y-m-d H:i:s');
        } catch (\Exception) {
            return null;
        }
    }
}

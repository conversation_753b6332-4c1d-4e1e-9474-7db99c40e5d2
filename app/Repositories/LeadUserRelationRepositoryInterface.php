<?php

namespace App\Repositories;

use App\Models\LeadUserRelation;
use Illuminate\Database\Eloquent\Collection;

/**
 * 线索用户关联仓储接口
 */
interface LeadUserRelationRepositoryInterface
{
    /**
     * 根据线索ID获取用户关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function getRelationsByLeadId(int $leadId): Collection;

    /**
     * 根据用户ID获取线索关联关系
     *
     * @param  int  $userId  用户ID
     */
    public function getRelationsByUserId(int $userId): Collection;

    /**
     * 获取线索的主负责人
     *
     * @param  int  $leadId  线索ID
     */
    public function getPrimaryOwnerByLeadId(int $leadId): ?LeadUserRelation;

    /**
     * 获取线索的协同人员
     *
     * @param  int  $leadId  线索ID
     */
    public function getCollaboratorsByLeadId(int $leadId): Collection;

    /**
     * 创建线索用户关联关系
     *
     * @param  array  $data  关联数据
     */
    public function create(array $data): LeadUserRelation;

    /**
     * 批量创建线索用户关联关系
     *
     * @param  array  $relationsData  关联数据数组
     */
    public function createBatch(array $relationsData): Collection;

    /**
     * 更新关联关系
     *
     * @param  int  $id  关联ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool;

    /**
     * 删除关联关系
     *
     * @param  int  $id  关联ID
     */
    public function delete(int $id): bool;

    /**
     * 删除线索的所有用户关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function deleteByLeadId(int $leadId): bool;

    /**
     * 检查用户是否已关联到线索
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @param  int  $roleType  角色类型
     */
    public function existsRelation(int $leadId, int $userId, int $roleType): bool;

    /**
     * 设置线索的主负责人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     */
    public function setPrimaryOwner(int $leadId, int $userId): bool;

    /**
     * 根据条件查找关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @param  int  $roleType  角色类型
     */
    public function findRelation(int $leadId, int $userId, int $roleType): ?LeadUserRelation;
}

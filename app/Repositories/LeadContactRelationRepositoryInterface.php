<?php

namespace App\Repositories;

use App\Models\LeadContactRelation;
use Illuminate\Database\Eloquent\Collection;

/**
 * 线索联系人关联仓储接口
 */
interface LeadContactRelationRepositoryInterface
{
    /**
     * 根据线索ID获取联系人关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function getRelationsByLeadId(int $leadId): Collection;

    /**
     * 根据联系人ID获取线索关联关系
     *
     * @param  int  $contactId  联系人ID
     */
    public function getRelationsByContactId(int $contactId): Collection;

    /**
     * 创建线索联系人关联关系
     *
     * @param  array  $data  关联数据
     */
    public function create(array $data): LeadContactRelation;

    /**
     * 批量创建线索联系人关联关系
     *
     * @param  array  $relationsData  关联数据数组
     */
    public function createBatch(array $relationsData): Collection;

    /**
     * 删除关联关系
     *
     * @param  int  $id  关联ID
     */
    public function delete(int $id): bool;

    /**
     * 删除线索的所有联系人关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function deleteByLeadId(int $leadId): bool;

    /**
     * 删除联系人的所有线索关联关系
     *
     * @param  int  $contactId  联系人ID
     */
    public function deleteByContactId(int $contactId): bool;

    /**
     * 检查线索和联系人是否已关联
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     */
    public function existsRelation(int $leadId, int $contactId): bool;

    /**
     * 根据线索ID和联系人ID查找关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     */
    public function findRelation(int $leadId, int $contactId): ?LeadContactRelation;

    /**
     * 批量删除关联关系
     *
     * @param  array  $relationIds  关联ID数组
     * @return int 删除成功的数量
     */
    public function deleteBatch(array $relationIds): int;

    /**
     * 同步线索的联系人关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  array  $contactIds  联系人ID数组
     */
    public function syncLeadContacts(int $leadId, array $contactIds): bool;
}

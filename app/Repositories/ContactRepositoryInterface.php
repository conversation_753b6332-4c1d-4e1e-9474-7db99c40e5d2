<?php

namespace App\Repositories;

use App\Models\Contact;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

/**
 * 联系人仓储接口
 */
interface ContactRepositoryInterface
{
    /**
     * 获取联系人分页列表
     *
     * @param  array  $filters  筛选条件
     * @param  int  $perPage  每页数量
     * @param  int  $page  页码
     */
    public function getContactsList(array $filters = [], int $perPage = 15, int $page = 1): LengthAwarePaginator;

    /**
     * 根据ID获取联系人详情
     *
     * @param  int  $id  联系人ID
     */
    public function findById(int $id): ?Contact;

    /**
     * 根据手机号获取联系人
     *
     * @param  string  $mobile  手机号
     */
    public function findByMobile(string $mobile): ?Contact;

    /**
     * 创建联系人
     *
     * @param  array  $data  联系人数据
     */
    public function create(array $data): Contact;

    /**
     * 更新联系人
     *
     * @param  int  $id  联系人ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool;

    /**
     * 删除联系人
     *
     * @param  int  $id  联系人ID
     */
    public function delete(int $id): bool;

    /**
     * 检查手机号是否已存在
     *
     * @param  string  $mobile  手机号
     * @param  int|null  $excludeId  排除的联系人ID
     */
    public function existsByMobile(string $mobile, ?int $excludeId = null): bool;

    /**
     * 批量创建联系人
     *
     * @param  array  $contactsData  联系人数据数组
     */
    public function createBatch(array $contactsData): Collection;

    /**
     * 根据线索ID获取关联的联系人
     *
     * @param  int  $leadId  线索ID
     */
    public function getContactsByLeadId(int $leadId): Collection;

    /**
     * 搜索联系人（按姓名、手机号）
     *
     * @param  string  $keyword  搜索关键词
     * @param  int  $limit  限制数量
     */
    public function searchContacts(string $keyword, int $limit = 10): Collection;
}

<?php

namespace App\Repositories;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\DTOs\Lead\LeadListDTO;
use App\Models\Lead;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

/**
 * 线索仓储实现
 *
 * 集成数据库操作优化组件，提供高性能的数据库操作
 */
class LeadRepository implements LeadRepositoryInterface
{
    /**
     * Lead 模型实例
     */
    protected Lead $model;

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        Lead $model,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        $this->model = $model;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }

    /**
     * 获取线索分页列表（优化版本）
     *
     * @param  LeadListDTO  $dto  查询条件DTO
     */
    public function getLeadsList(LeadListDTO $dto): LengthAwarePaginator
    {
        // 构建基础查询
        $query = $this->model->newQuery();

        // 预加载关联关系，避免 N+1 问题
        $query->with(['creator:id,name', 'contacts:id,name,mobile', 'users:id,name']);

        // 使用增强查询构建器构建复杂查询条件
        $conditions = $this->buildQueryConditions($dto);
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        // 添加动态排序
        $sortRules = $this->buildSortRules($dto);
        $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);

        // 优化查询性能
        $optimizationOptions = [
            'optimize_select' => true,
            'optimize_joins' => true,
        ];
        $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

        // 构建分页查询
        return $this->queryBuilder->buildPaginatedQuery(
            $query,
            $dto->page,
            $dto->pageSize
        );
    }

    /**
     * 构建查询条件数组
     */
    protected function buildQueryConditions(LeadListDTO $dto): array
    {
        $conditions = [];

        // 基础字段条件
        if ($dto->region) {
            $conditions['region'] = $dto->region;
        }

        if ($dto->source) {
            $conditions['source'] = $dto->source;
        }

        if ($dto->industry) {
            $conditions['industry'] = $dto->industry;
        }

        if ($dto->status) {
            $conditions['status'] = $dto->status;
        }

        if ($dto->stage) {
            $conditions['stage'] = $dto->stage;
        }

        if ($dto->creatorId) {
            $conditions['creator_id'] = $dto->creatorId;
        }

        // 搜索条件
        if ($dto->companyName) {
            $conditions['search'] = [
                'keyword' => $dto->companyName,
                'fields' => ['company_full_name', 'company_short_name', 'internal_name'],
                'match_type' => 'like',
            ];
        }

        return $conditions;
    }

    /**
     * 构建排序规则数组
     */
    protected function buildSortRules(LeadListDTO $dto): array
    {
        return [
            [
                'field' => $dto->sortBy ?? 'created_at',
                'direction' => $dto->sortDirection ?? 'desc',
                'nulls' => 'last',
            ],
        ];
    }

    /**
     * 根据ID获取线索详情（优化版本）
     *
     * @param  int  $id  线索ID
     */
    public function findById(int $id): ?Lead
    {
        $query = $this->model->newQuery();
        $query->with(['creator:id,name', 'contacts:id,name,mobile', 'users:id,name']);
        // 使用查询构建器添加条件
        $conditions = ['id' => $id];
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        // 设置查询缓存
        $query = $this->queryBuilder->setCacheForQuery($query, 3600, "lead_detail_{$id}");

        return $query->first();

    }

    /**
     * 创建线索
     *
     * @param  array<string, mixed>  $data  线索数据
     */
    public function create(array $data): Lead
    {
        return $this->model->create($data);
    }

    /**
     * 删除线索（使用事务管理）
     *
     * @param  int  $id  线索ID
     */
    public function delete(int $id): bool
    {
        return $this->transactionManager->executeInTransaction(function () use ($id) {
            $lead = $this->model->find($id);

            if (! $lead) {
                return false;
            }

            $result = $lead->delete();

            if ($result) {
                // 清除相关缓存
                $this->clearRelatedCache($id);

                Log::info('线索删除成功', [
                    'lead_id' => $id,
                    'company_name' => $lead->company_full_name,
                ]);
            }

            return $result ?? false;
        });
    }

    /**
     * 清除相关缓存
     *
     * @param  int|null  $leadId  线索ID
     */
    protected function clearRelatedCache(?int $leadId = null): void
    {
        try {
            $cacheKeys = ['leads_list_*', 'lead_stats_*'];

            if ($leadId) {
                $cacheKeys[] = "lead_detail_{$leadId}";
            }

            foreach ($cacheKeys as $pattern) {
                $this->queryBuilder->clearQueryCache($pattern);
            }

        } catch (Exception $e) {
            Log::warning('清除缓存失败', [
                'lead_id' => $leadId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 检查公司名称是否已存在（优化版本）
     *
     * @param  string  $companyName  公司名称
     */
    public function existsByCompanyName(string $companyName): bool
    {
        try {
            $query = $this->model->newQuery();
            $conditions = ['company_full_name' => $companyName];
            $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

            // 设置短期缓存
            $query = $this->queryBuilder->setCacheForQuery($query, 300, "company_exists_{$companyName}");

            return $query->exists();

        } catch (Exception $e) {
            Log::warning('检查公司名称存在性失败', [
                'company_name' => $companyName,
                'error' => $e->getMessage(),
            ]);

            // 降级处理
            return $this->model->where('company_full_name', $companyName)->exists();
        }
    }

    /**
     * 批量更新线索状态（使用事务管理和死锁重试）
     *
     * @param  array  $ids  线索ID数组
     * @param  int  $status  新状态
     */
    public function batchUpdateStatus(array $ids, int $status): int
    {
        // 验证和清理ID数组
        $validIds = array_filter($ids, fn ($id) => is_numeric($id) && $id > 0);
        $validIds = array_map('intval', $validIds);

        if (empty($validIds)) {
            return 0;
        }

        return $this->transactionManager->executeWithDeadlockRetry(function () use ($validIds, $status) {
            $result = $this->model->whereIn('id', $validIds)->update(['status' => $status]);

            if ($result > 0) {
                // 清除相关缓存
                $this->clearRelatedCache();

                Log::info('批量更新线索状态成功', [
                    'updated_count' => $result,
                    'status' => $status,
                    'ids_count' => count($validIds),
                ]);
            }

            return $result;
        }, 3, 100); // 最多重试3次，延迟100ms
    }

    /**
     * 更新线索（不使用事务管理，由上层Service管理事务）
     *
     * @param  int  $id  线索ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool
    {
        $result = $this->model->where('id', $id)->update($data);

        if ($result > 0) {
            // 清除相关缓存
            $this->clearRelatedCache($id);

            Log::info('线索更新成功', [
                'lead_id' => $id,
                'updated_fields' => array_keys($data),
            ]);
        }

        return $result > 0;
    }

    /**
     * 获取线索统计信息
     *
     * @param  array  $filters  筛选条件
     */
    public function getLeadsStatistics(array $filters = []): array
    {
        try {
            $query = $this->model->newQuery();

            // 应用筛选条件
            if (! empty($filters)) {
                $query = $this->queryBuilder->buildComplexQuery($filters, $query);
            }

            // 构建聚合查询
            $aggregations = [
                'count' => ['field' => '*'],
                'group_count' => ['field' => 'status'],
            ];

            return $this->queryBuilder->buildAggregateQuery($query, $aggregations);

        } catch (Exception $e) {
            Log::error('获取线索统计信息失败', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            return [
                'count' => 0,
                'group_count' => [],
                'error' => $e->getMessage(),
            ];
        }
    }
}

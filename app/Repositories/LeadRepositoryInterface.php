<?php

namespace App\Repositories;

use App\DTOs\Lead\LeadListDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * 线索仓储接口
 */
interface LeadRepositoryInterface
{
    /**
     * 获取线索分页列表
     *
     * @param  LeadListDTO  $dto  查询条件DTO
     */
    public function getLeadsList(LeadListDTO $dto): LengthAwarePaginator;

    /**
     * 根据ID获取线索详情
     *
     * @param  int  $id  线索ID
     * @return mixed
     */
    public function findById(int $id);

    /**
     * 创建线索
     *
     * @param  array  $data  线索数据
     * @return mixed
     */
    public function create(array $data);

    /**
     * 更新线索
     *
     * @param  int  $id  线索ID
     * @param  array  $data  更新数据
     */
    public function update(int $id, array $data): bool;

    /**
     * 删除线索
     *
     * @param  int  $id  线索ID
     */
    public function delete(int $id): bool;

    /**
     * 检查公司名称是否已存在
     *
     * @param  string  $companyName  公司名称
     */
    public function existsByCompanyName(string $companyName): bool;

    /**
     * 批量更新线索状态
     *
     * @param  array<int>  $ids  线索ID数组
     * @param  int  $status  新状态
     * @return int 实际更新的记录数量
     */
    public function batchUpdateStatus(array $ids, int $status): int;

    /**
     * 获取线索统计信息
     *
     * @param  array<string, mixed>  $filters  筛选条件
     * @return array<string, mixed> 统计信息
     */
    public function getLeadsStatistics(array $filters = []): array;
}

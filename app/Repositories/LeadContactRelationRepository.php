<?php

namespace App\Repositories;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\Models\LeadContactRelation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 线索联系人关联仓储实现
 *
 * 集成数据库操作优化组件，提供高性能的数据库操作
 */
class LeadContactRelationRepository implements LeadContactRelationRepositoryInterface
{
    /**
     * LeadContactRelation 模型实例
     */
    protected LeadContactRelation $model;

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        LeadContactRelation $model,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        $this->model = $model;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }

    /**
     * 根据线索ID获取联系人关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function getRelationsByLeadId(int $leadId): Collection
    {
        return $this->model->where('lead_id', $leadId)->get();
    }

    /**
     * 根据联系人ID获取线索关联关系
     *
     * @param  int  $contactId  联系人ID
     */
    public function getRelationsByContactId(int $contactId): Collection
    {
        return $this->model->where('contact_id', $contactId)->get();
    }

    /**
     * 创建线索联系人关联关系
     *
     * @param  array  $data  关联数据
     */
    public function create(array $data): LeadContactRelation
    {
        return $this->model->create($data);
    }

    /**
     * 删除关联关系
     *
     * @param  int  $id  关联ID
     */
    public function delete(int $id): bool
    {
        $relation = $this->model->find($id);
        if ($relation) {
            return $relation->delete() ?? false;
        }

        return false;
    }

    /**
     * 删除线索的所有联系人关联关系
     *
     * @param  int  $leadId  线索ID
     */
    public function deleteByLeadId(int $leadId): bool
    {
        return $this->model->where('lead_id', $leadId)->delete() !== false;
    }

    /**
     * 删除联系人的所有线索关联关系
     *
     * @param  int  $contactId  联系人ID
     */
    public function deleteByContactId(int $contactId): bool
    {
        return $this->model->where('contact_id', $contactId)->delete() !== false;
    }

    /**
     * 检查线索和联系人是否已关联
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     */
    public function existsRelation(int $leadId, int $contactId): bool
    {
        return $this->model->where('lead_id', $leadId)
            ->where('contact_id', $contactId)
            ->exists();
    }

    /**
     * 根据线索ID和联系人ID查找关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     */
    public function findRelation(int $leadId, int $contactId): ?LeadContactRelation
    {
        return $this->model->where('lead_id', $leadId)
            ->where('contact_id', $contactId)
            ->first();
    }

    /**
     * 批量删除关联关系（优化版本）
     *
     * @param  array  $relationIds  关联ID数组
     * @return int 删除成功的数量
     */
    public function deleteBatch(array $relationIds): int
    {
        if (empty($relationIds)) {
            return 0;
        }

        return $this->transactionManager->executeWithDeadlockRetry(function () use ($relationIds) {
            $result = $this->model->whereIn('id', $relationIds)->delete();

            if ($result > 0) {
                Log::info('批量删除线索联系人关联关系成功', [
                    'deleted_count' => $result,
                ]);
            }

            return $result;
        }, 3, 100);
    }

    /**
     * 同步线索的联系人关联关系（使用事务管理和死锁重试）
     *
     * @param  int  $leadId  线索ID
     * @param  array  $contactIds  联系人ID数组
     */
    public function syncLeadContacts(int $leadId, array $contactIds): bool
    {
        return $this->transactionManager->executeWithDeadlockRetry(function () use ($leadId, $contactIds) {
            // 删除现有的关联关系
            $this->deleteByLeadId($leadId);

            // 创建新的关联关系
            if (! empty($contactIds)) {
                $relationsData = [];
                foreach ($contactIds as $contactId) {
                    $relationsData[] = [
                        'lead_id' => $leadId,
                        'contact_id' => $contactId,
                    ];
                }

                // 使用批量创建方法
                $this->createBatch($relationsData);
            }

            // 清除相关缓存
            $this->clearRelatedCache($leadId);

            Log::info('同步线索联系人关联关系成功', [
                'lead_id' => $leadId,
                'contact_count' => count($contactIds),
            ]);

            return true;
        }, 3, 100); // 最多重试3次，延迟100ms
    }

    /**
     * 批量创建关联关系（优化版本）
     *
     * @param  array  $relationsData  关联关系数据数组
     */
    public function createBatch(array $relationsData): Collection
    {
        if (empty($relationsData)) {
            return collect();
        }

        return $this->transactionManager->executeInTransaction(function () use ($relationsData) {
            $relations = collect();

            foreach ($relationsData as $relationData) {
                // 检查关联关系是否已存在，避免重复创建
                if (! $this->existsRelation($relationData['lead_id'], $relationData['contact_id'])) {
                    $relation = $this->create($relationData);
                    $relations->push($relation);
                }
            }

            if ($relations->isNotEmpty()) {
                Log::info('批量创建线索联系人关联关系成功', [
                    'count' => $relations->count(),
                ]);
            }

            return $relations;
        });
    }

    /**
     * 清除相关缓存
     *
     * @param  int|null  $leadId  线索ID
     * @param  int|null  $contactId  联系人ID
     */
    protected function clearRelatedCache(?int $leadId = null, ?int $contactId = null): void
    {
        try {
            $cacheKeys = ['lead_contacts_*', 'contact_leads_*'];

            if ($leadId) {
                $cacheKeys[] = "lead_detail_{$leadId}";
                $cacheKeys[] = "lead_contacts_{$leadId}";
            }

            if ($contactId) {
                $cacheKeys[] = "contact_detail_{$contactId}";
                $cacheKeys[] = "contact_leads_{$contactId}";
            }

            foreach ($cacheKeys as $pattern) {
                $this->queryBuilder->clearQueryCache($pattern);
            }

        } catch (\Exception $e) {
            Log::warning('清除线索联系人关联缓存失败', [
                'lead_id' => $leadId,
                'contact_id' => $contactId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}

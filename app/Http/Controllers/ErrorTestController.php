<?php

namespace App\Http\Controllers;

use App\ApiResponses\ApiResponse;
use App\Exceptions\BusinessException;
use Illuminate\Http\JsonResponse;

/**
 * 错误测试控制器
 *
 * 用于测试各种类型的错误处理
 */
class ErrorTestController extends Controller
{
    /**
     * 测试 PHP Fatal Error
     */
    public function testFatalError(): JsonResponse
    {
        // 调用不存在的函数
        nonExistentFunction();

        return ApiResponse::success('This should not be reached');
    }

    /**
     * 测试 PHP Parse Error
     */
    public function testParseError(): JsonResponse
    {
        // 故意的语法错误
        eval('$invalid syntax here');

        return ApiResponse::success('This should not be reached');
    }

    /**
     * 测试 PHP Warning
     */
    public function testWarning(): JsonResponse
    {
        // 触发 PHP Warning
        $file = fopen('/nonexistent/path/file.txt', 'r');

        return ApiResponse::success('Warning triggered', ['file' => $file]);
    }

    /**
     * 测试 PHP Notice
     */
    public function testNotice(): JsonResponse
    {
        // 触发 PHP Notice
        $undefined = $undefinedVariable;

        return ApiResponse::success('Notice triggered', ['value' => $undefined]);
    }

    /**
     * 测试未捕获异常
     */
    public function testException(): JsonResponse
    {
        throw new \Exception('This is a test exception');
    }

    /**
     * 测试业务异常
     */
    public function testBusinessException(): JsonResponse
    {
        throw new BusinessException('This is a test business exception', 400);
    }

    /**
     * 测试内存溢出
     */
    public function testMemoryError(): JsonResponse
    {
        // 尝试分配大量内存
        $memory = [];
        for ($i = 0; $i < 1000000; $i++) {
            $memory[] = str_repeat('x', 10000);
        }

        return ApiResponse::success('Memory test completed');
    }

    /**
     * 测试除零错误
     */
    public function testDivisionByZero(): JsonResponse
    {
        $result = 10 / 0;

        return ApiResponse::success('Division result', ['result' => $result]);
    }

    /**
     * 测试类型错误
     */
    public function testTypeError(): JsonResponse
    {
        $this->requireString(123);

        return ApiResponse::success('Type test completed');
    }

    /**
     * 测试数组访问错误
     */
    public function testArrayError(): JsonResponse
    {
        $array = ['key' => 'value'];
        $result = $array['nonexistent']['nested'];

        return ApiResponse::success('Array access completed', ['result' => $result]);
    }

    /**
     * 测试递归调用导致的栈溢出
     */
    public function testStackOverflow(): JsonResponse
    {
        return $this->infiniteRecursion();
    }

    /**
     * 测试正常响应（对照组）
     */
    public function testNormal(): JsonResponse
    {
        return ApiResponse::success([
            'timestamp' => now()->toISOString(),
            'message' => 'All systems working normally',
        ], 'Normal response');
    }

    /**
     * 获取错误测试列表
     */
    public function getTestList(): JsonResponse
    {
        $tests = [
            'normal' => 'Normal response test',
            'fatal-error' => 'PHP Fatal Error test',
            'parse-error' => 'PHP Parse Error test',
            'warning' => 'PHP Warning test',
            'notice' => 'PHP Notice test',
            'exception' => 'Uncaught Exception test',
            'business-exception' => 'Business Exception test',
            'memory-error' => 'Memory overflow test',
            'division-by-zero' => 'Division by zero test',
            'type-error' => 'Type Error test',
            'array-error' => 'Array access error test',
            'stack-overflow' => 'Stack overflow test',
        ];

        return ApiResponse::success('Available error tests', $tests);
    }

    /**
     * 需要字符串参数的私有方法
     */
    private function requireString(string $value): void
    {
        // 这个方法期望字符串参数
    }

    /**
     * 无限递归方法
     */
    private function infiniteRecursion(): JsonResponse
    {
        return $this->infiniteRecursion();
    }
}

<?php

namespace App\Http\Controllers;

use App\ApiResponses\ApiResponse;
use App\DTOs\Lead\LeadCreateDTO;
use App\DTOs\Lead\LeadListDTO;
use App\DTOs\Lead\LeadUpdateDTO;
use App\Exceptions\BusinessException;
use App\Facades\BusinessLog;
use App\Http\Requests\Lead\BatchUpdateStatusRequest;
use App\Http\Requests\Lead\CreateLeadRequest;
use App\Http\Requests\Lead\LeadListRequest;
use App\Http\Requests\Lead\ShowLeadRequest;
use App\Http\Requests\Lead\UpdateLeadRequest;
use App\Resources\Lead\LeadCollection;
use App\Resources\Lead\LeadResource;
use App\Services\LeadService;
use Illuminate\Http\JsonResponse;

/**
 * 线索控制器
 */
class LeadController extends Controller
{
    protected LeadService $leadService;

    /**
     * 构造函数
     */
    public function __construct(LeadService $leadService)
    {
        $this->leadService = $leadService;
    }

    /**
     * 线索列表分页接口
     */
    public function list(LeadListRequest $request): JsonResponse
    {
        // 创建查询DTO
        $dto = new LeadListDTO($request->validated());

        // 获取分页数据
        $leads = $this->leadService->getLeadsList($dto);

        // 使用 LeadCollection 处理分页数据格式化
        $data = new LeadCollection($leads);

        return ApiResponse::success($data, '获取线索列表成功');
    }

    /**
     * 获取线索详情
     *
     * @throws BusinessException
     */
    public function show(ShowLeadRequest $request): JsonResponse
    {
        $leadId = $request->validated()['id'];

        $lead = $this->leadService->getLeadById($leadId);

        return ApiResponse::success(new LeadResource($lead), '获取线索详情成功');
    }

    /**
     * 创建线索
     */
    public function store(CreateLeadRequest $request): JsonResponse
    {
        // 创建 DTO 对象
        $dto = LeadCreateDTO::fromRequest($request);

        // 调用服务层创建线索
        $lead = $this->leadService->createLead($dto);

        return ApiResponse::success(new LeadResource($lead), '创建线索成功');
    }

    /**
     * 更新线索
     *
     * @throws BusinessException
     */
    public function update(UpdateLeadRequest $request, int $id): JsonResponse
    {
        // 创建 DTO 对象
        $dto = LeadUpdateDTO::fromRequest($request);

        // 调用服务层更新线索并获取最新数据
        $updatedLead = $this->leadService->updateLead($id, $dto);

        return ApiResponse::success(new LeadResource($updatedLead), '更新线索成功');
    }

    /**
     * 删除线索
     *
     * @throws BusinessException
     */
    public function destroy(ShowLeadRequest $request): JsonResponse
    {
        $leadId = $request->validated()['id'];

        // 记录业务日志
        BusinessLog::info([
            'message' => '删除线索',
            'module' => 'Lead',
            'action' => 'delete',
            'lead_id' => $leadId,
        ]);

        $this->leadService->deleteLead($leadId);

        // 记录删除成功日志
        BusinessLog::info([
            'message' => '线索删除成功',
            'module' => 'Lead',
            'action' => 'delete_success',
            'lead_id' => $leadId,
        ]);

        return ApiResponse::success(null, '删除线索成功');
    }

    /**
     * 批量更新线索状态
     */
    public function batchUpdateStatus(BatchUpdateStatusRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // 记录业务日志
        BusinessLog::info([
            'message' => '批量更新线索状态',
            'module' => 'Lead',
            'action' => 'batch_update_status',
            'lead_ids' => $validated['ids'],
            'new_status' => $validated['status'],
            'total_count' => count($validated['ids']),
        ]);

        $successCount = $this->leadService->batchUpdateStatus($validated['ids'], $validated['status']);

        // 记录批量更新结果
        BusinessLog::info([
            'message' => '批量更新线索状态完成',
            'module' => 'Lead',
            'action' => 'batch_update_status_result',
            'success_count' => $successCount,
            'total_count' => count($validated['ids']),
            'failed_count' => count($validated['ids']) - $successCount,
        ]);

        return ApiResponse::success([
            'success_count' => $successCount,
            'total_count' => count($validated['ids']),
        ], '批量更新状态成功');
    }
}

<?php

namespace App\Http\Controllers;

use App\Contracts\RedisServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Redis 服务测试控制器
 *
 * 用于测试和演示 Redis 服务的各种功能
 */
class RedisTestController extends Controller
{
    /**
     * Redis 服务实例
     */
    protected RedisServiceInterface $redisService;

    /**
     * 构造函数
     */
    public function __construct(RedisServiceInterface $redisService)
    {
        $this->redisService = $redisService;
    }

    /**
     * 测试 Redis 连接
     */
    public function ping(): JsonResponse
    {
        $isConnected = $this->redisService->ping();

        return response()->json([
            'status' => $isConnected ? 'success' : 'failed',
            'message' => $isConnected ? 'Redis 连接正常' : 'Redis 连接失败',
            'connection_info' => $isConnected ? $this->redisService->getConnectionInfo() : null,
        ]);
    }

    /**
     * 测试基础键值操作
     */
    public function basicOperations(Request $request): JsonResponse
    {
        $testKey = 'test:basic:'.time();
        $testValue = [
            'message' => 'Hello Redis!',
            'timestamp' => time(),
            'data' => $request->all(),
        ];

        // 设置键值对
        $setResult = $this->redisService->set($testKey, $testValue, 300); // 5分钟过期

        // 获取键值对
        $getValue = $this->redisService->get($testKey);

        // 检查键是否存在
        $exists = $this->redisService->exists($testKey);

        // 获取过期时间
        $ttl = $this->redisService->ttl($testKey);

        return response()->json([
            'test_key' => $testKey,
            'set_result' => $setResult,
            'get_value' => $getValue,
            'exists' => $exists,
            'ttl' => $ttl,
            'operations' => [
                'set' => $setResult ? 'success' : 'failed',
                'get' => $getValue !== null ? 'success' : 'failed',
                'exists' => $exists ? 'success' : 'failed',
                'ttl' => $ttl > 0 ? 'success' : 'failed',
            ],
        ]);
    }

    /**
     * 测试批量操作
     */
    public function batchOperations(): JsonResponse
    {
        $timestamp = time();
        $testData = [
            "batch:test1:{$timestamp}" => ['name' => '张三', 'age' => 25],
            "batch:test2:{$timestamp}" => ['name' => '李四', 'age' => 30],
            "batch:test3:{$timestamp}" => ['name' => '王五', 'age' => 28],
        ];

        // 批量设置
        $setResult = $this->redisService->setMultiple($testData, 300);

        // 批量获取
        $keys = array_keys($testData);
        $getResult = $this->redisService->getMultiple($keys);

        // 批量删除
        $deleteResult = $this->redisService->deleteMultiple($keys);

        return response()->json([
            'batch_set' => $setResult,
            'batch_get' => $getResult,
            'batch_delete' => $deleteResult,
            'operations' => [
                'set_multiple' => $setResult ? 'success' : 'failed',
                'get_multiple' => count($getResult) === count($keys) ? 'success' : 'failed',
                'delete_multiple' => $deleteResult === count($keys) ? 'success' : 'failed',
            ],
        ]);
    }

    /**
     * 测试计数器操作
     */
    public function counterOperations(): JsonResponse
    {
        $counterKey = 'test:counter:'.time();

        // 递增操作
        $increment1 = $this->redisService->increment($counterKey);
        $increment5 = $this->redisService->increment($counterKey, 5);

        // 递减操作
        $decrement2 = $this->redisService->decrement($counterKey, 2);

        // 最终值
        $finalValue = $this->redisService->get($counterKey);

        // 清理测试数据
        $this->redisService->delete($counterKey);

        return response()->json([
            'counter_key' => $counterKey,
            'increment_1' => $increment1,
            'increment_5' => $increment5,
            'decrement_2' => $decrement2,
            'final_value' => $finalValue,
            'operations' => [
                'increment' => $increment1 === 1 ? 'success' : 'failed',
                'increment_by' => $increment5 === 6 ? 'success' : 'failed',
                'decrement_by' => $decrement2 === 4 ? 'success' : 'failed',
                'final_check' => $finalValue == 4 ? 'success' : 'failed',
            ],
        ]);
    }

    /**
     * 测试模式匹配
     */
    public function patternMatching(): JsonResponse
    {
        $timestamp = time();
        $testKeys = [
            "pattern:user:{$timestamp}:1" => 'user1',
            "pattern:user:{$timestamp}:2" => 'user2',
            "pattern:order:{$timestamp}:1" => 'order1',
            "pattern:order:{$timestamp}:2" => 'order2',
        ];

        // 设置测试数据
        foreach ($testKeys as $key => $value) {
            $this->redisService->set($key, $value, 300);
        }

        // 模式匹配查找
        $userKeys = $this->redisService->keys("pattern:user:{$timestamp}:*");
        $orderKeys = $this->redisService->keys("pattern:order:{$timestamp}:*");
        $allPatternKeys = $this->redisService->keys("pattern:*:{$timestamp}:*");

        // 清理测试数据
        $this->redisService->deleteMultiple(array_keys($testKeys));

        return response()->json([
            'test_keys' => array_keys($testKeys),
            'user_keys' => $userKeys,
            'order_keys' => $orderKeys,
            'all_pattern_keys' => $allPatternKeys,
            'operations' => [
                'user_pattern' => count($userKeys) === 2 ? 'success' : 'failed',
                'order_pattern' => count($orderKeys) === 2 ? 'success' : 'failed',
                'all_pattern' => count($allPatternKeys) === 4 ? 'success' : 'failed',
            ],
        ]);
    }

    /**
     * 综合功能测试
     */
    public function comprehensiveTest(): JsonResponse
    {
        $results = [];

        // 测试连接
        $results['connection'] = $this->redisService->ping();

        // 测试基础操作
        $testKey = 'comprehensive:test:'.time();
        $testValue = ['test' => 'comprehensive', 'time' => time()];

        $results['set'] = $this->redisService->set($testKey, $testValue, 300);
        $results['get'] = $this->redisService->get($testKey) !== null;
        $results['exists'] = $this->redisService->exists($testKey);
        $results['expire'] = $this->redisService->expire($testKey, 600);
        $results['ttl'] = $this->redisService->ttl($testKey) > 0;

        // 测试计数器
        $counterKey = 'comprehensive:counter:'.time();
        $results['increment'] = $this->redisService->increment($counterKey) === 1;
        $results['decrement'] = $this->redisService->decrement($counterKey) === 0;

        // 测试批量操作
        $batchData = [
            'comprehensive:batch1:'.time() => 'value1',
            'comprehensive:batch2:'.time() => 'value2',
        ];
        $results['batch_set'] = $this->redisService->setMultiple($batchData, 300);
        $results['batch_get'] = count($this->redisService->getMultiple(array_keys($batchData))) === 2;

        // 清理测试数据
        $this->redisService->delete($testKey);
        $this->redisService->delete($counterKey);
        $this->redisService->deleteMultiple(array_keys($batchData));

        // 计算成功率
        $totalTests = count($results);
        $successfulTests = count(array_filter($results));
        $successRate = $totalTests > 0 ? ($successfulTests / $totalTests) * 100 : 0;

        return response()->json([
            'summary' => [
                'total_tests' => $totalTests,
                'successful_tests' => $successfulTests,
                'success_rate' => round($successRate, 2).'%',
                'status' => $successRate >= 90 ? 'excellent' : ($successRate >= 70 ? 'good' : 'needs_attention'),
            ],
            'detailed_results' => $results,
            'connection_info' => $this->redisService->getConnectionInfo(),
        ]);
    }
}

<?php

namespace App\Http\Controllers;

use App\Services\LogHealthMonitorService;
use App\Services\ResilientLoggerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 日志健康检查控制器
 */
class LogHealthController extends Controller
{
    private LogHealthMonitorService $healthMonitor;

    private ResilientLoggerService $resilientLogger;

    public function __construct(
        LogHealthMonitorService $healthMonitor,
        ResilientLoggerService $resilientLogger
    ) {
        $this->healthMonitor = $healthMonitor;
        $this->resilientLogger = $resilientLogger;
    }

    /**
     * 获取日志系统健康状态
     */
    public function health(): JsonResponse
    {
        try {
            $healthStatus = $this->healthMonitor->performHealthCheck();

            $httpStatus = match ($healthStatus['overall_status']) {
                'healthy' => 200,
                'degraded' => 200, // 降级但仍可用
                'unhealthy' => 503, // 服务不可用
                default => 500
            };

            return response()->json($healthStatus, $httpStatus);
        } catch (\Exception $e) {
            return response()->json([
                'overall_status' => 'error',
                'message' => 'Health check failed',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * 获取容错日志服务状态
     */
    public function resilientStatus(): JsonResponse
    {
        try {
            $status = $this->resilientLogger->getHealthStatus();

            return response()->json([
                'status' => 'ok',
                'resilient_logger' => $status,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get resilient logger status',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * 获取健康历史
     */
    public function healthHistory(Request $request): JsonResponse
    {
        try {
            $hours = $request->get('hours', 24);
            $hours = max(1, min(168, (int) $hours)); // 限制在1-168小时之间

            $history = $this->healthMonitor->getHealthHistory($hours);

            return response()->json($history);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get health history',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * 手动触发健康检查
     */
    public function triggerHealthCheck(): JsonResponse
    {
        try {
            $result = $this->healthMonitor->performHealthCheck();

            return response()->json([
                'status' => 'completed',
                'message' => 'Health check triggered successfully',
                'result' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to trigger health check',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * 重置熔断器
     */
    public function resetCircuitBreaker(): JsonResponse
    {
        try {
            // 清除熔断器相关的缓存
            cache()->forget('api_logging_circuit_breaker');
            cache()->forget('api_logging_failure_count');
            cache()->forget('api_logging_last_failure_time');

            return response()->json([
                'status' => 'success',
                'message' => 'Circuit breaker reset successfully',
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to reset circuit breaker',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * 测试日志记录功能
     */
    public function testLogging(Request $request): JsonResponse
    {
        try {
            $testData = [
                'test_id' => uniqid(),
                'timestamp' => now()->toISOString(),
                'test_type' => 'manual_test',
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip(),
            ];

            $async = $request->boolean('async', false);
            $success = $this->resilientLogger->logApiRequest($testData, $async);

            return response()->json([
                'status' => $success ? 'success' : 'failed',
                'message' => $success
                    ? 'Test log recorded successfully'
                    : 'Test log recording failed',
                'test_data' => $testData,
                'async' => $async,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Test logging failed',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }
}

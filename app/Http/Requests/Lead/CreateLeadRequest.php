<?php

namespace App\Http\Requests\Lead;

use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 创建线索请求验证
 */
class CreateLeadRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_full_name' => 'required|string|max:255|unique:crm_lead,company_full_name',
            'company_short_name' => 'required|string|max:100',
            'internal_name' => 'nullable|string|max:100',
            'region' => ['required', 'integer', Rule::in(array_keys(Lead::REGION_LABELS))],
            'source' => ['required', 'integer', Rule::in(array_keys(Lead::SOURCE_LABELS))],
            'industry' => ['required', 'integer', Rule::in(array_keys(Lead::INDUSTRY_LABELS))],
            'status' => ['nullable', 'integer', Rule::in(array_keys(Lead::STATUS_LABELS))],
            'stage' => ['nullable', 'integer', Rule::in(array_keys(Lead::STAGE_LABELS))],
            'address' => 'nullable|string|max:255',
            'creator_id' => 'required|integer|exists:users,id',
            'last_followed_at' => 'nullable|date',
            'remark' => 'nullable|string|max:1000',
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'company_full_name' => '公司全称',
            'company_short_name' => '公司简称',
            'internal_name' => '内部统称',
            'region' => '所属区域',
            'source' => '线索来源',
            'industry' => '所属行业',
            'status' => '线索状态',
            'stage' => '线索阶段',
            'address' => '详细地址',
            'creator_id' => '创建人ID',
            'last_followed_at' => '最近跟进时间',
            'remark' => '备注',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'company_full_name.required' => '公司全称不能为空',
            'company_full_name.string' => '公司全称必须是字符串',
            'company_full_name.max' => '公司全称长度不能超过255个字符',
            'company_full_name.unique' => '该公司已存在线索记录',
            'company_short_name.required' => '公司简称不能为空',
            'company_short_name.string' => '公司简称必须是字符串',
            'company_short_name.max' => '公司简称长度不能超过100个字符',
            'internal_name.string' => '内部统称必须是字符串',
            'internal_name.max' => '内部统称长度不能超过100个字符',
            'region.required' => '所属区域不能为空',
            'region.integer' => '所属区域必须是整数',
            'region.in' => '所属区域值无效',
            'source.required' => '线索来源不能为空',
            'source.integer' => '线索来源必须是整数',
            'source.in' => '线索来源值无效',
            'industry.required' => '所属行业不能为空',
            'industry.integer' => '所属行业必须是整数',
            'industry.in' => '所属行业值无效',
            'status.integer' => '线索状态必须是整数',
            'status.in' => '线索状态值无效',
            'stage.integer' => '线索阶段必须是整数',
            'stage.in' => '线索阶段值无效',
            'address.string' => '详细地址必须是字符串',
            'address.max' => '详细地址长度不能超过255个字符',
            'creator_id.required' => '创建人ID不能为空',
            'creator_id.integer' => '创建人ID必须是整数',
            'creator_id.exists' => '创建人不存在',
            'last_followed_at.date' => '最近跟进时间格式无效',
            'remark.string' => '备注必须是字符串',
            'remark.max' => '备注长度不能超过1000个字符',
        ];
    }
}

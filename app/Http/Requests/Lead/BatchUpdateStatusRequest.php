<?php

namespace App\Http\Requests\Lead;

use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 批量更新线索状态请求验证
 */
class BatchUpdateStatusRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1|max:100',
            'ids.*' => 'required|integer|min:1',
            'status' => ['required', 'integer', Rule::in(array_keys(Lead::STATUS_LABELS))],
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'ids' => '线索ID列表',
            'ids.*' => '线索ID',
            'status' => '线索状态',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'ids.required' => '线索ID列表不能为空',
            'ids.array' => '线索ID列表必须是数组',
            'ids.min' => '线索ID列表不能为空',
            'ids.max' => '批量操作不能超过100条记录',
            'ids.*.required' => '线索ID不能为空',
            'ids.*.integer' => '线索ID必须是整数',
            'ids.*.min' => '线索ID必须大于0',
            'status.required' => '线索状态不能为空',
            'status.integer' => '线索状态必须是整数',
            'status.in' => '无效的状态值',
        ];
    }
}

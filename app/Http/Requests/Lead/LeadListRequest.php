<?php

namespace App\Http\Requests\Lead;

use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * 线索列表请求验证
 */
class LeadListRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'company_name' => 'nullable|string|max:255',
            'region' => ['nullable', 'integer', Rule::in(array_keys(Lead::REGION_LABELS))],
            'source' => ['nullable', 'integer', Rule::in(array_keys(Lead::SOURCE_LABELS))],
            'industry' => ['nullable', 'integer', Rule::in(array_keys(Lead::INDUSTRY_LABELS))],
            'status' => ['nullable', 'integer', Rule::in(array_keys(Lead::STATUS_LABELS))],
            'stage' => ['nullable', 'integer', Rule::in(array_keys(Lead::STAGE_LABELS))],
            'creator_id' => 'nullable|integer|exists:users,id',
            'sort_by' => 'string|in:id,company_full_name,company_short_name,internal_name,region,source,industry,status,stage,creator_id,last_followed_at,created_at,updated_at',
            'sort_direction' => 'string|in:asc,desc',
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'page' => '页码',
            'per_page' => '每页数量',
            'company_name' => '公司名称',
            'region' => '所属区域',
            'source' => '线索来源',
            'industry' => '所属行业',
            'status' => '线索状态',
            'stage' => '线索阶段',
            'creator_id' => '创建人ID',
            'sort_by' => '排序字段',
            'sort_direction' => '排序方向',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'page.integer' => '页码必须是整数',
            'page.min' => '页码必须大于0',
            'per_page.integer' => '每页数量必须是整数',
            'per_page.min' => '每页数量必须大于0',
            'per_page.max' => '每页数量不能超过100',
            'company_name.string' => '公司名称必须是字符串',
            'company_name.max' => '公司名称长度不能超过255个字符',
            'creator_id.integer' => '创建人ID必须是整数',
            'creator_id.exists' => '创建人不存在',
            'sort_by.string' => '排序字段必须是字符串',
            'sort_by.in' => '排序字段值无效',
            'sort_direction.string' => '排序方向必须是字符串',
            'sort_direction.in' => '排序方向值无效，只能是asc或desc',
        ];
    }
}

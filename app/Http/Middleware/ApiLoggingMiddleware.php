<?php

namespace App\Http\Middleware;

use App\Contracts\DatabaseQueryCollectorInterface;
use App\Services\ResilientLoggerService;
use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApiLoggingMiddleware
{
    /**
     * 数据库查询收集器
     */
    private DatabaseQueryCollectorInterface $queryCollector;

    /**
     * 容错日志服务
     */
    private ResilientLoggerService $resilientLogger;

    /**
     * 构造函数
     */
    public function __construct(
        DatabaseQueryCollectorInterface $queryCollector,
        ResilientLoggerService $resilientLogger
    ) {
        $this->queryCollector = $queryCollector;
        $this->resilientLogger = $resilientLogger;
    }

    /**
     * 处理API请求并记录日志
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 检查是否应该记录此请求的日志
        if (! $this->shouldLogRequest($request)) {
            return $next($request);
        }

        // 1. 链路追踪 ID（优先取请求头 X-Request-ID，否则新生成）
        $traceId = $request->header('X-Request-ID') ?? Str::uuid()->toString();

        // 2. 将 trace_id 注入响应头，便于前端/其他服务追踪
        $request->headers->set('X-Request-ID', $traceId);

        // 3. 检查是否启用查询统计功能
        $queryLoggingEnabled = $this->isQueryLoggingEnabled();

        // 4. 启动数据库查询收集（如果启用）
        if ($queryLoggingEnabled) {
            $this->startQueryCollection($traceId);
        }

        // 5. 记录请求开始时间
        $start = microtime(true);

        try {
            // 6. 继续处理请求
            $response = $next($request);

            // 7. 记录响应结束，计算耗时（毫秒）
            $duration = round((microtime(true) - $start) * 1000, 2);

            // 8. 获取查询统计信息（如果启用）
            $queryStatistics = [];
            if ($queryLoggingEnabled) {
                $queryStatistics = $this->getQueryStatistics($traceId);
            }

            // 9. 构建日志数据
            $logData = [
                'trace_id' => $traceId,
                'method' => $request->method(),
                'uri' => $request->getPathInfo(),
                'status' => $response->getStatusCode(),
                'duration_ms' => $duration,
                'ip' => $request->ip(),
                'user_agent' => $this->sanitizeUserAgent($request->header('User-Agent')),
                'user_id' => $this->getUserId($request), // todo
                'memory_usage' => $this->getMemoryUsage(),
                'input_size' => strlen(json_encode($request->all()) ?: '{}').' bytes',
                'input' => $this->prepareInputData($request),
                'timestamp' => now()->toISOString(),
            ];

            // 10. 添加查询统计信息到日志（如果启用）
            if ($queryLoggingEnabled) {
                try {
                    // 即使查询统计为空或有错误，也要包含在日志中以保持格式一致性
                    $logData['database_queries'] = $queryStatistics;
                } catch (\Exception $e) {
                    Log::error('Failed to add query statistics to log data', [
                        'trace_id' => $traceId,
                        'error' => $e->getMessage(),
                        'exception_class' => get_class($e),
                        'context' => 'api_logging_middleware',
                    ]);
                    // 确保即使出错也有默认结构
                    $logData['database_queries'] = $this->getDefaultQueryStatistics();
                }
            }

            // 11. 记录日志（使用容错服务）
            $asyncLogging = config('logging.api_logging.async', false);
            $this->resilientLogger->logApiRequest($logData, $asyncLogging);

            return $response;
        } catch (\Exception $e) {
            // 记录请求处理异常
            Log::error('API request processing failed', [
                'trace_id' => $traceId,
                'method' => $request->method(),
                'uri' => $request->getPathInfo(),
                'error' => $e->getMessage(),
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'context' => 'api_logging_middleware',
            ]);

            // 确保异常情况下也能重置收集器状态
            if ($queryLoggingEnabled) {
                $this->resetQueryCollector($traceId);
            }
            throw $e;
        } finally {
            // 12. 重置查询收集器状态（如果启用）
            if ($queryLoggingEnabled) {
                $this->resetQueryCollector($traceId);
            }
        }
    }

    /**
     * 检查是否应该记录此请求的日志
     *
     * @param  \Illuminate\Http\Request  $request
     */
    private function shouldLogRequest($request): bool
    {
        // 检查全局日志开关
        if (! config('logging.api_logging.enabled', true)) {
            return false;
        }

        // 检查是否在排除路径列表中
        $excludedPaths = config('logging.api_logging.excluded_paths', []);
        $currentPath = $request->getPathInfo();

        foreach ($excludedPaths as $excludedPath) {
            if (fnmatch($excludedPath, $currentPath)) {
                return false;
            }
        }

        // 检查是否在排除方法列表中
        $excludedMethods = config('logging.api_logging.excluded_methods', []);
        if (in_array(strtoupper($request->method()), array_map('strtoupper', $excludedMethods))) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否启用查询统计功能
     */
    private function isQueryLoggingEnabled(): bool
    {
        try {
            return config('database.query_logging.enabled', true);
        } catch (\Exception $e) {
            Log::warning('Failed to read query logging configuration, using default value', [
                'error' => $e->getMessage(),
                'context' => 'api_logging_middleware',
            ]);

            return true; // 默认启用
        }
    }

    /**
     * 启动数据库查询收集
     *
     * @param  string  $traceId  链路追踪ID
     */
    private function startQueryCollection(string $traceId): void
    {
        try {
            $this->queryCollector->startCollection();
        } catch (\Exception $e) {
            Log::error('Failed to start database query collection', [
                'trace_id' => $traceId,
                'error' => $e->getMessage(),
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'context' => 'api_logging_middleware',
            ]);
        }
    }

    /**
     * 获取查询统计信息
     *
     * @param  string  $traceId  链路追踪ID
     * @return array 查询统计信息，失败时返回默认结构
     */
    private function getQueryStatistics(string $traceId): array
    {
        try {
            $statistics = $this->queryCollector->getQueryStatistics();

            // 验证返回的统计信息结构是否完整
            if (! $this->isValidQueryStatistics($statistics)) {
                Log::warning('Invalid query statistics structure returned', [
                    'trace_id' => $traceId,
                    'statistics' => $statistics,
                    'context' => 'api_logging_middleware',
                ]);

                return $this->getDefaultQueryStatistics();
            }

            return $statistics;
        } catch (\Exception $e) {
            Log::error('Failed to get database query statistics', [
                'trace_id' => $traceId,
                'error' => $e->getMessage(),
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'context' => 'api_logging_middleware',
            ]);

            return $this->getDefaultQueryStatistics();
        }
    }

    /**
     * 重置查询收集器状态
     *
     * @param  string  $traceId  链路追踪ID
     */
    private function resetQueryCollector(string $traceId): void
    {
        try {
            $this->queryCollector->reset();
        } catch (\Exception $e) {
            Log::error('Failed to reset database query collector', [
                'trace_id' => $traceId,
                'error' => $e->getMessage(),
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'context' => 'api_logging_middleware',
            ]);
        }
    }

    /**
     * 验证查询统计信息结构是否有效
     *
     * @param  array  $statistics  查询统计信息
     */
    private function isValidQueryStatistics(array $statistics): bool
    {
        $requiredKeys = [
            'total_queries',
            'total_time_ms',
            'slow_queries_count',
            'queries',
            'slow_queries_summary',
        ];

        foreach ($requiredKeys as $key) {
            if (! array_key_exists($key, $statistics)) {
                return false;
            }
        }

        // 验证数据类型
        return is_int($statistics['total_queries']) &&
               is_numeric($statistics['total_time_ms']) &&
               is_int($statistics['slow_queries_count']) &&
               is_array($statistics['queries']) &&
               is_array($statistics['slow_queries_summary']);
    }

    /**
     * 获取默认的查询统计信息结构
     */
    private function getDefaultQueryStatistics(): array
    {
        return [
            'total_queries' => 0,
            'total_time_ms' => 0.0,
            'slow_queries_count' => 0,
            'queries' => [],
            'slow_queries_summary' => [],
            'error' => 'Query statistics unavailable',
        ];
    }

    /**
     * 过滤敏感数据
     *
     * @param  array  $data  原始数据
     * @return array 过滤后的数据
     */
    private function filterSensitiveData(array $data): array
    {
        $sensitiveFields = config('logging.sensitive_fields', [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'authorization',
            'x-api-key',
            'access_token',
            'refresh_token',
            'private_key',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'id_card',
        ]);

        return $this->recursiveFilter($data, $sensitiveFields);
    }

    /**
     * 递归过滤敏感字段
     *
     * @param  array  $data  数据数组
     * @param  array  $sensitiveFields  敏感字段列表
     * @return array 过滤后的数据
     */
    private function recursiveFilter(array $data, array $sensitiveFields): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->recursiveFilter($value, $sensitiveFields);
            } elseif (in_array(strtolower($key), array_map('strtolower', $sensitiveFields))) {
                $data[$key] = '[FILTERED]';
            }
        }

        return $data;
    }

    /**
     * 准备输入数据用于日志记录
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|string
     */
    private function prepareInputData($request)
    {
        $inputData = $request->all();
        $filteredData = $this->filterSensitiveData($inputData);

        // 检查数据大小限制
        $maxSize = config('logging.max_input_size', 10240);
        $jsonData = json_encode($filteredData) ?: '{}';

        if (strlen($jsonData) > $maxSize) {
            return [
                'message' => 'Input data too large for logging',
                'size_bytes' => strlen($jsonData),
                'max_size_bytes' => $maxSize,
                'truncated' => true,
            ];
        }

        return $filteredData;
    }

    /**
     * 获取用户ID
     *
     * @param  \Illuminate\Http\Request  $request
     */
    private function getUserId($request): ?int
    {
        try {
            return $request->user()?->id;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取内存使用情况
     */
    private function getMemoryUsage(): array
    {
        return [
            'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2).'M',
            'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2).'M',
        ];
    }

    /**
     * 清理用户代理字符串
     */
    private function sanitizeUserAgent(?string $userAgent): ?string
    {
        if (! $userAgent) {
            return null;
        }

        // 限制长度，避免过长的用户代理字符串
        $maxLength = config('logging.max_user_agent_length', 500);

        return strlen($userAgent) > $maxLength
            ? substr($userAgent, 0, $maxLength).'...'
            : $userAgent;
    }
}

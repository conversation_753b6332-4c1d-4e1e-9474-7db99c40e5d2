<?php

namespace App\Http\Middleware;

use App\Services\GlobalErrorHandlerService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * 全局错误处理中间件
 *
 * 注册全局错误处理器并设置错误上下文信息
 */
class GlobalErrorHandlerMiddleware
{
    /**
     * 处理传入的请求
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 注册全局错误处理器
        GlobalErrorHandlerService::register();
        // 设置错误上下文信息
        $this->setErrorContext($request);
        try {
            $response = $next($request);
            // 请求完成后清理上下文
            $this->cleanupErrorContext();

            return $response;

        } catch (\Throwable $e) {
            // 在异常发生时也要清理上下文
            $this->cleanupErrorContext();
            throw $e;
        }
    }

    /**
     * 设置错误上下文信息
     */
    private function setErrorContext(Request $request): void
    {
        $traceId = $request->header('X-Request-ID') ?? Str::uuid()->toString();
        $context = [
            'trace_id' => $traceId,
            'request_method' => $request->method(),
            'request_uri' => $request->getRequestUri(),
            'request_url' => $request->fullUrl(),
            'user_agent' => $request->header('User-Agent'),
            'ip_address' => $request->ip(),
            'user_id' => $this->getUserId($request),
            //            'session_id' => $request->session()?->getId(),  // todo:新增登录功能后完善
            'timestamp' => now()->toISOString(),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ];
        // 添加请求参数（过滤敏感信息）
        $inputData = $request->all();
        $context['request_data'] = $this->filterSensitiveData($inputData);
        GlobalErrorHandlerService::setErrorContext($context);

        // 将 trace_id 添加到请求头，便于后续中间件使用
        $request->headers->set('X-Request-ID', $traceId);
    }

    /**
     * 获取用户ID
     */
    private function getUserId(Request $request): ?int
    {
        try {
            return $request->user()?->id;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 过滤敏感数据
     */
    private function filterSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'authorization',
            'x-api-key',
            'access_token',
            'refresh_token',
            'private_key',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'id_card',
        ];

        return $this->recursiveFilter($data, $sensitiveFields);
    }

    /**
     * 递归过滤敏感字段
     */
    private function recursiveFilter(array $data, array $sensitiveFields): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->recursiveFilter($value, $sensitiveFields);
            } elseif (in_array(strtolower($key), array_map('strtolower', $sensitiveFields))) {
                $data[$key] = '[FILTERED]';
            }
        }

        return $data;
    }

    /**
     * 清理错误上下文
     */
    private function cleanupErrorContext(): void
    {
        // 在请求结束时清理上下文，避免内存泄漏
        GlobalErrorHandlerService::clearErrorContext();
    }
}

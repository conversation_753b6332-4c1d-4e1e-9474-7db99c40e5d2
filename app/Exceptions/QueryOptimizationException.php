<?php

namespace App\Exceptions;

use Exception;

/**
 * 查询优化异常类
 *
 * 用于处理查询构建和优化过程中的各种异常情况
 */
class QueryOptimizationException extends Exception
{
    /**
     * 无效查询条件异常代码
     */
    public const INVALID_CONDITIONS = 2001;

    /**
     * 不支持的查询操作异常代码
     */
    public const UNSUPPORTED_OPERATION = 2002;

    /**
     * 查询优化失败异常代码
     */
    public const OPTIMIZATION_FAILED = 2003;

    /**
     * 字段验证失败异常代码
     */
    public const FIELD_VALIDATION_FAILED = 2004;

    /**
     * 查询超时异常代码
     */
    public const QUERY_TIMEOUT = 2005;

    /**
     * 查询复杂度过高异常代码
     */
    public const QUERY_TOO_COMPLEX = 2006;

    /**
     * 查询上下文信息
     *
     * @var array<string, mixed>
     */
    protected array $context;

    /**
     * 构造函数
     *
     * @param  string  $message  异常消息
     * @param  int  $code  异常代码
     * @param  array<string, mixed>  $context  查询上下文信息
     * @param  \Throwable|null  $previous  前一个异常
     */
    public function __construct(
        string $message = '',
        int $code = 0,
        array $context = [],
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * 获取查询上下文信息
     *
     * @return array<string, mixed>
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * 设置查询上下文信息
     *
     * @param  array<string, mixed>  $context
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    /**
     * 创建无效查询条件异常
     *
     * @param  array<string, mixed>  $conditions  无效的查询条件
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function invalidConditions(array $conditions, array $context = []): static
    {
        return new static(
            '查询条件无效或格式错误',
            self::INVALID_CONDITIONS,
            array_merge($context, ['invalid_conditions' => $conditions])
        );
    }

    /**
     * 创建不支持的查询操作异常
     *
     * @param  string  $operation  不支持的操作
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function unsupportedOperation(string $operation, array $context = []): static
    {
        return new static(
            "不支持的查询操作：{$operation}",
            self::UNSUPPORTED_OPERATION,
            array_merge($context, ['operation' => $operation])
        );
    }

    /**
     * 创建查询优化失败异常
     *
     * @param  string  $reason  优化失败原因
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function optimizationFailed(string $reason, array $context = []): static
    {
        return new static(
            "查询优化失败：{$reason}",
            self::OPTIMIZATION_FAILED,
            array_merge($context, ['optimization_reason' => $reason])
        );
    }

    /**
     * 创建字段验证失败异常
     *
     * @param  array<string>  $invalidFields  无效字段列表
     * @param  array<string>  $allowedFields  允许的字段列表
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function fieldValidationFailed(
        array $invalidFields,
        array $allowedFields = [],
        array $context = []
    ): static {
        $message = '字段验证失败，无效字段：'.implode(', ', $invalidFields);
        if (! empty($allowedFields)) {
            $message .= '。允许的字段：'.implode(', ', $allowedFields);
        }

        return new static(
            $message,
            self::FIELD_VALIDATION_FAILED,
            array_merge($context, [
                'invalid_fields' => $invalidFields,
                'allowed_fields' => $allowedFields,
            ])
        );
    }

    /**
     * 创建查询超时异常
     *
     * @param  int  $timeout  超时时间
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function queryTimeout(int $timeout, array $context = []): static
    {
        return new static(
            "查询执行超时，超时时间：{$timeout}秒",
            self::QUERY_TIMEOUT,
            array_merge($context, ['timeout' => $timeout])
        );
    }

    /**
     * 创建查询复杂度过高异常
     *
     * @param  int  $complexity  查询复杂度分数
     * @param  int  $maxComplexity  最大允许复杂度
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function queryTooComplex(int $complexity, int $maxComplexity, array $context = []): static
    {
        return new static(
            "查询复杂度过高：{$complexity}，最大允许：{$maxComplexity}",
            self::QUERY_TOO_COMPLEX,
            array_merge($context, [
                'complexity' => $complexity,
                'max_complexity' => $maxComplexity,
            ])
        );
    }

    /**
     * 判断是否为字段验证失败异常
     */
    public function isFieldValidationError(): bool
    {
        return $this->code === self::FIELD_VALIDATION_FAILED;
    }

    /**
     * 判断是否为查询超时异常
     */
    public function isQueryTimeout(): bool
    {
        return $this->code === self::QUERY_TIMEOUT;
    }

    /**
     * 判断是否为查询复杂度过高异常
     */
    public function isQueryTooComplex(): bool
    {
        return $this->code === self::QUERY_TOO_COMPLEX;
    }

    /**
     * 获取异常的详细信息数组
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->context,
            'trace' => $this->getTraceAsString(),
        ];
    }
}

<?php

namespace App\Exceptions;

use Exception;

/**
 * 事务异常类
 *
 * 用于处理事务管理过程中的各种异常情况
 */
class TransactionException extends Exception
{
    /**
     * 事务超时异常代码
     */
    public const TIMEOUT = 1001;

    /**
     * 死锁异常代码
     */
    public const DEADLOCK = 1002;

    /**
     * 保存点异常代码
     */
    public const SAVEPOINT_ERROR = 1003;

    /**
     * 隔离级别异常代码
     */
    public const ISOLATION_LEVEL_ERROR = 1004;

    /**
     * 嵌套事务异常代码
     */
    public const NESTED_TRANSACTION_ERROR = 1005;

    /**
     * 事务回滚异常代码
     */
    public const ROLLBACK_ERROR = 1006;

    /**
     * 事务提交异常代码
     */
    public const COMMIT_ERROR = 1007;

    /**
     * 事务上下文信息
     *
     * @var array<string, mixed>
     */
    protected array $context;

    /**
     * 构造函数
     *
     * @param  string  $message  异常消息
     * @param  int  $code  异常代码
     * @param  array<string, mixed>  $context  事务上下文信息
     * @param  \Throwable|null  $previous  前一个异常
     */
    public function __construct(
        string $message = '',
        int $code = 0,
        array $context = [],
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * 获取事务上下文信息
     *
     * @return array<string, mixed>
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * 设置事务上下文信息
     *
     * @param  array<string, mixed>  $context
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    /**
     * 创建事务超时异常
     *
     * @param  int  $timeout  超时时间
     * @param  array<string, mixed>  $context  上下文信息
     * @return static
     */
    public static function timeout(int $timeout, array $context = []): static
    {
        return new static(
            "事务执行超时，超时时间：{$timeout}秒",
            self::TIMEOUT,
            array_merge($context, ['timeout' => $timeout])
        );
    }

    /**
     * 创建死锁异常
     *
     * @param  string  $details  死锁详情
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function deadlock(string $details = '', array $context = []): static
    {
        $message = '检测到数据库死锁';
        if ($details) {
            $message .= "：{$details}";
        }

        return new static(
            $message,
            self::DEADLOCK,
            array_merge($context, ['deadlock_details' => $details])
        );
    }

    /**
     * 创建保存点异常
     *
     * @param  string  $savepointName  保存点名称
     * @param  string  $operation  操作类型
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function savepointError(string $savepointName, string $operation, array $context = []): static
    {
        return new static(
            "保存点操作失败：{$operation} - {$savepointName}",
            self::SAVEPOINT_ERROR,
            array_merge($context, [
                'savepoint_name' => $savepointName,
                'operation' => $operation,
            ])
        );
    }

    /**
     * 创建隔离级别异常
     *
     * @param  string  $level  隔离级别
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function isolationLevelError(string $level, array $context = []): static
    {
        return new static(
            "设置事务隔离级别失败：{$level}",
            self::ISOLATION_LEVEL_ERROR,
            array_merge($context, ['isolation_level' => $level])
        );
    }

    /**
     * 创建嵌套事务异常
     *
     * @param  string  $details  异常详情
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function nestedTransactionError(string $details, array $context = []): static
    {
        return new static(
            "嵌套事务操作失败：{$details}",
            self::NESTED_TRANSACTION_ERROR,
            array_merge($context, ['details' => $details])
        );
    }

    /**
     * 创建事务回滚异常
     *
     * @param  string  $reason  回滚原因
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function rollbackError(string $reason, array $context = []): static
    {
        return new static(
            "事务回滚失败：{$reason}",
            self::ROLLBACK_ERROR,
            array_merge($context, ['rollback_reason' => $reason])
        );
    }

    /**
     * 创建事务提交异常
     *
     * @param  string  $reason  提交失败原因
     * @param  array<string, mixed>  $context  上下文信息
     */
    public static function commitError(string $reason, array $context = []): static
    {
        return new static(
            "事务提交失败：{$reason}",
            self::COMMIT_ERROR,
            array_merge($context, ['commit_reason' => $reason])
        );
    }

    /**
     * 判断是否为死锁异常
     */
    public function isDeadlock(): bool
    {
        return $this->code === self::DEADLOCK;
    }

    /**
     * 判断是否为超时异常
     */
    public function isTimeout(): bool
    {
        return $this->code === self::TIMEOUT;
    }

    /**
     * 判断是否为保存点异常
     */
    public function isSavepointError(): bool
    {
        return $this->code === self::SAVEPOINT_ERROR;
    }

    /**
     * 获取异常的详细信息数组
     *
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->context,
            'trace' => $this->getTraceAsString(),
        ];
    }
}

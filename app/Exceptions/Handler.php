<?php

namespace App\Exceptions;

use App\ApiResponses\ApiResponse;
use App\Services\GlobalErrorHandlerService;
use ErrorException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * 不上报的异常类型
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        // 可以添加不需要上报的异常类型
    ];

    /**
     * 不记录的敏感输入字段
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * 注册应用程序的异常处理回调
     */
    public function register(): void
    {
        // 0. PHP 错误异常 (转换为异常的 PHP 错误)
        $this->renderable(function (ErrorException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                $this->logPhpError($e, $request);

                $errorDetails = [];
                if (config('app.debug')) {
                    $errorDetails = [
                        'error_type' => 'PHP_ERROR',
                        'severity' => $e->getSeverity(),
                        'severity_name' => $this->getSeverityName($e->getSeverity()),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'message' => $e->getMessage(),
                    ];
                }

                return ApiResponse::systemError(
                    '服务器内部错误',
                    500,
                    $errorDetails,
                    $request->header('X-Request-ID')
                );
            }
        });

        // 1. 验证失败 (422)
        $this->renderable(function (ValidationException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                return ApiResponse::validationError(
                    $e->errors(),
                    '数据验证失败',
                    $request->header('X-Request-ID')
                );
            }
        });

        // 2. 模型未找到 (404)
        $this->renderable(function (ModelNotFoundException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                return ApiResponse::systemError(
                    '您访问的资源不存在',
                    404,
                    [],
                    $request->header('X-Request-ID')
                );
            }
        });

        // 3. 认证异常 (401)
        $this->renderable(function (AuthenticationException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                return ApiResponse::systemError(
                    '未授权，请先登录',
                    401,
                    [],
                    $request->header('X-Request-ID')
                );
            }
        });

        // 4. 授权异常 (403)
        $this->renderable(function (AuthorizationException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                $message = $e->getMessage() ?: '您没有权限执行此操作';

                return ApiResponse::systemError(
                    $message,
                    403,
                    [],
                    $request->header('X-Request-ID')
                );
            }
        });

        // 5. 自定义业务异常 (状态码自定义)
        $this->renderable(function (BusinessException $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                // 业务警告日志
                Log::warning('业务逻辑异常', [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage(),
                    'url' => $request->fullUrl(),
                    'user_id' => auth()->id(),
                    'trace_id' => $request->header('X-Request-ID'),
                ]);

                return ApiResponse::systemError(
                    $e->getMessage(),
                    $e->getCode(),
                    [],
                    $request->header('X-Request-ID')
                );
            }
        });

        // 6. 兜底其他异常 (500)
        $this->renderable(function (Throwable $e, Request $request) {
            if ($request->wantsJson() || $request->is('api/*')) {
                // 记录到错误日志
                $this->logSystemError($e, $request);

                $errorDetails = [];
                if (config('app.debug')) {
                    $errorDetails = [
                        'exception_class' => get_class($e),
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTrace(),
                    ];
                }

                return ApiResponse::systemError(
                    '服务器内部错误',
                    500,
                    $errorDetails,
                    $request->header('X-Request-ID')
                );
            }
        });
    }

    /**
     * 记录 PHP 错误日志
     */
    private function logPhpError(ErrorException $exception, Request $request): void
    {
        try {
            $errorData = [
                'type' => 'PHP_ERROR_EXCEPTION',
                'severity' => $exception->getSeverity(),
                'severity_name' => $this->getSeverityName($exception->getSeverity()),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'trace_id' => $request->header('X-Request-ID'),
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
                'context' => GlobalErrorHandlerService::getErrorContext(),
            ];

            Log::channel('error')->error('PHP Error Exception', $errorData);

        } catch (\Exception $e) {
            // 日志记录失败时使用备用方案
            error_log('Failed to log PHP error: '.$exception->getMessage());
        }
    }

    /**
     * 记录系统错误日志
     */
    private function logSystemError(Throwable $exception, Request $request): void
    {
        try {
            $errorData = [
                'type' => 'SYSTEM_EXCEPTION',
                'exception_class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'trace_id' => $request->header('X-Request-ID'),
                'user_agent' => $request->header('User-Agent'),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString(),
                'context' => GlobalErrorHandlerService::getErrorContext(),
            ];

            Log::channel('error')->error('System Exception', $errorData);

        } catch (\Exception $e) {
            // 日志记录失败时使用备用方案
            error_log('Failed to log system error: '.$exception->getMessage());
        }
    }

    /**
     * 获取错误级别名称
     */
    private function getSeverityName(int $severity): string
    {
        $severityNames = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED',
        ];

        return $severityNames[$severity] ?? 'UNKNOWN_ERROR';
    }
}

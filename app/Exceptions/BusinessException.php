<?php

namespace App\Exceptions;

use Exception;
use InvalidArgumentException;

/**
 * 业务逻辑异常类
 *
 * 用于处理业务逻辑中的预期错误情况，可携带HTTP状态码
 * 支持通过错误码字符串创建异常，提供更灵活的错误处理机制
 */
class BusinessException extends Exception
{
    /**
     * 构造业务异常
     *
     * @param  string  $message  异常消息
     * @param  int  $code  HTTP状态码，默认为400 (Bad Request)
     */
    public function __construct(string $message = '', int $code = 400)
    {
        parent::__construct($message, $code);
    }

    /**
     * 通过错误码创建业务异常
     *
     * @param  string  $errorCode  错误码，格式为 'module.error_type'（如：'Lead.not_found'）
     * @param  array<string, mixed>  $params  参数数组，用于替换消息中的占位符
     * @return static
     *
     * @throws InvalidArgumentException 当错误码不存在时抛出
     */
    public static function fromErrorCode(string $errorCode, array $params = []): static
    {
        $errorConfig = config("errors.{$errorCode}");

        if (! $errorConfig) {
            throw new InvalidArgumentException("错误码 '{$errorCode}' 不存在");
        }

        $message = $errorConfig['message'];

        // 替换消息中的参数占位符
        if (! empty($params)) {
            foreach ($params as $key => $value) {
                $message = str_replace(":{$key}", $value, $message);
            }
        }

        return new static($message, $errorConfig['code']);
    }

    /**
     * 检查错误码是否存在
     *
     * @param  string  $errorCode  错误码字符串（如：'Lead.not_found'）
     */
    public static function hasErrorCode(string $errorCode): bool
    {
        return config("errors.{$errorCode}") !== null;
    }

    /**
     * 获取所有可用的错误码
     *
     * @return array<int, string> 错误码列表
     */
    public static function getAllErrorCodes(): array
    {
        $errors = config('errors', []);
        $errorCodes = [];

        foreach ($errors as $module => $moduleErrors) {
            foreach ($moduleErrors as $errorType => $config) {
                $errorCodes[] = "{$module}.{$errorType}";
            }
        }

        return $errorCodes;
    }
}

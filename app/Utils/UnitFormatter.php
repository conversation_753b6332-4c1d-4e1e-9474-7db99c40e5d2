<?php

declare(strict_types=1);

namespace App\Utils;

/**
 * 数值单位格式化工具类
 *
 * 提供内存和时间单位的智能格式化功能，自动选择合适的单位并保持适当的精度。
 * 主要用于日志记录和性能监控中的数值格式化。
 */
class UnitFormatter
{
    /**
     * 内存单位定义（字节为基准）
     */
    private const MEMORY_UNITS = [
        'B' => 1,
        'KB' => 1024,
        'MB' => 1024 * 1024,
        'GB' => 1024 * 1024 * 1024,
    ];

    /**
     * 时间单位定义（秒为基准）
     */
    private const TIME_UNITS = [
        'μs' => 0.000001,
        'ms' => 0.001,
        's' => 1,
        'min' => 60,
        'h' => 3600,
    ];

    /**
     * 格式化内存大小
     *
     * 自动根据数值大小选择合适的单位（B、KB、MB、GB），
     * 保留2位小数并去除无意义的尾随零。
     *
     * @param  int  $bytes  内存字节数
     * @return string 格式化后的内存大小字符串
     *
     * @example
     * UnitFormatter::formatMemory(1024) => "1 KB"
     * UnitFormatter::formatMemory(1536000) => "1.46 MB"
     * UnitFormatter::formatMemory(2147483648) => "2 GB"
     */
    public static function formatMemory(int $bytes): string
    {
        if ($bytes < 0) {
            return '0 B';
        }

        if ($bytes === 0) {
            return '0 B';
        }

        // 确定最合适的单位
        $selectedUnit = 'B';
        $selectedSize = 1;

        foreach (self::MEMORY_UNITS as $unit => $size) {
            if ($bytes >= $size) {
                $selectedUnit = $unit;
                $selectedSize = $size;
            }
        }

        $value = $bytes / $selectedSize;

        // 根据单位和数值大小调整精度
        $precision = match ($selectedUnit) {
            'B' => 0,
            'KB' => $value >= 100 ? 0 : ($value >= 10 ? 1 : 2),
            'MB', 'GB' => $value >= 100 ? 1 : 2,
        };

        $formatted = number_format($value, $precision, '.', '');

        // 只去除小数点后的尾随零
        if (str_contains($formatted, '.')) {
            $formatted = rtrim($formatted, '0');
            $formatted = rtrim($formatted, '.');
        }

        return $formatted.' '.$selectedUnit;
    }

    /**
     * 格式化时间长度
     *
     * 根据数值大小自动选择合适的单位（μs、ms、s、min、h），
     * 对于较大的时间值，支持复合格式（如 "1 min 5.5 s"）。
     *
     * @param  float  $seconds  时间秒数
     * @param  bool  $useCompoundFormat  是否使用复合格式（如 "1 min 5.5 s"）
     * @return string 格式化后的时间字符串
     *
     * @example
     * UnitFormatter::formatTime(0.001234) => "1.23 ms"
     * UnitFormatter::formatTime(65.5) => "1 min 5.5 s"
     * UnitFormatter::formatTime(65.5, false) => "65.5 s"
     */
    public static function formatTime(float $seconds, bool $useCompoundFormat = true): string
    {
        if ($seconds < 0) {
            return '0 s';
        }

        if ($seconds === 0.0) {
            return '0 s';
        }

        // 对于大于1分钟的时间，使用复合格式
        if ($useCompoundFormat && $seconds >= 60) {
            return self::formatCompoundTime($seconds);
        }

        // 确定最合适的单位
        $selectedUnit = 'μs';
        $selectedSize = self::TIME_UNITS['μs'];

        // 对于非复合格式，优先使用秒作为基准单位
        if (! $useCompoundFormat && $seconds >= 1) {
            $selectedUnit = 's';
            $selectedSize = self::TIME_UNITS['s'];
        } else {
            // 按从大到小的顺序检查单位
            $unitsDescending = [
                'h' => self::TIME_UNITS['h'],
                'min' => self::TIME_UNITS['min'],
                's' => self::TIME_UNITS['s'],
                'ms' => self::TIME_UNITS['ms'],
                'μs' => self::TIME_UNITS['μs'],
            ];

            foreach ($unitsDescending as $unit => $size) {
                if ($seconds >= $size) {
                    $selectedUnit = $unit;
                    $selectedSize = $size;
                    break;
                }
            }
        }

        $value = $seconds / $selectedSize;

        // 根据单位和数值大小调整精度
        $precision = self::getTimePrecision($selectedUnit, $value);

        $formatted = number_format($value, $precision, '.', '');

        // 只去除小数点后的尾随零
        if (str_contains($formatted, '.')) {
            $formatted = rtrim($formatted, '0');
            $formatted = rtrim($formatted, '.');
        }

        return $formatted.' '.$selectedUnit;
    }

    /**
     * 格式化复合时间（如 "1 min 5.5 s"）
     *
     * @param  float  $seconds  总秒数
     * @return string 复合格式的时间字符串
     */
    private static function formatCompoundTime(float $seconds): string
    {
        $parts = [];

        // 小时
        if ($seconds >= 3600) {
            $hours = (int) floor($seconds / 3600);
            $parts[] = $hours.' h';
            $seconds = fmod($seconds, 3600);
        }

        // 分钟
        if ($seconds >= 60) {
            $minutes = (int) floor($seconds / 60);
            $parts[] = $minutes.' min';
            $seconds = fmod($seconds, 60);
        }

        // 秒（保留适当的小数位）
        if ($seconds > 0) {
            if ($seconds >= 1) {
                // 对于复合格式中的秒，保持2位小数精度以保证准确性
                $precision = 2;
                $formatted = number_format($seconds, $precision, '.', '');

                // 只去除小数点后的尾随零
                if (str_contains($formatted, '.')) {
                    $formatted = rtrim($formatted, '0');
                    $formatted = rtrim($formatted, '.');
                }

                $parts[] = $formatted.' s';
            } else {
                // 小于1秒的情况，转换为毫秒
                $milliseconds = $seconds * 1000;
                $precision = $milliseconds >= 10 ? 1 : 2;
                $formatted = number_format($milliseconds, $precision, '.', '');

                // 只去除小数点后的尾随零
                if (str_contains($formatted, '.')) {
                    $formatted = rtrim($formatted, '0');
                    $formatted = rtrim($formatted, '.');
                }

                $parts[] = $formatted.' ms';
            }
        }

        return implode(' ', $parts);
    }

    /**
     * 根据时间单位和数值大小确定精度
     *
     * @param  string  $unit  时间单位
     * @param  float  $value  数值
     * @return int 小数位数
     */
    private static function getTimePrecision(string $unit, float $value): int
    {
        return match ($unit) {
            'μs' => $value >= 100 ? 0 : ($value >= 10 ? 1 : 2),
            'ms' => $value >= 100 ? 0 : ($value >= 10 ? 1 : 2),
            's' => 2, // 秒单位始终保持2位小数精度
            'min' => $value >= 10 ? 1 : 2,
            'h' => 2,
            default => 2,
        };
    }

    /**
     * 格式化数据传输速率
     *
     * @param  int  $bytesPerSecond  每秒字节数
     * @return string 格式化后的速率字符串
     *
     * @example
     * UnitFormatter::formatDataRate(1048576) => "1 MB/s"
     */
    public static function formatDataRate(int $bytesPerSecond): string
    {
        $memoryPart = self::formatMemory($bytesPerSecond);

        return $memoryPart.'/s';
    }

    /**
     * 格式化百分比
     *
     * @param  float  $percentage  百分比数值（0-100）
     * @param  int  $precision  小数位数
     * @return string 格式化后的百分比字符串
     *
     * @example
     * UnitFormatter::formatPercentage(85.6789) => "85.68%"
     */
    public static function formatPercentage(float $percentage, int $precision = 2): string
    {
        $formatted = number_format($percentage, $precision, '.', '');

        // 只去除小数点后的尾随零
        if (str_contains($formatted, '.')) {
            $formatted = rtrim($formatted, '0');
            $formatted = rtrim($formatted, '.');
        }

        return $formatted.'%';
    }

    /**
     * 格式化数量（添加千分位分隔符）
     *
     * @param  int  $count  数量
     * @return string 格式化后的数量字符串
     *
     * @example
     * UnitFormatter::formatCount(1234567) => "1,234,567"
     */
    public static function formatCount(int $count): string
    {
        return number_format($count, 0, '.', ',');
    }
}

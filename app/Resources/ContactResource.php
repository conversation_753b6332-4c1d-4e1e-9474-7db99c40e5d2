<?php

namespace App\Resources;

use Illuminate\Http\Request;

/**
 * 联系人资源类
 *
 * @property-read int $id
 * @property-read string $name
 * @property-read string|null $gender
 * @property-read int|null $age
 * @property-read string|null $mobile
 * @property-read string|null $telephone
 * @property-read string|null $email
 * @property-read string|null $wx
 * @property-read string|null $department
 * @property-read string|null $position
 * @property-read string|null $remark
 * @property-read \Illuminate\Support\Carbon $created_at
 * @property-read \Illuminate\Support\Carbon $updated_at
 */
class ContactResource extends BaseResource
{
    /**
     * 将资源转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'gender' => $this->gender,
            'age' => $this->age,
            'mobile' => $this->mobile,
            'telephone' => $this->telephone,
            'email' => $this->email,
            'wx' => $this->wx,
            'department' => $this->department,
            'position' => $this->position,
            'remark' => $this->remark,
            'created_at' => $this->formatCreatedAt(),
            'updated_at' => $this->formatUpdatedAt(),
        ];
    }
}

<?php

namespace App\Resources;

use Illuminate\Http\Request;

/**
 * 用户资源类
 *
 * @property-read int $id
 * @property-read string $name
 * @property-read string $email
 * @property-read \Illuminate\Support\Carbon $created_at
 * @property-read \Illuminate\Support\Carbon $updated_at
 */
class UserResource extends BaseResource
{
    /**
     * 将资源转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role_type' => $this->whenPivot('role_type'),
            'is_primary' => $this->whenPivot('is_primary'),
            'created_at' => $this->formatCreatedAt(),
            'updated_at' => $this->formatUpdatedAt(),
        ];
    }
}

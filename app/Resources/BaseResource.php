<?php

namespace App\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 基础资源类
 *
 * 提供通用的资源处理方法
 */
abstract class BaseResource extends JsonResource
{
    /**
     * 格式化时间戳
     */
    protected function formatTimestamp(?Carbon $timestamp, string $format = 'Y-m-d H:i:s'): ?string
    {
        return $timestamp?->format($format);
    }

    /**
     * 安全地格式化时间戳，处理可能的 null 值
     *
     * @param  mixed  $timestamp
     */
    protected function safeFormatTimestamp($timestamp, string $format = 'Y-m-d H:i:s'): ?string
    {
        if ($timestamp === null || $timestamp === '') {
            return null;
        }

        if ($timestamp instanceof Carbon) {
            try {
                return $timestamp->format($format);
            } catch (\Exception $e) {
                return null;
            }
        }

        // 尝试转换为 Carbon 实例
        try {
            $carbon = Carbon::parse($timestamp);

            return $carbon->format($format);
        } catch (\Exception $e) {
            // 如果转换失败，返回 null
            return null;
        }
    }

    /**
     * 格式化创建时间
     */
    protected function formatCreatedAt(): ?string
    {
        return $this->safeFormatTimestamp($this->created_at);
    }

    /**
     * 格式化更新时间
     */
    protected function formatUpdatedAt(): ?string
    {
        return $this->safeFormatTimestamp($this->updated_at);
    }

    /**
     * 格式化删除时间
     */
    protected function formatDeletedAt(): ?string
    {
        return $this->safeFormatTimestamp($this->deleted_at);
    }
}

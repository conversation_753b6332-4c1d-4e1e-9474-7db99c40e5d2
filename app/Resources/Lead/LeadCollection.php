<?php

namespace App\Resources\Lead;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * 线索集合资源类
 */
class LeadCollection extends ResourceCollection
{
    /**
     * 资源类型
     *
     * @var string
     */
    public $collects = LeadResource::class;

    /**
     * 将资源集合转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'list' => $this->collection,
            'pagination' => $this->getPaginationData(),
        ];
    }

    /**
     * 获取分页数据
     *
     * @return array<string, mixed>
     */
    protected function getPaginationData(): array
    {
        $paginator = $this->resource;

        // 确保是分页器实例
        if (! $paginator instanceof LengthAwarePaginator) {
            return [];
        }

        return [
            'current_page' => $paginator->currentPage(),
            'page_size' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
            'has_more_pages' => $paginator->hasMorePages(),
        ];
    }
}

<?php

namespace App\Resources\Lead;

use App\Resources\BaseResource;
use App\Resources\ContactResource;
use App\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

/**
 * 线索资源类
 *
 * @property-read int $id
 * @property-read string $company_full_name
 * @property-read string $company_short_name
 * @property-read string|null $internal_name
 * @property-read int $region
 * @property-read string $region_label
 * @property-read int $source
 * @property-read string $source_label
 * @property-read int $industry
 * @property-read string $industry_label
 * @property-read int $status
 * @property-read string $status_label
 * @property-read int $stage
 * @property-read string $stage_label
 * @property-read string|null $address
 * @property-read mixed $creator
 * @property-read string|null $remark
 * @property-read Carbon|null $last_followed_at
 * @property-read Carbon $created_at
 * @property-read Carbon $updated_at
 */
class LeadResource extends BaseResource
{
    /**
     * 将资源转换为数组
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'company_full_name' => $this->company_full_name,
            'company_short_name' => $this->company_short_name,
            'internal_name' => $this->internal_name,
            'region' => [
                'value' => $this->region,
                'label' => $this->region_label,
            ],
            'source' => [
                'value' => $this->source,
                'label' => $this->source_label,
            ],
            'industry' => [
                'value' => $this->industry,
                'label' => $this->industry_label,
            ],
            'status' => [
                'value' => $this->status,
                'label' => $this->status_label,
            ],
            'stage' => [
                'value' => $this->stage,
                'label' => $this->stage_label,
            ],
            'address' => $this->address,
            'creator' => $this->whenLoaded('creator', fn () => [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
            ]),
            'contacts' => ContactResource::collection($this->whenLoaded('contacts')),
            'users' => UserResource::collection($this->whenLoaded('users')),
            'last_followed_at' => $this->last_followed_at?->format('Y-m-d H:i:s'),
            'remark' => $this->remark,
            'created_at' => $this->formatCreatedAt(),
            'updated_at' => $this->formatUpdatedAt(),
        ];
    }
}

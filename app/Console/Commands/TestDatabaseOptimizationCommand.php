<?php

namespace App\Console\Commands;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\Models\Lead;
use Illuminate\Console\Command;

/**
 * 数据库优化组件测试命令
 *
 * 用于测试和验证数据库操作优化组件的各项功能
 */
class TestDatabaseOptimizationCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'db:test-optimization {--component=all : 测试的组件 (query-builder|transaction-manager|all)}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试数据库操作优化组件功能';

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        parent::__construct();
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始测试数据库操作优化组件...');
        $this->newLine();

        $component = $this->option('component');
        $results = [];

        switch ($component) {
            case 'query-builder':
                $results['query_builder'] = $this->testQueryBuilder();
                break;

            case 'transaction-manager':
                $results['transaction_manager'] = $this->testTransactionManager();
                break;

            case 'all':
            default:
                $results['query_builder'] = $this->testQueryBuilder();
                $results['transaction_manager'] = $this->testTransactionManager();
                break;
        }

        // 显示测试结果摘要
        $this->displaySummary($results);

        return Command::SUCCESS;
    }

    /**
     * 测试查询构建器
     */
    protected function testQueryBuilder(): array
    {
        $this->info('测试查询构建器组件...');
        $results = [];

        // 测试复杂查询构建
        $results['complex_query'] = $this->testComplexQueryBuilding();

        // 测试动态排序
        $results['dynamic_sorting'] = $this->testDynamicSorting();

        // 测试范围查询
        $results['range_conditions'] = $this->testRangeConditions();

        // 测试搜索条件
        $results['search_conditions'] = $this->testSearchConditions();

        // 测试查询优化
        $results['query_optimization'] = $this->testQueryOptimization();

        // 测试聚合查询
        $results['aggregate_query'] = $this->testAggregateQuery();

        $this->newLine();

        return $results;
    }

    /**
     * 测试事务管理器
     */
    protected function testTransactionManager(): array
    {
        $this->info('测试事务管理器组件...');
        $results = [];

        // 测试基础事务
        $results['basic_transaction'] = $this->testBasicTransaction();

        // 测试嵌套事务
        $results['nested_transaction'] = $this->testNestedTransaction();

        // 测试事务回滚
        $results['transaction_rollback'] = $this->testTransactionRollback();

        // 测试死锁重试
        $results['deadlock_retry'] = $this->testDeadlockRetry();

        // 测试批量事务
        $results['batch_transactions'] = $this->testBatchTransactions();

        // 测试事务统计
        $results['transaction_statistics'] = $this->testTransactionStatistics();

        $this->newLine();

        return $results;
    }

    /**
     * 测试复杂查询构建
     */
    protected function testComplexQueryBuilding(): bool
    {
        try {
            $this->line('  测试复杂查询构建...');

            $query = Lead::query();
            $conditions = [
                'status' => 1,
                'region' => ['operator' => 'in', 'value' => [1, 2, 3]],
                'created_at_range' => [
                    'start' => '2024-01-01',
                    'end' => '2024-12-31',
                    'type' => 'date_range',
                ],
            ];

            $result = $this->queryBuilder->buildComplexQuery($conditions, $query);
            $sql = $result->toSql();

            $this->line("    生成的 SQL: {$sql}");
            $this->line('    ✅ 复杂查询构建成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 复杂查询构建失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试动态排序
     */
    protected function testDynamicSorting(): bool
    {
        try {
            $this->line('  测试动态排序...');

            $query = Lead::query();
            $sortRules = [
                'created_at:desc',
                ['field' => 'status', 'direction' => 'asc', 'nulls' => 'last'],
            ];

            $result = $this->queryBuilder->addDynamicSorting($query, $sortRules);
            $sql = $result->toSql();

            $this->line("    生成的 SQL: {$sql}");
            $this->line('    ✅ 动态排序成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 动态排序失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试范围查询条件
     */
    protected function testRangeConditions(): bool
    {
        try {
            $this->line('  测试范围查询条件...');

            $query = Lead::query();
            $result = $this->queryBuilder->addRangeCondition(
                $query,
                'created_at',
                '2024-01-01',
                '2024-12-31',
                'date_range'
            );

            $sql = $result->toSql();
            $this->line("    生成的 SQL: {$sql}");
            $this->line('    ✅ 范围查询条件成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 范围查询条件失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试搜索条件
     */
    protected function testSearchConditions(): bool
    {
        try {
            $this->line('  测试搜索条件...');

            $query = Lead::query();
            $result = $this->queryBuilder->addSearchCondition(
                $query,
                ['company_full_name', 'company_short_name'],
                '测试公司',
                'like'
            );

            $sql = $result->toSql();
            $this->line("    生成的 SQL: {$sql}");
            $this->line('    ✅ 搜索条件成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 搜索条件失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试查询优化
     */
    protected function testQueryOptimization(): bool
    {
        try {
            $this->line('  测试查询优化...');

            $query = Lead::query()->where('status', 1);
            $suggestions = $this->queryBuilder->getOptimizationSuggestions($query);

            $this->line('    优化建议:');
            foreach ($suggestions['suggestions'] as $suggestion) {
                $this->line("      - {$suggestion['message']} (优先级: {$suggestion['priority']})");
            }

            $this->line("    复杂度分数: {$suggestions['complexity_score']}");
            $this->line('    ✅ 查询优化分析成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 查询优化失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试聚合查询
     */
    protected function testAggregateQuery(): bool
    {
        try {
            $this->line('  测试聚合查询...');

            $query = Lead::query();
            $aggregations = [
                'count' => ['field' => '*'],
                'group_count' => ['field' => 'status'],
            ];

            $results = $this->queryBuilder->buildAggregateQuery($query, $aggregations);

            $this->line("    总数: {$results['count']}");
            if (isset($results['group_count'])) {
                $this->line('    按状态分组统计:');
                foreach ($results['group_count'] as $group) {
                    $this->line("      状态 {$group['status']}: {$group['count']} 条");
                }
            }
            $this->line('    ✅ 聚合查询成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 聚合查询失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试基础事务
     */
    protected function testBasicTransaction(): bool
    {
        try {
            $this->line('  测试基础事务...');

            $result = $this->transactionManager->executeInTransaction(function () {
                // 模拟数据库操作
                return 'transaction_success';
            });

            $this->line("    事务执行结果: {$result}");
            $this->line('    ✅ 基础事务成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 基础事务失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试嵌套事务
     */
    protected function testNestedTransaction(): bool
    {
        try {
            $this->line('  测试嵌套事务...');

            $result = $this->transactionManager->executeInTransaction(function () {
                $savepoint = $this->transactionManager->beginNestedTransaction();

                // 模拟嵌套操作
                $this->transactionManager->commitNestedTransaction($savepoint);

                return 'nested_transaction_success';
            });

            $this->line("    嵌套事务执行结果: {$result}");
            $this->line('    ✅ 嵌套事务成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 嵌套事务失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试事务回滚
     */
    protected function testTransactionRollback(): bool
    {
        try {
            $this->line('  测试事务回滚...');

            try {
                $this->transactionManager->executeInTransaction(function () {
                    throw new \Exception('模拟事务失败');
                });
            } catch (\Exception $e) {
                $this->line("    事务回滚触发: {$e->getMessage()}");
                $this->line('    ✅ 事务回滚成功');

                return true;
            }

            $this->error('    ❌ 事务回滚测试失败：未触发异常');

            return false;

        } catch (\Exception $e) {
            $this->error("    ❌ 事务回滚测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试死锁重试
     */
    protected function testDeadlockRetry(): bool
    {
        try {
            $this->line('  测试死锁重试机制...');

            $result = $this->transactionManager->executeWithDeadlockRetry(function () {
                return 'deadlock_retry_success';
            }, 3, 100);

            $this->line("    死锁重试执行结果: {$result}");
            $this->line('    ✅ 死锁重试机制正常');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 死锁重试测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试批量事务
     */
    protected function testBatchTransactions(): bool
    {
        try {
            $this->line('  测试批量事务...');

            $callbacks = [
                function () {
                    return 'batch_1';
                },
                function () {
                    return 'batch_2';
                },
                function () {
                    return 'batch_3';
                },
            ];

            $results = $this->transactionManager->executeBatchTransactions($callbacks);

            $this->line('    批量事务执行结果: '.implode(', ', $results));
            $this->line('    ✅ 批量事务成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 批量事务失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试事务统计
     */
    protected function testTransactionStatistics(): bool
    {
        try {
            $this->line('  测试事务统计...');

            $stats = $this->transactionManager->getTransactionStatistics();

            $this->line('    事务统计信息:');
            $this->line("      总事务数: {$stats['total_transactions']}");
            $this->line("      成功提交: {$stats['successful_commits']}");
            $this->line("      失败回滚: {$stats['failed_rollbacks']}");
            $this->line("      成功率: {$stats['success_rate']}%");
            $this->line("      当前事务级别: {$stats['current_transaction_level']}");
            $this->line('      是否在事务中: '.($stats['in_transaction'] ? '是' : '否'));

            $this->line('    ✅ 事务统计获取成功');

            return true;

        } catch (\Exception $e) {
            $this->error("    ❌ 事务统计失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 显示测试结果摘要
     */
    protected function displaySummary(array $results): void
    {
        $this->info('测试结果摘要:');
        $this->line('==================');

        $totalTests = 0;
        $passedTests = 0;

        foreach ($results as $component => $componentResults) {
            $componentName = $component === 'query_builder' ? '查询构建器' : '事务管理器';
            $this->line("{$componentName}:");

            foreach ($componentResults as $test => $passed) {
                $totalTests++;
                if ($passed) {
                    $passedTests++;
                }

                $status = $passed ? '✅ 通过' : '❌ 失败';
                $testName = str_replace('_', ' ', $test);
                $this->line("  {$testName}: {$status}");
            }
            $this->newLine();
        }

        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        $this->line("总计: {$passedTests}/{$totalTests} 通过 (".round($successRate, 1).'%)');

        if ($successRate >= 90) {
            $this->info('🎉 数据库优化组件运行良好！');
        } elseif ($successRate >= 70) {
            $this->warn('⚠️  数据库优化组件基本正常，但有部分功能异常');
        } else {
            $this->error('🚨 数据库优化组件存在严重问题，请检查配置');
        }
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

/**
 * 验证错误响应格式命令
 */
class ValidateErrorResponseFormatCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'error:validate-format
                            {--url= : Base URL for testing (default: http://localhost:8000)}
                            {--detailed : Show detailed output}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'Validate that all error responses use the new format with errors field';

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $baseUrl = $this->option('url') ?: 'http://localhost:8000';
        $verbose = $this->option('detailed');

        $this->info('Validating error response format...');
        $this->line('Base URL: '.$baseUrl);
        $this->line('Expected format: {"code": 500, "message": "...", "errors": {...}}');
        $this->line('');

        $errorTests = [
            'exception' => 500,
            'business-exception' => 400,
            'fatal-error' => 500,
            'type-error' => 500,
            'division-by-zero' => 500,
        ];

        $results = [];
        $allPassed = true;

        foreach ($errorTests as $testType => $expectedCode) {
            $this->line("Testing: {$testType}");

            $result = $this->validateErrorResponse($baseUrl, $testType, $expectedCode, $verbose);
            $results[$testType] = $result;

            if ($result['valid']) {
                $this->line('  <fg=green>✓</> Format valid');
            } else {
                $this->line("  <fg=red>✗</> Format invalid: {$result['reason']}");
                $allPassed = false;
            }

            if ($verbose && isset($result['response'])) {
                $this->line('  Response: '.json_encode($result['response'], JSON_PRETTY_PRINT));
            }

            $this->line('');
        }

        // 显示总结
        $this->showValidationSummary($results, $allPassed);

        return $allPassed ? 0 : 1;
    }

    /**
     * 验证错误响应格式
     */
    private function validateErrorResponse(string $baseUrl, string $testType, int $expectedCode, bool $verbose): array
    {
        try {
            $url = "{$baseUrl}/api/test/errors/{$testType}";
            $response = Http::timeout(10)->get($url);

            $statusCode = $response->status();
            $body = $response->json();

            // 基本格式检查
            if (! is_array($body)) {
                return [
                    'valid' => false,
                    'reason' => 'Response is not JSON array',
                    'response' => $response->body(),
                ];
            }

            // 检查必需字段
            $requiredFields = ['code', 'message', 'errors'];
            foreach ($requiredFields as $field) {
                if (! array_key_exists($field, $body)) {
                    return [
                        'valid' => false,
                        'reason' => "Missing required field: {$field}",
                        'response' => $body,
                    ];
                }
            }

            // 检查 errors 字段类型
            if (! is_array($body['errors']) && ! is_object($body['errors'])) {
                return [
                    'valid' => false,
                    'reason' => 'errors field must be array or object',
                    'response' => $body,
                ];
            }

            // 检查状态码
            if ($statusCode !== $expectedCode) {
                return [
                    'valid' => false,
                    'reason' => "Unexpected status code: {$statusCode} (expected: {$expectedCode})",
                    'response' => $body,
                ];
            }

            // 检查响应码字段
            if ($body['code'] !== $expectedCode) {
                return [
                    'valid' => false,
                    'reason' => "Response code mismatch: {$body['code']} (expected: {$expectedCode})",
                    'response' => $body,
                ];
            }

            // 检查 errors 字段中是否包含 trace_id
            $hasTraceId = false;
            if (is_array($body['errors']) && array_key_exists('trace_id', $body['errors'])) {
                $hasTraceId = true;
            } elseif (is_object($body['errors']) && property_exists($body['errors'], 'trace_id')) {
                $hasTraceId = true;
            }

            return [
                'valid' => true,
                'reason' => 'Format is valid',
                'response' => $body,
                'has_trace_id' => $hasTraceId,
                'errors_type' => is_array($body['errors']) ? 'array' : 'object',
                'errors_count' => is_array($body['errors']) ? count($body['errors']) : count((array) $body['errors']),
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'reason' => 'Request failed: '.$e->getMessage(),
                'response' => null,
            ];
        }
    }

    /**
     * 显示验证总结
     */
    private function showValidationSummary(array $results, bool $allPassed): void
    {
        $total = count($results);
        $passed = count(array_filter($results, fn ($r) => $r['valid']));
        $failed = $total - $passed;

        $this->line('<fg=cyan>Validation Summary:</>');
        $this->line("Total tests: {$total}");
        $this->line("<fg=green>Passed: {$passed}</>");
        $this->line("<fg=red>Failed: {$failed}</>");

        if ($allPassed) {
            $this->line('');
            $this->line('<fg=green>🎉 All error responses use the correct format!</>');

            // 显示格式特性统计
            $this->showFormatFeatures($results);
        } else {
            $this->line('');
            $this->line('<fg=red>❌ Some error responses have format issues:</>');
            foreach ($results as $testType => $result) {
                if (! $result['valid']) {
                    $this->line("  {$testType}: {$result['reason']}");
                }
            }
        }
    }

    /**
     * 显示格式特性统计
     */
    private function showFormatFeatures(array $results): void
    {
        $validResults = array_filter($results, fn ($r) => $r['valid']);

        if (empty($validResults)) {
            return;
        }

        $this->line('');
        $this->line('<fg=cyan>Format Features:</>');

        $withTraceId = count(array_filter($validResults, fn ($r) => $r['has_trace_id'] ?? false));
        $withTimestamp = count(array_filter($validResults, fn ($r) => $r['has_timestamp'] ?? false));
        $arrayErrors = count(array_filter($validResults, fn ($r) => ($r['errors_type'] ?? '') === 'array'));
        $objectErrors = count(array_filter($validResults, fn ($r) => ($r['errors_type'] ?? '') === 'object'));

        $this->line("Responses with trace_id: {$withTraceId}/".count($validResults));
        $this->line("Responses with timestamp: {$withTimestamp}/".count($validResults));
        $this->line("Errors as array: {$arrayErrors}/".count($validResults));
        $this->line("Errors as object: {$objectErrors}/".count($validResults));
    }
}

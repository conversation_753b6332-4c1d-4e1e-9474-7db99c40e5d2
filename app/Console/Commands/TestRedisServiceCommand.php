<?php

namespace App\Console\Commands;

use App\Contracts\RedisServiceInterface;
use Illuminate\Console\Command;

/**
 * Redis 服务测试命令
 *
 * 用于测试 Redis 服务的各项功能
 */
class TestRedisServiceCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'redis:test {--connection=default : Redis 连接名称}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试 Redis 服务功能';

    /**
     * Redis 服务实例
     */
    protected RedisServiceInterface $redisService;

    /**
     * 构造函数
     */
    public function __construct(RedisServiceInterface $redisService)
    {
        parent::__construct();
        $this->redisService = $redisService;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始测试 Redis 服务...');
        $this->newLine();

        $connection = $this->option('connection');
        if ($connection !== 'default') {
            $this->redisService = app("redis.service.{$connection}");
            $this->info("使用 Redis 连接: {$connection}");
        }

        $results = [];

        // 测试连接
        $results['connection'] = $this->testConnection();

        // 测试基础操作
        $results['basic_operations'] = $this->testBasicOperations();

        // 测试批量操作
        $results['batch_operations'] = $this->testBatchOperations();

        // 测试计数器操作
        $results['counter_operations'] = $this->testCounterOperations();

        // 测试模式匹配
        $results['pattern_matching'] = $this->testPatternMatching();

        // 显示测试结果摘要
        $this->displaySummary($results);

        return Command::SUCCESS;
    }

    /**
     * 测试连接
     */
    protected function testConnection(): bool
    {
        $this->info('测试 Redis 连接...');

        $isConnected = $this->redisService->ping();

        if ($isConnected) {
            $this->line('✅ Redis 连接正常');

            $info = $this->redisService->getConnectionInfo();
            if (! empty($info['server_info'])) {
                $this->line('   Redis 版本: '.($info['server_info']['redis_version'] ?? 'Unknown'));
            }
        } else {
            $this->error('❌ Redis 连接失败');
        }

        $this->newLine();

        return $isConnected;
    }

    /**
     * 测试基础操作
     */
    protected function testBasicOperations(): bool
    {
        $this->info('测试基础键值操作...');

        $testKey = 'test:command:'.time();
        $testValue = [
            'message' => 'Hello from command!',
            'timestamp' => time(),
        ];

        try {
            // 设置键值对
            $setResult = $this->redisService->set($testKey, $testValue, 300);
            $this->line($setResult ? '✅ SET 操作成功' : '❌ SET 操作失败');

            // 获取键值对
            $getValue = $this->redisService->get($testKey);
            $getSuccess = $getValue !== null && $getValue['message'] === 'Hello from command!';
            $this->line($getSuccess ? '✅ GET 操作成功' : '❌ GET 操作失败');

            // 检查键是否存在
            $exists = $this->redisService->exists($testKey);
            $this->line($exists ? '✅ EXISTS 操作成功' : '❌ EXISTS 操作失败');

            // 获取过期时间
            $ttl = $this->redisService->ttl($testKey);
            $ttlSuccess = $ttl > 0 && $ttl <= 300;
            $this->line($ttlSuccess ? "✅ TTL 操作成功 (剩余: {$ttl}秒)" : '❌ TTL 操作失败');

            // 删除键
            $deleteResult = $this->redisService->delete($testKey);
            $this->line($deleteResult ? '✅ DELETE 操作成功' : '❌ DELETE 操作失败');

            $success = $setResult && $getSuccess && $exists && $ttlSuccess && $deleteResult;

        } catch (\Exception $e) {
            $this->error('❌ 基础操作测试失败: '.$e->getMessage());
            $success = false;
        }

        $this->newLine();

        return $success;
    }

    /**
     * 测试批量操作
     */
    protected function testBatchOperations(): bool
    {
        $this->info('测试批量操作...');

        $timestamp = time();
        $testData = [
            "batch:cmd1:{$timestamp}" => ['name' => '张三', 'type' => 'user'],
            "batch:cmd2:{$timestamp}" => ['name' => '李四', 'type' => 'admin'],
            "batch:cmd3:{$timestamp}" => ['name' => '王五', 'type' => 'guest'],
        ];

        try {
            // 批量设置
            $setResult = $this->redisService->setMultiple($testData, 300);
            $this->line($setResult ? '✅ 批量设置成功' : '❌ 批量设置失败');

            // 批量获取
            $keys = array_keys($testData);
            $getResult = $this->redisService->getMultiple($keys);
            $getSuccess = count($getResult) === count($keys) &&
                         $getResult["batch:cmd1:{$timestamp}"]['name'] === '张三';
            $this->line($getSuccess ? '✅ 批量获取成功' : '❌ 批量获取失败');

            // 批量删除
            $deleteResult = $this->redisService->deleteMultiple($keys);
            $deleteSuccess = $deleteResult === count($keys);
            $this->line($deleteSuccess ? "✅ 批量删除成功 (删除了 {$deleteResult} 个键)" : '❌ 批量删除失败');

            $success = $setResult && $getSuccess && $deleteSuccess;

        } catch (\Exception $e) {
            $this->error('❌ 批量操作测试失败: '.$e->getMessage());
            $success = false;
        }

        $this->newLine();

        return $success;
    }

    /**
     * 测试计数器操作
     */
    protected function testCounterOperations(): bool
    {
        $this->info('测试计数器操作...');

        $counterKey = 'test:counter:cmd:'.time();

        try {
            // 递增操作
            $increment1 = $this->redisService->increment($counterKey);
            $this->line($increment1 === 1 ? '✅ 递增操作成功 (值: 1)' : "❌ 递增操作失败 (值: {$increment1})");

            $increment5 = $this->redisService->increment($counterKey, 5);
            $this->line($increment5 === 6 ? '✅ 递增5成功 (值: 6)' : "❌ 递增5失败 (值: {$increment5})");

            // 递减操作
            $decrement2 = $this->redisService->decrement($counterKey, 2);
            $this->line($decrement2 === 4 ? '✅ 递减2成功 (值: 4)' : "❌ 递减2失败 (值: {$decrement2})");

            // 清理测试数据
            $this->redisService->delete($counterKey);

            $success = $increment1 === 1 && $increment5 === 6 && $decrement2 === 4;

        } catch (\Exception $e) {
            $this->error('❌ 计数器操作测试失败: '.$e->getMessage());
            $success = false;
        }

        $this->newLine();

        return $success;
    }

    /**
     * 测试模式匹配
     */
    protected function testPatternMatching(): bool
    {
        $this->info('测试模式匹配...');

        $timestamp = time();
        $testKeys = [
            "pattern:cmd:user:{$timestamp}:1" => 'user1',
            "pattern:cmd:user:{$timestamp}:2" => 'user2',
            "pattern:cmd:order:{$timestamp}:1" => 'order1',
        ];

        try {
            // 设置测试数据
            foreach ($testKeys as $key => $value) {
                $this->redisService->set($key, $value, 300);
            }

            // 模式匹配查找
            $userKeys = $this->redisService->keys("pattern:cmd:user:{$timestamp}:*");
            $userSuccess = count($userKeys) === 2;
            $this->line($userSuccess ? '✅ 用户模式匹配成功 (找到 '.count($userKeys).' 个键)' : '❌ 用户模式匹配失败');

            $allKeys = $this->redisService->keys("pattern:cmd:*:{$timestamp}:*");
            $allSuccess = count($allKeys) === 3;
            $this->line($allSuccess ? '✅ 全部模式匹配成功 (找到 '.count($allKeys).' 个键)' : '❌ 全部模式匹配失败');

            // 清理测试数据
            $this->redisService->deleteMultiple(array_keys($testKeys));

            $success = $userSuccess && $allSuccess;

        } catch (\Exception $e) {
            $this->error('❌ 模式匹配测试失败: '.$e->getMessage());
            $success = false;
        }

        $this->newLine();

        return $success;
    }

    /**
     * 显示测试结果摘要
     *
     * @param  array<string, bool>  $results
     */
    protected function displaySummary(array $results): void
    {
        $this->info('测试结果摘要:');
        $this->line('==================');

        $totalTests = count($results);
        $passedTests = count(array_filter($results));
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;

        foreach ($results as $test => $passed) {
            $status = $passed ? '✅ 通过' : '❌ 失败';
            $testName = match ($test) {
                'connection' => 'Redis 连接',
                'basic_operations' => '基础操作',
                'batch_operations' => '批量操作',
                'counter_operations' => '计数器操作',
                'pattern_matching' => '模式匹配',
                default => $test,
            };
            $this->line("{$testName}: {$status}");
        }

        $this->newLine();
        $this->line("总计: {$passedTests}/{$totalTests} 通过 (".round($successRate, 1).'%)');

        if ($successRate >= 90) {
            $this->info('🎉 Redis 服务运行良好！');
        } elseif ($successRate >= 70) {
            $this->warn('⚠️  Redis 服务基本正常，但有部分功能异常');
        } else {
            $this->error('🚨 Redis 服务存在严重问题，请检查配置和连接');
        }
    }
}

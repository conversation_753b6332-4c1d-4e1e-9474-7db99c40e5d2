<?php

namespace App\Console\Commands;

use App\Models\Contact;
use App\Models\Lead;
use App\Resources\BaseResource;
use App\Resources\ContactResource;
use App\Resources\Lead\LeadResource;
use Exception;
use Illuminate\Console\Command;

/**
 * 测试资源类 Bug 修复命令
 *
 * 用于测试和验证资源类中时间戳格式化的修复
 */
class TestResourceBugFixCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'test:resource-bugfix';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试资源类时间戳格式化 Bug 修复';

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始测试资源类 Bug 修复...');
        $this->newLine();

        $results = [];

        // 测试正常的时间戳格式化
        $results['normal_timestamps'] = $this->testNormalTimestamps();

        // 测试 NULL 时间戳处理
        $results['null_timestamps'] = $this->testNullTimestamps();

        // 测试异常时间戳处理
        $results['invalid_timestamps'] = $this->testInvalidTimestamps();

        // 显示测试结果
        $this->displayResults($results);

        return Command::SUCCESS;
    }

    /**
     * 测试正常的时间戳格式化
     */
    protected function testNormalTimestamps(): bool
    {
        try {
            $this->line('  测试正常时间戳格式化...');

            // 测试联系人资源
            $contact = Contact::first();
            if ($contact) {
                $contactResource = new ContactResource($contact);
                $contactArray = $contactResource->toArray(request());

                if (isset($contactArray['created_at']) && isset($contactArray['updated_at'])) {
                    $this->line("    联系人时间戳格式化成功: {$contactArray['created_at']}");
                } else {
                    throw new Exception('联系人时间戳字段缺失');
                }
            }

            // 测试线索资源
            $lead = Lead::first();
            if ($lead) {
                $leadResource = new LeadResource($lead);
                $leadArray = $leadResource->toArray(request());

                if (isset($leadArray['created_at']) && isset($leadArray['updated_at'])) {
                    $this->line("    线索时间戳格式化成功: {$leadArray['created_at']}");
                } else {
                    throw new Exception('线索时间戳字段缺失');
                }
            }

            $this->line('    ✅ 正常时间戳格式化测试通过');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 正常时间戳格式化测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试 NULL 时间戳处理
     */
    protected function testNullTimestamps(): bool
    {
        try {
            $this->line('  测试 NULL 时间戳处理...');

            // 创建一个模拟的联系人对象，时间戳为 null
            $mockContact = new Contact;
            $mockContact->id = 999999;
            $mockContact->name = '测试联系人';
            $mockContact->created_at = null;
            $mockContact->updated_at = null;

            $contactResource = new ContactResource($mockContact);
            $contactArray = $contactResource->toArray(request());

            // 检查 NULL 时间戳是否被正确处理
            if ($contactArray['created_at'] === null && $contactArray['updated_at'] === null) {
                $this->line('    NULL 时间戳正确处理为 null');
            } else {
                throw new Exception('NULL 时间戳处理不正确');
            }

            $this->line('    ✅ NULL 时间戳处理测试通过');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ NULL 时间戳处理测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试异常时间戳处理
     */
    protected function testInvalidTimestamps(): bool
    {
        try {
            $this->line('  测试异常时间戳处理...');

            // 测试 BaseResource 的 safeFormatTimestamp 方法
            $baseResource = new class(null) extends BaseResource
            {
                public function testSafeFormat($timestamp)
                {
                    return $this->safeFormatTimestamp($timestamp);
                }

                public function toArray($request)
                {
                    return [];
                }
            };

            // 测试各种异常情况
            $testCases = [
                null => null,
                '' => null,
                'invalid-date' => null,
                'not-a-date' => null,
            ];

            foreach ($testCases as $input => $expected) {
                $result = $baseResource->testSafeFormat($input);
                if ($result !== $expected) {
                    throw new Exception("输入 '{$input}' 期望 '{$expected}' 但得到 '{$result}'");
                }
            }

            $this->line('    异常时间戳正确处理为 null');
            $this->line('    ✅ 异常时间戳处理测试通过');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 异常时间戳处理测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 显示测试结果
     */
    protected function displayResults(array $results): void
    {
        $this->newLine();
        $this->info('测试结果摘要:');
        $this->line('==================');

        $totalTests = count($results);
        $passedTests = array_sum($results);

        foreach ($results as $test => $passed) {
            $status = $passed ? '✅ 通过' : '❌ 失败';
            $testName = str_replace('_', ' ', $test);
            $this->line("{$testName}: {$status}");
        }

        $this->newLine();
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        $this->line("总计: {$passedTests}/{$totalTests} 通过 (".round($successRate, 1).'%)');

        if ($successRate === 100) {
            $this->info('🎉 所有资源类 Bug 修复测试通过！');
        } elseif ($successRate >= 70) {
            $this->warn('⚠️  大部分测试通过，但仍有部分问题');
        } else {
            $this->error('🚨 资源类 Bug 修复存在问题，请检查实现');
        }
    }
}

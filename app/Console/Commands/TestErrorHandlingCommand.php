<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

/**
 * 错误处理测试命令
 */
class TestErrorHandlingCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'error:test 
                            {type? : Error type to test (normal, fatal-error, exception, etc.)}
                            {--all : Test all error types}
                            {--url= : Base URL for testing (default: http://localhost:8000)}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'Test the global error handling system';

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $baseUrl = $this->option('url') ?: 'http://localhost:8000';
        $type = $this->argument('type');
        $testAll = $this->option('all');

        $this->info('Starting error handling tests...');
        $this->line('Base URL: '.$baseUrl);
        $this->line('');

        if ($testAll) {
            return $this->testAllErrorTypes($baseUrl);
        }

        if ($type) {
            return $this->testSpecificError($baseUrl, $type);
        }

        // 显示可用的测试类型
        $this->showAvailableTests($baseUrl);

        return 0;
    }

    /**
     * 测试所有错误类型
     */
    private function testAllErrorTypes(string $baseUrl): int
    {
        $errorTypes = [
            'normal',
            'warning',
            'notice',
            'exception',
            'business-exception',
            'division-by-zero',
            'type-error',
            'array-error',
        ];

        $results = [];

        foreach ($errorTypes as $type) {
            $this->line("Testing: {$type}");
            $result = $this->performTest($baseUrl, $type);
            $results[$type] = $result;

            if ($result['success']) {
                $this->line("  <fg=green>✓</> {$result['message']}");
            } else {
                $this->line("  <fg=red>✗</> {$result['message']}");
            }

            $this->line('');
        }

        // 显示总结
        $this->showTestSummary($results);

        return 0;
    }

    /**
     * 测试特定错误类型
     */
    private function testSpecificError(string $baseUrl, string $type): int
    {
        $this->info("Testing error type: {$type}");

        $result = $this->performTest($baseUrl, $type);

        if ($result['success']) {
            $this->line("<fg=green>✓</> {$result['message']}");

            if (isset($result['details'])) {
                $this->line('Response details:');
                $this->line(json_encode($result['details'], JSON_PRETTY_PRINT) ?: '{}');
            }
        } else {
            $this->line("<fg=red>✗</> {$result['message']}");

            if (isset($result['error'])) {
                $this->line('Error details:');
                $this->line($result['error']);
            }
        }

        return $result['success'] ? 0 : 1;
    }

    /**
     * 执行测试
     */
    private function performTest(string $baseUrl, string $type): array
    {
        try {
            $url = "{$baseUrl}/api/test/errors/{$type}";

            $response = Http::timeout(10)->get($url);

            $statusCode = $response->status();
            $body = $response->json();

            // 检查响应格式
            if (! is_array($body) || ! isset($body['code'], $body['message'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid response format',
                    'error' => 'Response is not in expected JSON format',
                ];
            }

            // 检查错误响应是否包含 errors 字段
            if ($statusCode >= 400 && ! isset($body['errors'])) {
                return [
                    'success' => false,
                    'message' => 'Error response missing errors field',
                    'error' => 'Error responses should contain errors field',
                ];
            }

            // 根据错误类型判断是否符合预期
            $expected = $this->getExpectedResult($type);

            if ($statusCode === $expected['status_code']) {
                return [
                    'success' => true,
                    'message' => "Test passed (HTTP {$statusCode})",
                    'details' => $body,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Unexpected status code: {$statusCode} (expected: {$expected['status_code']})",
                    'error' => json_encode($body),
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Request failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取预期结果
     */
    private function getExpectedResult(string $type): array
    {
        $expectations = [
            'normal' => ['status_code' => 200],
            'warning' => ['status_code' => 200], // Warning 不会阻止执行
            'notice' => ['status_code' => 200],  // Notice 不会阻止执行
            'exception' => ['status_code' => 500],
            'business-exception' => ['status_code' => 400],
            'division-by-zero' => ['status_code' => 500],
            'type-error' => ['status_code' => 500],
            'array-error' => ['status_code' => 500],
            'fatal-error' => ['status_code' => 500],
            'parse-error' => ['status_code' => 500],
            'memory-error' => ['status_code' => 500],
            'stack-overflow' => ['status_code' => 500],
        ];

        return $expectations[$type] ?? ['status_code' => 500];
    }

    /**
     * 显示可用的测试
     */
    private function showAvailableTests(string $baseUrl): void
    {
        try {
            $response = Http::get("{$baseUrl}/api/test/errors/");

            if ($response->successful()) {
                $data = $response->json();

                $this->info('Available error tests:');
                $this->line('');

                if (isset($data['data']) && is_array($data['data'])) {
                    foreach ($data['data'] as $type => $description) {
                        $this->line("  <fg=cyan>{$type}</> - {$description}");
                    }
                }

                $this->line('');
                $this->line('Usage examples:');
                $this->line('  php artisan error:test normal');
                $this->line('  php artisan error:test exception');
                $this->line('  php artisan error:test --all');
            } else {
                $this->error('Failed to fetch available tests');
            }

        } catch (\Exception $e) {
            $this->error('Error fetching test list: '.$e->getMessage());
        }
    }

    /**
     * 显示测试总结
     */
    private function showTestSummary(array $results): void
    {
        $total = count($results);
        $passed = count(array_filter($results, fn ($r) => $r['success']));
        $failed = $total - $passed;

        $this->line('');
        $this->line('<fg=cyan>Test Summary:</>');
        $this->line("Total tests: {$total}");
        $this->line("<fg=green>Passed: {$passed}</>");
        $this->line("<fg=red>Failed: {$failed}</>");

        if ($failed > 0) {
            $this->line('');
            $this->line('<fg=red>Failed tests:</>');
            foreach ($results as $type => $result) {
                if (! $result['success']) {
                    $this->line("  {$type}: {$result['message']}");
                }
            }
        }
    }
}

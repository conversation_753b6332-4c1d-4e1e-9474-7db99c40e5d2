<?php

namespace App\Console\Commands;

use App\Services\LogHealthMonitorService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 日志健康检查命令
 */
class LogHealthCheckCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'log:health-check
                            {--alert : Send alerts for unhealthy status}
                            {--detailed : Show detailed output}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'Check the health status of the logging system';

    private LogHealthMonitorService $healthMonitor;

    /**
     * 构造函数
     */
    public function __construct(LogHealthMonitorService $healthMonitor)
    {
        parent::__construct();
        $this->healthMonitor = $healthMonitor;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('Starting log system health check...');

        try {
            $healthStatus = $this->healthMonitor->performHealthCheck();

            $this->displayHealthStatus($healthStatus);

            if ($this->option('alert') && ! empty($healthStatus['alerts'])) {
                $this->sendAlerts($healthStatus['alerts']);
            }

            // 根据健康状态返回适当的退出码
            return match ($healthStatus['overall_status']) {
                'healthy' => 0,
                'degraded' => 1,
                'unhealthy' => 2,
                default => 3
            };

        } catch (\Exception $e) {
            $this->error('Health check failed: '.$e->getMessage());

            if ($this->option('detailed')) {
                $this->error('Stack trace: '.$e->getTraceAsString());
            }

            Log::error('Log health check command failed', [
                'error' => $e->getMessage(),
                'command' => 'log:health-check',
            ]);

            return 4;
        }
    }

    /**
     * 显示健康状态
     */
    private function displayHealthStatus(array $healthStatus): void
    {
        $status = $healthStatus['overall_status'];

        // 显示总体状态
        $statusColor = match ($status) {
            'healthy' => 'green',
            'degraded' => 'yellow',
            'unhealthy' => 'red',
            default => 'white'
        };

        $this->line('');
        $this->line("<fg={$statusColor}>Overall Status: ".strtoupper($status).'</>');
        $this->line('Timestamp: '.$healthStatus['timestamp']);

        // 显示详细检查结果
        if ($this->option('detailed') || $status !== 'healthy') {
            $this->line('');
            $this->line('<fg=cyan>Detailed Check Results:</>');

            foreach ($healthStatus['checks'] as $checkName => $checkResult) {
                $checkStatus = $checkResult['healthy'] ? 'PASS' : 'FAIL';
                $checkColor = $checkResult['healthy'] ? 'green' : 'red';

                $this->line("  <fg={$checkColor}>[{$checkStatus}]</> {$checkName}: {$checkResult['message']}");

                if ($this->option('detailed') && isset($checkResult['details'])) {
                    foreach ($checkResult['details'] as $key => $value) {
                        if (is_array($value)) {
                            $value = json_encode($value);
                        }
                        $this->line("    {$key}: {$value}");
                    }
                }
            }
        }

        // 显示告警信息
        if (! empty($healthStatus['alerts'])) {
            $this->line('');
            $this->line('<fg=red>Alerts:</>');
            foreach ($healthStatus['alerts'] as $alert) {
                $this->line("  <fg=red>⚠</> {$alert}");
            }
        }

        $this->line('');
    }

    /**
     * 发送告警
     */
    private function sendAlerts(array $alerts): void
    {
        $this->info('Sending alerts...');

        foreach ($alerts as $alert) {
            try {
                // 这里可以集成各种告警渠道
                Log::channel('single')->critical('Log System Alert (CLI)', [
                    'alert_message' => $alert,
                    'source' => 'health-check-command',
                    'timestamp' => now()->toISOString(),
                ]);

                $this->line("  <fg=green>✓</> Alert sent: {$alert}");

            } catch (\Exception $e) {
                $this->line("  <fg=red>✗</> Failed to send alert: {$alert}");
                $this->line("    Error: {$e->getMessage()}");
            }
        }
    }
}

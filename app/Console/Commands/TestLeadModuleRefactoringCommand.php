<?php

namespace App\Console\Commands;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\DTOs\Lead\LeadListDTO;
use App\Repositories\ContactRepositoryInterface;
use App\Repositories\LeadRepositoryInterface;
use App\Services\LeadService;
use Exception;
use Illuminate\Console\Command;

/**
 * 线索模块重构功能测试命令
 *
 * 用于测试和验证重构后的线索模块各项功能
 */
class TestLeadModuleRefactoringCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'Lead:test-refactoring {--component=all : 测试的组件 (repository|service|performance|all)}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试线索模块重构后的功能';

    /**
     * 线索服务实例
     */
    protected LeadService $leadService;

    /**
     * 线索仓储实例
     */
    protected LeadRepositoryInterface $leadRepository;

    /**
     * 联系人仓储实例
     */
    protected ContactRepositoryInterface $contactRepository;

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        LeadService $leadService,
        LeadRepositoryInterface $leadRepository,
        ContactRepositoryInterface $contactRepository,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        parent::__construct();
        $this->leadService = $leadService;
        $this->leadRepository = $leadRepository;
        $this->contactRepository = $contactRepository;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $this->info('开始测试线索模块重构功能...');
        $this->newLine();

        $component = $this->option('component');
        $results = [];

        switch ($component) {
            case 'repository':
                $results['repository'] = $this->testRepositoryLayer();
                break;

            case 'service':
                $results['service'] = $this->testServiceLayer();
                break;

            case 'performance':
                $results['performance'] = $this->testPerformance();
                break;

            case 'all':
            default:
                $results['repository'] = $this->testRepositoryLayer();
                $results['service'] = $this->testServiceLayer();
                $results['performance'] = $this->testPerformance();
                break;
        }

        // 显示测试结果摘要
        $this->displaySummary($results);

        return Command::SUCCESS;
    }

    /**
     * 测试 Repository 层
     */
    protected function testRepositoryLayer(): array
    {
        $this->info('测试 Repository 层功能...');
        $results = [];

        // 测试线索查询功能
        $results['lead_query'] = $this->testLeadQuery();

        // 测试线索CRUD操作
        $results['lead_crud'] = $this->testLeadCRUD();

        // 测试批量操作
        $results['batch_operations'] = $this->testBatchOperations();

        // 测试联系人操作
        $results['contact_operations'] = $this->testContactOperations();

        // 测试缓存功能
        $results['cache_functionality'] = $this->testCacheFunctionality();

        $this->newLine();

        return $results;
    }

    /**
     * 测试线索查询功能
     */
    protected function testLeadQuery(): bool
    {
        try {
            $this->line('  测试线索查询功能...');

            // 创建测试DTO
            $dto = new LeadListDTO([
                'page' => 1,
                'pageSize' => 10,
                'sortBy' => 'created_at',
                'sortDirection' => 'desc',
            ]);

            // 执行查询
            $result = $this->leadRepository->getLeadsList($dto);

            $this->line("    查询结果: 总数 {$result->total()}, 当前页 {$result->count()} 条");
            $this->line('    ✅ 线索查询功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 线索查询功能失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试线索CRUD操作
     */
    protected function testLeadCRUD(): bool
    {
        try {
            $this->line('  测试线索CRUD操作...');

            // 测试数据
            $testData = [
                'company_full_name' => '测试公司_'.time(),
                'company_short_name' => '测试公司',
                'region' => 1,
                'source' => 1,
                'industry' => 1,
                'status' => 1,
                'stage' => 1,
                'creator_id' => 1,
                'remark' => '重构测试数据',
            ];

            // 测试创建
            $lead = $this->leadRepository->create($testData);
            $this->line("    创建线索成功: ID {$lead->id}");

            // 测试查询
            $foundLead = $this->leadRepository->findById($lead->id);
            if (! $foundLead) {
                throw new Exception('查询创建的线索失败');
            }
            $this->line("    查询线索成功: {$foundLead->company_full_name}");

            // 测试更新
            $updateData = ['remark' => '更新后的备注_'.time()];
            $updateResult = $this->leadRepository->update($lead->id, $updateData);
            if (! $updateResult) {
                throw new Exception('更新线索失败');
            }
            $this->line('    更新线索成功');

            // 测试删除
            $deleteResult = $this->leadRepository->delete($lead->id);
            if (! $deleteResult) {
                throw new Exception('删除线索失败');
            }
            $this->line('    删除线索成功');

            $this->line('    ✅ 线索CRUD操作正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 线索CRUD操作失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试批量操作
     */
    protected function testBatchOperations(): bool
    {
        try {
            $this->line('  测试批量操作...');

            // 创建测试数据
            $testLeads = [];
            for ($i = 1; $i <= 3; $i++) {
                $lead = $this->leadRepository->create([
                    'company_full_name' => "批量测试公司_{$i}_".time(),
                    'company_short_name' => "测试{$i}",
                    'region' => 1,
                    'source' => 1,
                    'industry' => 1,
                    'status' => 1,
                    'stage' => 1,
                    'creator_id' => 1,
                    'remark' => '批量操作测试数据',
                ]);
                $testLeads[] = $lead->id;
            }

            $this->line('    创建了 '.count($testLeads).' 条测试数据');

            // 测试批量更新状态
            $updateCount = $this->leadRepository->batchUpdateStatus($testLeads, 2);
            $this->line("    批量更新状态成功: {$updateCount} 条");

            // 清理测试数据
            foreach ($testLeads as $leadId) {
                $this->leadRepository->delete($leadId);
            }
            $this->line('    清理测试数据完成');

            $this->line('    ✅ 批量操作功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 批量操作失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试联系人操作
     */
    protected function testContactOperations(): bool
    {
        try {
            $this->line('  测试联系人操作...');

            // 测试联系人列表查询
            $contacts = $this->contactRepository->getContactsList([], 5, 1);
            $this->line("    联系人列表查询成功: {$contacts->count()} 条");

            // 测试联系人搜索
            $searchResults = $this->contactRepository->searchContacts('测试', 5);
            $this->line("    联系人搜索成功: {$searchResults->count()} 条");

            $this->line('    ✅ 联系人操作功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 联系人操作失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试缓存功能
     */
    protected function testCacheFunctionality(): bool
    {
        try {
            $this->line('  测试缓存功能...');

            // 获取缓存统计
            $cacheStats = $this->queryBuilder->getCacheStatistics();
            $this->line("    缓存统计: 总查询 {$cacheStats['total_queries']}, 命中率 {$cacheStats['hit_rate']}%");

            $this->line('    ✅ 缓存功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 缓存功能测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试 Service 层
     */
    protected function testServiceLayer(): array
    {
        $this->info('测试 Service 层功能...');
        $results = [];

        // 测试业务逻辑
        $results['business_logic'] = $this->testBusinessLogic();

        // 测试事务管理
        $results['transaction_management'] = $this->testTransactionManagement();

        // 测试异常处理
        $results['exception_handling'] = $this->testExceptionHandling();

        $this->newLine();

        return $results;
    }

    /**
     * 测试业务逻辑
     */
    protected function testBusinessLogic(): bool
    {
        try {
            $this->line('  测试业务逻辑...');

            // 测试获取线索统计
            $stats = $this->leadService->getLeadsStatistics();
            $this->line("    线索统计: 总数 {$stats['count']}");

            $this->line('    ✅ 业务逻辑功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 业务逻辑测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试事务管理
     */
    protected function testTransactionManagement(): bool
    {
        try {
            $this->line('  测试事务管理...');

            // 获取事务统计
            $transactionStats = $this->transactionManager->getTransactionStatistics();
            $this->line("    事务统计: 总数 {$transactionStats['total_transactions']}, 成功率 {$transactionStats['success_rate']}%");

            $this->line('    ✅ 事务管理功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 事务管理测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试异常处理
     */
    protected function testExceptionHandling(): bool
    {
        try {
            $this->line('  测试异常处理...');

            // 测试查询不存在的线索
            $nonExistentLead = $this->leadRepository->findById(999999);
            if ($nonExistentLead === null) {
                $this->line('    不存在的线索查询返回null - 正常');
            }

            $this->line('    ✅ 异常处理功能正常');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 异常处理测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试性能
     */
    protected function testPerformance(): array
    {
        $this->info('测试性能表现...');
        $results = [];

        // 测试查询性能
        $results['query_performance'] = $this->testQueryPerformance();

        // 测试缓存性能
        $results['cache_performance'] = $this->testCachePerformance();

        // 测试批量操作性能
        $results['batch_performance'] = $this->testBatchPerformance();

        $this->newLine();

        return $results;
    }

    /**
     * 测试查询性能
     */
    protected function testQueryPerformance(): bool
    {
        try {
            $this->line('  测试查询性能...');

            $startTime = microtime(true);

            // 执行多次查询测试
            for ($i = 0; $i < 10; $i++) {
                $dto = new LeadListDTO([
                    'page' => 1,
                    'pageSize' => 10,
                    'sortBy' => 'created_at',
                    'sortDirection' => 'desc',
                ]);
                $this->leadRepository->getLeadsList($dto);
            }

            $endTime = microtime(true);
            $avgTime = ($endTime - $startTime) / 10 * 1000; // 转换为毫秒

            $this->line('    10次查询平均耗时: '.round($avgTime, 2).'ms');
            $this->line('    ✅ 查询性能测试完成');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 查询性能测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试缓存性能
     */
    protected function testCachePerformance(): bool
    {
        try {
            $this->line('  测试缓存性能...');

            // 第一次查询（缓存未命中）
            $startTime = microtime(true);
            $this->leadRepository->findById(1);
            $firstQueryTime = (microtime(true) - $startTime) * 1000;

            // 第二次查询（缓存命中）
            $startTime = microtime(true);
            $this->leadRepository->findById(1);
            $secondQueryTime = (microtime(true) - $startTime) * 1000;

            $this->line('    首次查询耗时: '.round($firstQueryTime, 2).'ms');
            $this->line('    缓存命中耗时: '.round($secondQueryTime, 2).'ms');

            if ($secondQueryTime < $firstQueryTime) {
                $improvement = (($firstQueryTime - $secondQueryTime) / $firstQueryTime) * 100;
                $this->line('    缓存性能提升: '.round($improvement, 1).'%');
            }

            $this->line('    ✅ 缓存性能测试完成');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 缓存性能测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 测试批量操作性能
     */
    protected function testBatchPerformance(): bool
    {
        try {
            $this->line('  测试批量操作性能...');

            // 创建测试数据
            $testIds = [];
            for ($i = 1; $i <= 50; $i++) {
                $lead = $this->leadRepository->create([
                    'company_full_name' => "性能测试公司_{$i}_".time(),
                    'company_short_name' => "性能测试{$i}",
                    'region' => 1,
                    'source' => 1,
                    'industry' => 1,
                    'status' => 1,
                    'stage' => 1,
                    'creator_id' => 1,
                ]);
                $testIds[] = $lead->id;
            }

            // 测试批量更新性能
            $startTime = microtime(true);
            $updateCount = $this->leadRepository->batchUpdateStatus($testIds, 2);
            $batchTime = (microtime(true) - $startTime) * 1000;

            $this->line("    批量更新 {$updateCount} 条记录耗时: ".round($batchTime, 2).'ms');

            // 清理测试数据
            foreach ($testIds as $leadId) {
                $this->leadRepository->delete($leadId);
            }

            $this->line('    ✅ 批量操作性能测试完成');

            return true;

        } catch (Exception $e) {
            $this->error("    ❌ 批量操作性能测试失败: {$e->getMessage()}");

            return false;
        }
    }

    /**
     * 显示测试结果摘要
     */
    protected function displaySummary(array $results): void
    {
        $this->info('测试结果摘要:');
        $this->line('==================');

        $totalTests = 0;
        $passedTests = 0;

        foreach ($results as $component => $componentResults) {
            $componentName = match ($component) {
                'repository' => 'Repository 层',
                'service' => 'Service 层',
                'performance' => '性能测试',
                default => $component
            };

            $this->line("{$componentName}:");

            foreach ($componentResults as $test => $passed) {
                $totalTests++;
                if ($passed) {
                    $passedTests++;
                }

                $status = $passed ? '✅ 通过' : '❌ 失败';
                $testName = str_replace('_', ' ', $test);
                $this->line("  {$testName}: {$status}");
            }
            $this->newLine();
        }

        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        $this->line("总计: {$passedTests}/{$totalTests} 通过 (".round($successRate, 1).'%)');

        if ($successRate >= 90) {
            $this->info('🎉 线索模块重构功能运行良好！');
        } elseif ($successRate >= 70) {
            $this->warn('⚠️  线索模块重构基本正常，但有部分功能异常');
        } else {
            $this->error('🚨 线索模块重构存在严重问题，请检查实现');
        }
    }
}

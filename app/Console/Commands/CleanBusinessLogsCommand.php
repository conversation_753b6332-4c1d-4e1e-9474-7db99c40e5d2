<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

/**
 * 清理业务日志命令
 *
 * 自动删除过期的业务日志文件
 */
class CleanBusinessLogsCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'logs:clean-business 
                            {--days=14 : 保留天数}
                            {--dry-run : 仅显示将要删除的文件，不实际删除}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理过期的业务日志文件';

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $retentionDays = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        $this->info('开始清理业务日志文件...');
        $this->info("保留天数: {$retentionDays}");

        if ($dryRun) {
            $this->warn('运行模式: 预览模式 (不会实际删除文件)');
        }

        $logPath = storage_path('logs');
        $cutoffDate = Carbon::now()->subDays($retentionDays);

        $this->info("删除 {$cutoffDate->toDateString()} 之前的日志文件");

        // 获取所有业务日志文件
        $businessLogFiles = $this->getBusinessLogFiles($logPath);

        if (empty($businessLogFiles)) {
            $this->info('没有找到业务日志文件');

            return 0;
        }

        $deletedCount = 0;
        $totalSize = 0;

        foreach ($businessLogFiles as $file) {
            $fileDate = $this->extractDateFromFilename($file);

            if ($fileDate && $fileDate->lt($cutoffDate)) {
                $fileSize = File::size($file);
                $totalSize += $fileSize;

                $this->line('将删除: '.basename($file).' ('.$this->formatBytes($fileSize).')');

                if (! $dryRun) {
                    try {
                        File::delete($file);
                        $deletedCount++;
                    } catch (\Exception $e) {
                        $this->error('删除文件失败: '.basename($file).' - '.$e->getMessage());
                    }
                } else {
                    $deletedCount++;
                }
            }
        }

        if ($deletedCount > 0) {
            $action = $dryRun ? '将删除' : '已删除';
            $this->info("{$action} {$deletedCount} 个日志文件，释放空间: ".$this->formatBytes($totalSize));
        } else {
            $this->info('没有需要删除的过期日志文件');
        }

        // 显示当前日志文件统计
        $this->showLogStatistics($logPath);

        return 0;
    }

    /**
     * 获取所有业务日志文件
     *
     * @return array<int, string> 日志文件路径列表
     */
    private function getBusinessLogFiles(string $logPath): array
    {
        if (! File::exists($logPath)) {
            return [];
        }

        $files = File::glob($logPath.'/business-*.log');

        // 按文件名排序
        sort($files);

        return $files;
    }

    /**
     * 从文件名中提取日期
     */
    private function extractDateFromFilename(string $filename): ?Carbon
    {
        $basename = basename($filename);

        // 匹配格式: business-YYYY-MM-DD.log
        if (preg_match('/business-(\d{4}-\d{2}-\d{2})\.log$/', $basename, $matches)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $matches[1]);

                return $date !== false ? $date : null;
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return round($bytes, 2).' '.$units[$unitIndex];
    }

    /**
     * 显示日志文件统计信息
     */
    private function showLogStatistics(string $logPath): void
    {
        $this->info("\n当前业务日志文件统计:");
        $this->line(str_repeat('-', 50));

        $businessLogFiles = $this->getBusinessLogFiles($logPath);

        if (empty($businessLogFiles)) {
            $this->line('没有业务日志文件');

            return;
        }

        $totalSize = 0;
        $fileCount = 0;

        foreach ($businessLogFiles as $file) {
            $fileSize = File::size($file);
            $totalSize += $fileSize;
            $fileCount++;

            $fileDate = $this->extractDateFromFilename($file);
            $dateStr = $fileDate ? $fileDate->toDateString() : '未知';

            $this->line(sprintf(
                '%-20s %10s %s',
                basename($file),
                $this->formatBytes($fileSize),
                $dateStr
            ));
        }

        $this->line(str_repeat('-', 50));
        $this->info("总计: {$fileCount} 个文件，{$this->formatBytes($totalSize)}");
    }
}

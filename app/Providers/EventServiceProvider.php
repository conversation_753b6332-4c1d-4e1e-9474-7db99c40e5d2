<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Database\Events\TransactionBeginning;
use Illuminate\Database\Events\TransactionCommitted;
use Illuminate\Database\Events\TransactionRolledBack;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use Laravel\Telescope\IncomingEntry;
use Laravel\Telescope\Telescope;

class EventServiceProvider extends ServiceProvider
{
    /** @var bool 是否已注册事务事件监听器 */
    private static bool $txnEventsRegistered = false;

    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        // 注册数据库事务事件监听（仅非生产环境）
        if (! app()->environment('production')) {
            $this->registerDatabaseTransactionEvents();
        }
    }

    /**
     * 注册数据库事务事件监听（开发环境）
     */
    private function registerDatabaseTransactionEvents(): void
    {
        // 避免在长驻进程（Octane/Queue）中重复注册
        if (self::$txnEventsRegistered) {
            return;
        }
        self::$txnEventsRegistered = true;

        // 监听事务开启
        Event::listen(TransactionBeginning::class, function (TransactionBeginning $event) {
            Telescope::recordQuery(
                IncomingEntry::make([
                    'sql' => 'START TRANSACTION',
                    'bindings' => [],
                    'time' => 0,
                    'connection' => $event->connectionName,
                ])
            );
        });

        // 监听事务提交
        Event::listen(TransactionCommitted::class, function (TransactionCommitted $event) {
            Telescope::recordQuery(
                IncomingEntry::make([
                    'sql' => 'COMMIT',
                    'bindings' => [],
                    'time' => 0,
                    'connection' => $event->connectionName,
                ])
            );
        });

        // 监听事务回滚
        Event::listen(TransactionRolledBack::class, function (TransactionRolledBack $event) {
            Telescope::recordQuery(
                IncomingEntry::make([
                    'sql' => 'ROLLBACK',
                    'bindings' => [],
                    'time' => 0,
                    'connection' => $event->connectionName,
                ])
            );
        });
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}

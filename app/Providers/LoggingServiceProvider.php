<?php

namespace App\Providers;

use App\Services\LogHealthMonitorService;
use App\Services\ResilientLoggerService;
use Illuminate\Support\ServiceProvider;

/**
 * 日志服务提供者
 */
class LoggingServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册容错日志服务
        $this->app->singleton(ResilientLoggerService::class, function ($app) {
            return new ResilientLoggerService;
        });

        // 注册日志健康监控服务
        $this->app->singleton(LogHealthMonitorService::class, function ($app) {
            return new LogHealthMonitorService;
        });
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 注册定时任务
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\LogHealthCheckCommand::class,
                \App\Console\Commands\TestErrorHandlingCommand::class,
                \App\Console\Commands\ValidateErrorResponseFormatCommand::class,
            ]);
        }
    }

    /**
     * 获取提供的服务
     */
    public function provides(): array
    {
        return [
            ResilientLoggerService::class,
            LogHealthMonitorService::class,
        ];
    }
}

<?php

namespace App\Providers;

use App\Contracts\RedisServiceInterface;
use App\Services\RedisService;
use Illuminate\Redis\RedisManager;
use Illuminate\Support\ServiceProvider;

/**
 * Redis 服务提供者
 *
 * 负责注册 Redis 服务的依赖注入绑定
 */
class RedisServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册 Redis 服务为单例
        $this->app->singleton(RedisServiceInterface::class, function ($app) {
            /** @var RedisManager $redisManager */
            $redisManager = $app['redis'];

            // 获取默认连接名称，可以通过配置文件自定义
            $connection = config('database.redis.default_connection', 'default');

            return new RedisService($redisManager, $connection);
        });

        // 注册别名，方便直接注入 RedisService 类
        $this->app->alias(RedisServiceInterface::class, RedisService::class);

        // 注册命名连接的 Redis 服务
        $this->registerNamedConnections();
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 发布配置文件（如果需要自定义配置）
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/redis.php' => config_path('redis.php'),
            ], 'redis-config');
        }
    }

    /**
     * 注册命名连接的 Redis 服务
     */
    protected function registerNamedConnections(): void
    {
        // 获取所有 Redis 连接配置
        $connections = config('database.redis', []);

        foreach ($connections as $name => $config) {
            // 跳过非连接配置项
            if (in_array($name, ['client', 'options', 'clusters'])) {
                continue;
            }

            // 为每个连接注册单独的服务实例
            $this->app->singleton("redis.service.{$name}", function ($app) use ($name) {
                /** @var RedisManager $redisManager */
                $redisManager = $app['redis'];

                return new RedisService($redisManager, $name);
            });
        }
    }

    /**
     * 获取服务提供者提供的服务
     *
     * @return array<string>
     */
    public function provides(): array
    {
        return [
            RedisServiceInterface::class,
            RedisService::class,
        ];
    }
}

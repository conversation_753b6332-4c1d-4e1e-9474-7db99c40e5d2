<?php

namespace App\Providers;

use App\Services\BusinessLogService;
use Illuminate\Support\ServiceProvider;

/**
 * 业务日志服务提供者
 */
class BusinessLogServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册业务日志服务为单例
        $this->app->singleton('business-log', function ($app) {
            return new BusinessLogService;
        });

        // 注册别名
        $this->app->alias('business-log', BusinessLogService::class);
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 注册命令
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\CleanBusinessLogsCommand::class,
            ]);
        }
    }
}

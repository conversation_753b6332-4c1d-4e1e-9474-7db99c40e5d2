<?php

namespace App\Providers;

use App\Repositories\ContactRepository;
use App\Repositories\ContactRepositoryInterface;
use App\Repositories\LeadContactRelationRepository;
use App\Repositories\LeadContactRelationRepositoryInterface;
use App\Repositories\LeadRepository;
use App\Repositories\LeadRepositoryInterface;
use App\Repositories\LeadUserRelationRepository;
use App\Repositories\LeadUserRelationRepositoryInterface;
use Illuminate\Support\ServiceProvider;

/**
 * 仓储服务提供者
 */
class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 线索相关仓储绑定
        $this->app->bind(LeadRepositoryInterface::class, LeadRepository::class);

        // 联系人相关仓储绑定
        $this->app->bind(ContactRepositoryInterface::class, ContactRepository::class);

        // 线索用户关联仓储绑定
        $this->app->bind(LeadUserRelationRepositoryInterface::class, LeadUserRelationRepository::class);

        // 线索联系人关联仓储绑定
        $this->app->bind(LeadContactRelationRepositoryInterface::class, LeadContactRelationRepository::class);
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        //
    }
}

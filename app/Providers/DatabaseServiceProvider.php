<?php

namespace App\Providers;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\Services\Database\EnhancedQueryBuilder;
use App\Services\Database\TransactionManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

/**
 * 数据库服务提供者
 *
 * 注册数据库操作优化组件的服务绑定
 */
class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册查询构建器服务
        $this->registerQueryBuilder();

        // 注册事务管理器服务
        $this->registerTransactionManager();

        // 注册配置
        $this->registerConfig();
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__.'/../../config/database-optimization.php' => config_path('database-optimization.php'),
        ], 'database-optimization-config');

        // 注册数据库查询监听器
        $this->registerQueryListeners();

        // 注册命令
        if ($this->app->runningInConsole()) {
            $this->registerCommands();
        }
    }

    /**
     * 注册查询构建器服务
     */
    protected function registerQueryBuilder(): void
    {
        $this->app->singleton(QueryBuilderInterface::class, function ($app) {
            return new EnhancedQueryBuilder;
        });

        // 注册别名
        $this->app->alias(QueryBuilderInterface::class, EnhancedQueryBuilder::class);
        $this->app->alias(QueryBuilderInterface::class, 'query.builder');
    }

    /**
     * 注册事务管理器服务
     */
    protected function registerTransactionManager(): void
    {
        $this->app->singleton(TransactionManagerInterface::class, function ($app) {
            return new TransactionManager;
        });

        // 注册别名
        $this->app->alias(TransactionManagerInterface::class, TransactionManager::class);
        $this->app->alias(TransactionManagerInterface::class, 'transaction.manager');
    }

    /**
     * 注册配置
     */
    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/../../config/database-optimization.php',
            'database-optimization'
        );
    }

    /**
     * 注册数据库查询监听器
     */
    protected function registerQueryListeners(): void
    {
        if (! config('database-optimization.monitoring.enabled', false)) {
            return;
        }

        DB::listen(function ($query) {
            $this->handleQueryExecution($query);
        });
    }

    /**
     * 处理查询执行
     *
     * @param  \Illuminate\Database\Events\QueryExecuted  $query
     */
    protected function handleQueryExecution($query): void
    {
        try {
            $executionTime = $query->time;
            $slowQueryThreshold = config('database-optimization.monitoring.slow_query_threshold', 1000);

            // 记录慢查询
            if ($executionTime > $slowQueryThreshold) {
                Log::warning('检测到慢查询', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $executionTime,
                    'connection' => $query->connectionName,
                ]);

                // 触发慢查询事件
                event('database.slow_query', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $executionTime,
                    'connection' => $query->connectionName,
                ]);
            }

            // 记录查询统计
            if (config('database-optimization.monitoring.collect_statistics', false)) {
                $this->collectQueryStatistics($query);
            }

        } catch (\Exception $e) {
            Log::error('查询监听器处理失败', [
                'error' => $e->getMessage(),
                'sql' => $query->sql ?? 'unknown',
            ]);
        }
    }

    /**
     * 收集查询统计信息
     *
     * @param  \Illuminate\Database\Events\QueryExecuted  $query
     */
    protected function collectQueryStatistics($query): void
    {
        try {
            $cacheKey = 'db_stats:'.date('Y-m-d-H');
            $stats = cache()->get($cacheKey, [
                'total_queries' => 0,
                'total_time' => 0,
                'slow_queries' => 0,
                'connections' => [],
                'query_types' => [],
            ]);

            // 更新统计信息
            $stats['total_queries']++;
            $stats['total_time'] += $query->time;

            // 统计连接使用情况
            $connection = $query->connectionName;
            $stats['connections'][$connection] = ($stats['connections'][$connection] ?? 0) + 1;

            // 统计查询类型
            $queryType = $this->getQueryType($query->sql);
            $stats['query_types'][$queryType] = ($stats['query_types'][$queryType] ?? 0) + 1;

            // 统计慢查询
            $slowQueryThreshold = config('database-optimization.monitoring.slow_query_threshold', 1000);
            if ($query->time > $slowQueryThreshold) {
                $stats['slow_queries']++;
            }

            // 缓存统计信息（1小时）
            cache()->put($cacheKey, $stats, 3600);

        } catch (\Exception $e) {
            Log::warning('收集查询统计信息失败', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 获取查询类型
     *
     * @param  string  $sql  SQL 语句
     */
    protected function getQueryType(string $sql): string
    {
        $sql = strtolower(trim($sql));

        if (str_starts_with($sql, 'select')) {
            return 'select';
        } elseif (str_starts_with($sql, 'insert')) {
            return 'insert';
        } elseif (str_starts_with($sql, 'update')) {
            return 'update';
        } elseif (str_starts_with($sql, 'delete')) {
            return 'delete';
        } elseif (str_starts_with($sql, 'create')) {
            return 'create';
        } elseif (str_starts_with($sql, 'alter')) {
            return 'alter';
        } elseif (str_starts_with($sql, 'drop')) {
            return 'drop';
        } else {
            return 'other';
        }
    }

    /**
     * 注册命令
     */
    protected function registerCommands(): void
    {
        $this->commands([
            // 这里可以注册相关的 Artisan 命令
            // \App\Console\Commands\DatabaseOptimizationCommand::class,
        ]);
    }

    /**
     * 获取服务提供者提供的服务
     *
     * @return array<string>
     */
    public function provides(): array
    {
        return [
            QueryBuilderInterface::class,
            TransactionManagerInterface::class,
            EnhancedQueryBuilder::class,
            TransactionManager::class,
            'query.builder',
            'transaction.manager',
        ];
    }
}

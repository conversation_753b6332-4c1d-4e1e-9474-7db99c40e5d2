<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * 处理失败日志的队列任务
 */
class ProcessFailedLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $maxExceptions = 3;

    public int $timeout = 30;

    private array $logData;

    private string $originalError;

    /**
     * 创建新的任务实例
     *
     * @param  array  $logData  原始日志数据
     * @param  string  $originalError  原始错误信息
     */
    public function __construct(array $logData, string $originalError)
    {
        $this->logData = $logData;
        $this->originalError = $originalError;

        // 设置队列和延迟
        $this->onQueue('logging');
        $this->delay(now()->addMinutes(5));
    }

    /**
     * 执行任务
     */
    public function handle(): void
    {
        try {
            // 尝试重新记录日志
            Log::channel('api')->info('API Request (Recovered)', array_merge($this->logData, [
                'recovered_at' => now()->toISOString(),
                'original_error' => $this->originalError,
                'recovery_attempt' => $this->attempts(),
            ]));

            // 记录恢复成功
            Log::channel('single')->info('Failed log recovered successfully', [
                'trace_id' => $this->logData['trace_id'] ?? 'unknown',
                'recovery_attempt' => $this->attempts(),
                'original_error' => $this->originalError,
            ]);

        } catch (Exception $e) {
            // 如果仍然失败，记录到系统日志
            Log::channel('single')->error('Failed to recover log after queue processing', [
                'trace_id' => $this->logData['trace_id'] ?? 'unknown',
                'attempt' => $this->attempts(),
                'original_error' => $this->originalError,
                'recovery_error' => $e->getMessage(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(Exception $exception): void
    {
        // 最终失败时，将日志保存到备用文件
        try {
            $backupPath = storage_path('logs/failed_logs_backup.log');
            $logEntry = [
                'timestamp' => now()->toISOString(),
                'original_data' => $this->logData,
                'original_error' => $this->originalError,
                'final_error' => $exception->getMessage(),
                'total_attempts' => $this->attempts(),
            ];

            file_put_contents(
                $backupPath,
                json_encode($logEntry).PHP_EOL,
                FILE_APPEND | LOCK_EX
            );

            // 记录最终失败
            Log::channel('single')->critical('Log recovery completely failed', [
                'trace_id' => $this->logData['trace_id'] ?? 'unknown',
                'total_attempts' => $this->attempts(),
                'backup_file' => $backupPath,
            ]);

        } catch (Exception $e) {
            // 连备份都失败时，使用 error_log
            error_log('Critical: Complete log system failure - '.json_encode([
                'trace_id' => $this->logData['trace_id'] ?? 'unknown',
                'error' => $exception->getMessage(),
            ]));
        }
    }

    /**
     * 计算重试延迟时间
     */
    public function retryAfter(): int
    {
        // 指数退避：第1次重试5分钟，第2次重试15分钟，第3次重试45分钟
        return pow(3, $this->attempts()) * 5 * 60;
    }
}

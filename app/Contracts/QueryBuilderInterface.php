<?php

namespace App\Contracts;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;

/**
 * 查询构建器接口
 *
 * 提供增强的查询构建功能，支持复杂条件构建、动态排序和查询优化
 */
interface QueryBuilderInterface
{
    /**
     * 构建复杂查询条件
     *
     * @param  array<string, mixed>  $conditions  查询条件数组
     * @param  Builder|QueryBuilder|null  $query  基础查询构建器，为空时创建新的
     * @return Builder|QueryBuilder 构建后的查询对象
     */
    public function buildComplexQuery(array $conditions, Builder|QueryBuilder|null $query = null): Builder|QueryBuilder;

    /**
     * 添加动态排序
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $sortRules  排序规则数组
     * @return Builder|QueryBuilder 添加排序后的查询对象
     */
    public function addDynamicSorting(Builder|QueryBuilder $query, array $sortRules): Builder|QueryBuilder;

    /**
     * 优化查询性能
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $options  优化选项
     * @return Builder|QueryBuilder 优化后的查询对象
     */
    public function optimizeQuery(Builder|QueryBuilder $query, array $options = []): Builder|QueryBuilder;

    /**
     * 构建范围查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $field  字段名
     * @param  mixed  $start  开始值
     * @param  mixed  $end  结束值
     * @param  string  $type  范围类型：'between', 'date_range', 'number_range'
     * @return Builder|QueryBuilder 添加范围条件后的查询对象
     */
    public function addRangeCondition(
        Builder|QueryBuilder $query,
        string $field,
        mixed $start,
        mixed $end,
        string $type = 'between'
    ): Builder|QueryBuilder;

    /**
     * 构建模糊匹配条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string>  $fields  搜索字段数组
     * @param  string  $keyword  搜索关键词
     * @param  string  $matchType  匹配类型：'like', 'full_text', 'exact'
     * @return Builder|QueryBuilder 添加搜索条件后的查询对象
     */
    public function addSearchCondition(
        Builder|QueryBuilder $query,
        array $fields,
        string $keyword,
        string $matchType = 'like'
    ): Builder|QueryBuilder;

    /**
     * 构建关联查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $relation  关联关系名
     * @param  array<string, mixed>  $conditions  关联条件
     * @return Builder|QueryBuilder 添加关联条件后的查询对象
     */
    public function addRelationCondition(
        Builder|QueryBuilder $query,
        string $relation,
        array $conditions
    ): Builder|QueryBuilder;

    /**
     * 获取查询优化建议
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed> 优化建议数组
     */
    public function getOptimizationSuggestions(Builder|QueryBuilder $query): array;

    /**
     * 分析查询执行计划
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed> 执行计划分析结果
     */
    public function analyzeQueryPlan(Builder|QueryBuilder $query): array;

    /**
     * 构建分页查询
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  int  $page  页码
     * @param  int  $perPage  每页数量
     * @param  array<string>  $columns  查询字段
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator 分页结果
     */
    public function buildPaginatedQuery(
        Builder|QueryBuilder $query,
        int $page = 1,
        int $perPage = 15,
        array $columns = ['*']
    ): \Illuminate\Contracts\Pagination\LengthAwarePaginator;

    /**
     * 构建聚合查询
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $aggregations  聚合配置
     * @return array<string, mixed> 聚合结果
     */
    public function buildAggregateQuery(Builder|QueryBuilder $query, array $aggregations): array;

    /**
     * 构建批量查询
     *
     * @param  array<array<string, mixed>>  $conditions  多个查询条件
     * @param  string  $model  模型类名
     * @return array<int, \Illuminate\Support\Collection> 批量查询结果
     */
    public function buildBatchQuery(array $conditions, string $model): array;

    /**
     * 验证查询条件
     *
     * @param  array<string, mixed>  $conditions  查询条件
     * @param  array<string>  $allowedFields  允许的字段列表
     * @return bool 验证结果
     *
     * @throws \InvalidArgumentException 当条件无效时抛出异常
     */
    public function validateConditions(array $conditions, array $allowedFields = []): bool;

    /**
     * 获取查询统计信息
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed> 统计信息
     */
    public function getQueryStatistics(Builder|QueryBuilder $query): array;

    /**
     * 设置查询缓存
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  int  $ttl  缓存时间（秒）
     * @param  string|null  $cacheKey  缓存键，为空时自动生成
     * @return Builder|QueryBuilder 设置缓存后的查询对象
     */
    public function setCacheForQuery(
        Builder|QueryBuilder $query,
        int $ttl = 3600,
        ?string $cacheKey = null
    ): Builder|QueryBuilder;

    /**
     * 清除查询缓存
     *
     * @param  string|array<string>  $cacheKeys  缓存键或缓存键数组
     * @return bool 清除结果
     */
    public function clearQueryCache(string|array $cacheKeys): bool;
}

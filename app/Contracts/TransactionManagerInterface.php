<?php

namespace App\Contracts;

/**
 * 事务管理器接口
 *
 * 提供增强的事务管理功能，支持嵌套事务、超时控制和死锁检测
 */
interface TransactionManagerInterface
{
    /**
     * 在事务中执行回调函数
     *
     * @param  callable(): mixed  $callback  要执行的回调函数
     * @param  int  $attempts  重试次数，默认为1
     * @param  int  $timeout  事务超时时间（秒），默认为30秒
     * @return mixed 回调函数的返回值
     *
     * @throws \App\Exceptions\TransactionException 当事务执行失败时抛出异常
     */
    public function executeInTransaction(callable $callback, int $attempts = 1, int $timeout = 30): mixed;

    /**
     * 开始嵌套事务
     *
     * @param  string|null  $savepointName  保存点名称，为空时自动生成
     * @return string 保存点名称
     *
     * @throws \App\Exceptions\TransactionException 当无法创建保存点时抛出异常
     */
    public function beginNestedTransaction(?string $savepointName = null): string;

    /**
     * 提交嵌套事务
     *
     * @param  string  $savepointName  保存点名称
     * @return bool 提交结果
     *
     * @throws \App\Exceptions\TransactionException 当提交失败时抛出异常
     */
    public function commitNestedTransaction(string $savepointName): bool;

    /**
     * 回滚嵌套事务
     *
     * @param  string  $savepointName  保存点名称
     * @return bool 回滚结果
     *
     * @throws \App\Exceptions\TransactionException 当回滚失败时抛出异常
     */
    public function rollbackNestedTransaction(string $savepointName): bool;

    /**
     * 释放保存点
     *
     * @param  string  $savepointName  保存点名称
     * @return bool 释放结果
     */
    public function releaseSavepoint(string $savepointName): bool;

    /**
     * 检查是否在事务中
     *
     * @return bool 是否在事务中
     */
    public function inTransaction(): bool;

    /**
     * 获取当前事务级别
     *
     * @return int 事务级别，0表示不在事务中
     */
    public function getTransactionLevel(): int;

    /**
     * 设置事务隔离级别
     *
     * @param  string  $level  隔离级别：'READ_UNCOMMITTED', 'READ_COMMITTED', 'REPEATABLE_READ', 'SERIALIZABLE'
     * @return bool 设置结果
     *
     * @throws \App\Exceptions\TransactionException 当设置失败时抛出异常
     */
    public function setIsolationLevel(string $level): bool;

    /**
     * 获取当前事务隔离级别
     *
     * @return string 当前隔离级别
     */
    public function getIsolationLevel(): string;

    /**
     * 检测死锁并重试
     *
     * @param  callable(): mixed  $callback  要执行的回调函数
     * @param  int  $maxRetries  最大重试次数
     * @param  int  $retryDelay  重试延迟时间（毫秒）
     * @return mixed 回调函数的返回值
     *
     * @throws \App\Exceptions\TransactionException 当重试次数用尽时抛出异常
     */
    public function executeWithDeadlockRetry(callable $callback, int $maxRetries = 3, int $retryDelay = 100): mixed;

    /**
     * 批量执行事务
     *
     * @param  array<callable(): mixed>  $callbacks  回调函数数组
     * @param  bool  $stopOnError  遇到错误时是否停止执行
     * @return array<int, mixed> 执行结果数组
     *
     * @throws \App\Exceptions\TransactionException 当批量执行失败时抛出异常
     */
    public function executeBatchTransactions(array $callbacks, bool $stopOnError = true): array;

    /**
     * 获取事务统计信息
     *
     * @return array<string, mixed> 统计信息
     */
    public function getTransactionStatistics(): array;

    /**
     * 设置事务超时时间
     *
     * @param  int  $timeout  超时时间（秒）
     * @return bool 设置结果
     */
    public function setTimeout(int $timeout): bool;

    /**
     * 获取事务超时时间
     *
     * @return int 超时时间（秒）
     */
    public function getTimeout(): int;

    /**
     * 强制回滚所有活动事务
     *
     * @return bool 回滚结果
     */
    public function forceRollbackAll(): bool;

    /**
     * 获取活动保存点列表
     *
     * @return array<string> 保存点名称数组
     */
    public function getActiveSavepoints(): array;

    /**
     * 清理过期的保存点
     *
     * @param  int  $maxAge  最大存活时间（秒）
     * @return int 清理的保存点数量
     */
    public function cleanupExpiredSavepoints(int $maxAge = 300): int;

    /**
     * 注册事务回调
     *
     * @param  string  $event  事件类型：'before_begin', 'after_commit', 'after_rollback'
     * @param  callable(): mixed  $callback  回调函数
     * @return bool 注册结果
     */
    public function registerCallback(string $event, callable $callback): bool;

    /**
     * 移除事务回调
     *
     * @param  string  $event  事件类型
     * @param  (callable(): mixed)|null  $callback  回调函数，为空时移除该事件的所有回调
     * @return bool 移除结果
     */
    public function removeCallback(string $event, ?callable $callback = null): bool;

    /**
     * 获取事务执行历史
     *
     * @param  int  $limit  限制数量
     * @return array<array<string, mixed>> 执行历史
     */
    public function getTransactionHistory(int $limit = 100): array;

    /**
     * 清除事务执行历史
     *
     * @return bool 清除结果
     */
    public function clearTransactionHistory(): bool;
}

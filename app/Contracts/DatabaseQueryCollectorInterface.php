<?php

namespace App\Contracts;

/**
 * 数据库查询收集器接口
 *
 * 定义了查询收集器的基本功能，包括开始/停止收集、获取统计信息等
 */
interface DatabaseQueryCollectorInterface
{
    /**
     * 开始收集数据库查询
     */
    public function startCollection(): void;

    /**
     * 停止收集数据库查询
     */
    public function stopCollection(): void;

    /**
     * 获取查询统计信息
     *
     * @return array<string, mixed> 包含查询统计信息的数组
     */
    public function getQueryStatistics(): array;

    /**
     * 重置收集器状态，清除当前请求的数据
     */
    public function reset(): void;
}

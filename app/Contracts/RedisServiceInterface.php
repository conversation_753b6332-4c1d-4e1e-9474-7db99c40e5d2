<?php

namespace App\Contracts;

/**
 * Redis 服务接口
 *
 * 定义 Redis 操作的基本功能，包括键值对操作、过期时间设置、批量操作等
 */
interface RedisServiceInterface
{
    /**
     * 设置键值对
     *
     * @param  string  $key  键名
     * @param  mixed  $value  值（将自动序列化为 JSON）
     * @param  int|null  $ttl  过期时间（秒），null 表示永不过期
     * @return bool 操作是否成功
     */
    public function set(string $key, mixed $value, ?int $ttl = null): bool;

    /**
     * 获取键对应的值
     *
     * @param  string  $key  键名
     * @param  mixed  $default  默认值
     * @return mixed 反序列化后的值或默认值
     */
    public function get(string $key, mixed $default = null): mixed;

    /**
     * 删除指定键
     *
     * @param  string  $key  键名
     * @return bool 操作是否成功
     */
    public function delete(string $key): bool;

    /**
     * 检查键是否存在
     *
     * @param  string  $key  键名
     * @return bool 键是否存在
     */
    public function exists(string $key): bool;

    /**
     * 设置键的过期时间
     *
     * @param  string  $key  键名
     * @param  int  $ttl  过期时间（秒）
     * @return bool 操作是否成功
     */
    public function expire(string $key, int $ttl): bool;

    /**
     * 获取键的剩余过期时间
     *
     * @param  string  $key  键名
     * @return int 剩余过期时间（秒），-1 表示永不过期，-2 表示键不存在
     */
    public function ttl(string $key): int;

    /**
     * 批量设置键值对
     *
     * @param  array<string, mixed>  $data  键值对数组
     * @param  int|null  $ttl  过期时间（秒），null 表示永不过期
     * @return bool 操作是否成功
     */
    public function setMultiple(array $data, ?int $ttl = null): bool;

    /**
     * 批量获取键对应的值
     *
     * @param  array<string>  $keys  键名数组
     * @return array<string, mixed> 键值对数组
     */
    public function getMultiple(array $keys): array;

    /**
     * 批量删除键
     *
     * @param  array<string>  $keys  键名数组
     * @return int 成功删除的键数量
     */
    public function deleteMultiple(array $keys): int;

    /**
     * 清空当前数据库的所有键
     *
     * @return bool 操作是否成功
     */
    public function flush(): bool;

    /**
     * 获取匹配模式的所有键
     *
     * @param  string  $pattern  匹配模式（支持通配符 * 和 ?）
     * @return array<string> 匹配的键名数组
     */
    public function keys(string $pattern): array;

    /**
     * 原子性递增操作
     *
     * @param  string  $key  键名
     * @param  int  $increment  递增值，默认为 1
     * @return int 递增后的值
     */
    public function increment(string $key, int $increment = 1): int;

    /**
     * 原子性递减操作
     *
     * @param  string  $key  键名
     * @param  int  $decrement  递减值，默认为 1
     * @return int 递减后的值
     */
    public function decrement(string $key, int $decrement = 1): int;

    /**
     * 获取 Redis 连接信息
     *
     * @return array<string, mixed> 连接信息
     */
    public function getConnectionInfo(): array;

    /**
     * 测试 Redis 连接
     *
     * @return bool 连接是否正常
     */
    public function ping(): bool;
}

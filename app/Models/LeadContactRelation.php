<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 线索联系人关联模型
 *
 * @property int $id
 * @property int $lead_id 线索ID
 * @property int $contact_id 联系人ID
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class LeadContactRelation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'crm_lead_contact_relation';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'lead_id',
        'contact_id',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'lead_id' => 'integer',
        'contact_id' => 'integer',
        'created_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 不使用updated_at字段
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 创建时间字段
     *
     * @var string
     */
    const CREATED_AT = 'created_at';

    /**
     * 线索关联
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class, 'lead_id');
    }

    /**
     * 联系人关联
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class, 'contact_id');
    }
}

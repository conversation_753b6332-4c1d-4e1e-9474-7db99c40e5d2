<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 线索用户关联模型
 *
 * @property int $id
 * @property int $lead_id 线索ID
 * @property int $user_id 用户ID
 * @property int|null $role_type 角色类型
 * @property int|null $is_primary 是否主负责人
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class LeadUserRelation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'crm_lead_user_relation';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'lead_id',
        'user_id',
        'role_type',
        'is_primary',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'lead_id' => 'integer',
        'user_id' => 'integer',
        'role_type' => 'integer',
        'is_primary' => 'integer',
        'created_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 不使用updated_at字段
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 创建时间字段
     *
     * @var string
     */
    const CREATED_AT = 'created_at';

    /**
     * 线索关联
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class, 'lead_id');
    }

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 联系人模型
 *
 * @property int $id
 * @property string $name 姓名
 * @property string|null $gender 性别
 * @property int|null $age 年龄
 * @property string|null $mobile 手机
 * @property string|null $telephone 座机
 * @property string|null $email 邮箱
 * @property string|null $wx 微信
 * @property string|null $department 所属部门
 * @property string|null $position 职位
 * @property string|null $remark 备注
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class Contact extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'crm_contact';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'gender',
        'age',
        'mobile',
        'telephone',
        'email',
        'wx',
        'department',
        'position',
        'remark',
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'age' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联的线索（多对多）
     */
    public function leads(): BelongsToMany
    {
        return $this->belongsToMany(Lead::class, 'crm_lead_contact_relation', 'contact_id', 'lead_id')
            ->withPivot('created_at');
    }
}

<?php

namespace App\Logging;

use Illuminate\Support\Str;
use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

/**
 * 业务日志格式化器
 *
 * 将业务日志格式化为 JSON 格式，包含 trace_id 等必要字段
 */
class BusinessLogFormatter extends JsonFormatter
{
    /**
     * 格式化日志记录
     */
    public function format(LogRecord $record): string
    {
        // 获取基础格式化数据
        $data = $this->normalize($record);

        // 确保包含 trace_id
        if (! isset($data['context']['trace_id'])) {
            $data['context']['trace_id'] = $this->generateTraceId();
        }

        // 重新组织数据结构，使其更适合业务日志
        $businessLog = [
            'timestamp' => $data['datetime'],
            'level' => $data['level_name'],
            'trace_id' => $data['context']['trace_id'],
            'message' => $data['message'],
            'context' => $this->sanitizeContext($data['context']),
            'extra' => $data['extra'] ?? [],
        ];

        // 添加请求相关信息（如果存在）
        if (app()->bound('request') && app('request')) {
            $request = app('request');
            $businessLog['request'] = [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            // 添加用户信息（如果已认证）
            if ($request->user()) {
                $businessLog['user'] = [
                    'id' => $request->user()->id,
                    'email' => $request->user()->email ?? null,
                ];
            }
        }

        return $this->toJson($businessLog, true)."\n";
    }

    /**
     * 生成 trace_id
     */
    private function generateTraceId(): string
    {
        return Str::uuid()->toString();
    }

    /**
     * 清理上下文数据，移除敏感信息
     */
    private function sanitizeContext(array $context): array
    {
        $sensitiveKeys = [
            'password',
            'password_confirmation',
            'token',
            'secret',
            'key',
            'authorization',
            'cookie',
            'x-api-key',
        ];

        return $this->recursiveSanitize($context, $sensitiveKeys);
    }

    /**
     * 递归清理敏感数据
     */
    private function recursiveSanitize(array $data, array $sensitiveKeys): array
    {
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);

            // 检查是否为敏感字段
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (str_contains($lowerKey, $sensitiveKey)) {
                    $data[$key] = '[REDACTED]';

                    continue 2;
                }
            }

            // 递归处理数组
            if (is_array($value)) {
                $data[$key] = $this->recursiveSanitize($value, $sensitiveKeys);
            }
        }

        return $data;
    }
}

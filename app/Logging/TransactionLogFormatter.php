<?php

namespace App\Logging;

use Illuminate\Support\Str;
use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

/**
 * 事务日志格式化器
 *
 * 专门用于格式化事务相关的日志，提供结构化的事务日志格式
 */
class TransactionLogFormatter extends JsonFormatter
{
    /**
     * 格式化日志记录
     */
    public function format(LogRecord $record): string
    {
        // 获取基础格式化数据
        $data = $this->normalize($record);

        // 确保 data 是数组
        if (! is_array($data)) {
            $data = [];
        }

        // 确保 context 存在且是数组
        if (! isset($data['context']) || ! is_array($data['context'])) {
            $data['context'] = [];
        }

        // 确保包含 trace_id
        if (! isset($data['context']['trace_id'])) {
            $data['context']['trace_id'] = $this->generateTraceId();
        }

        // 构建事务日志结构
        $transactionLog = [
            'timestamp' => $data['datetime'],
            'level' => $data['level_name'],
            'message' => $data['message'],
            'transaction_id' => $data['context']['transaction_id'] ?? null,
            'trace_id' => $data['context']['trace_id'],
            'operation_type' => $data['context']['operation_type'] ?? 'unknown',
            'user_id' => $data['context']['user_id'] ?? null,
            'connection' => $data['context']['connection'] ?? 'mysql',
            'context' => $this->sanitizeContext($data['context']),
        ];

        // 添加性能指标（支持新旧格式）
        $hasDuration = isset($data['context']['duration']) || isset($data['context']['duration_ms']);
        $hasMemory = isset($data['context']['memory_usage']) || isset($data['context']['memory_usage_mb']);

        if ($hasDuration || $hasMemory) {
            $performance = [];

            // 处理执行时间（优先使用格式化后的值）
            if (isset($data['context']['duration'])) {
                $performance['duration'] = $data['context']['duration'];
            } elseif (isset($data['context']['duration_ms'])) {
                $performance['duration_ms'] = $data['context']['duration_ms'];
            }

            // 处理内存使用（优先使用格式化后的值）
            if (isset($data['context']['memory_usage'])) {
                $performance['memory_usage'] = $data['context']['memory_usage'];
            } elseif (isset($data['context']['memory_usage_mb'])) {
                $performance['memory_usage_mb'] = $data['context']['memory_usage_mb'];
            }

            // 处理峰值内存
            if (isset($data['context']['memory_peak'])) {
                $performance['memory_peak'] = $data['context']['memory_peak'];
            } elseif (isset($data['context']['peak_memory_mb'])) {
                $performance['peak_memory_mb'] = $data['context']['peak_memory_mb'];
            }

            // 处理内存差值
            if (isset($data['context']['memory_diff'])) {
                $performance['memory_diff'] = $data['context']['memory_diff'];
            } elseif (isset($data['context']['memory_diff_mb'])) {
                $performance['memory_diff_mb'] = $data['context']['memory_diff_mb'];
            }

            $transactionLog['performance'] = $performance;
        }

        // 添加错误信息（如果是错误日志）
        if ($data['level'] >= 400) { // ERROR 级别及以上
            $transactionLog['error'] = [
                'type' => $data['context']['exception_class'] ?? $data['context']['error_type'] ?? 'unknown',
                'message' => $data['context']['exception_message'] ?? $data['context']['error_message'] ?? '',
                'code' => $data['context']['exception_code'] ?? $data['context']['error_code'] ?? 0,
                'file' => $data['context']['file'] ?? null,
                'line' => $data['context']['line'] ?? null,
            ];
        }

        // 添加请求信息（如果存在）
        if (app()->bound('request')) {
            $request = app('request');
            $transactionLog['request'] = [
                'method' => $request->method(),
                'path' => $request->path(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];
        }

        return $this->toJson($transactionLog, true)."\n";
    }

    /**
     * 生成 trace_id
     */
    private function generateTraceId(): string
    {
        return Str::uuid()->toString();
    }

    /**
     * 清理上下文数据
     *
     * @param  array<string, mixed>  $context  原始上下文数据
     * @return array<string, mixed> 清理后的上下文数据
     */
    private function sanitizeContext(array $context): array
    {
        // 移除已经提取到顶级字段的数据
        $excludeKeys = [
            'transaction_id', 'trace_id', 'operation_type', 'user_id',
            'connection',
            // 旧格式字段
            'duration_ms', 'memory_usage_mb', 'peak_memory_mb', 'memory_diff_mb',
            // 新格式字段
            'duration', 'memory_usage', 'memory_peak', 'memory_diff',
            'exception_class', 'exception_message', 'exception_code',
            'error_type', 'error_message', 'error_code', 'file', 'line',
        ];

        $sanitized = [];
        foreach ($context as $key => $value) {
            if (! in_array($key, $excludeKeys)) {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }
}

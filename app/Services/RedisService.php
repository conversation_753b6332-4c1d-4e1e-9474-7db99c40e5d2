<?php

namespace App\Services;

use App\Contracts\RedisServiceInterface;
use Illuminate\Redis\RedisManager;
use Illuminate\Support\Facades\Log;
use JsonException;
use RedisException;

/**
 * Redis 服务实现类
 *
 * 提供 Redis 操作的具体实现，包括数据序列化、异常处理和日志记录
 */
class RedisService implements RedisServiceInterface
{
    /**
     * Redis 管理器实例
     */
    protected RedisManager $redis;

    /**
     * 默认连接名称
     */
    protected string $connection;

    /**
     * 构造函数
     *
     * @param  RedisManager  $redis  Redis 管理器
     * @param  string  $connection  连接名称，默认为 'default'
     */
    public function __construct(RedisManager $redis, string $connection = 'default')
    {
        $this->redis = $redis;
        $this->connection = $connection;
    }

    /**
     * 设置键值对
     *
     * @param  string  $key  键名
     * @param  mixed  $value  值（将自动序列化为 JSON）
     * @param  int|null  $ttl  过期时间（秒），null 表示永不过期
     * @return bool 操作是否成功
     */
    public function set(string $key, mixed $value, ?int $ttl = null): bool
    {
        try {
            $serializedValue = $this->serialize($value);
            $connection = $this->getConnection();

            if ($ttl !== null && $ttl > 0) {
                return $connection->setex($key, $ttl, $serializedValue);
            }

            return $connection->set($key, $serializedValue);
        } catch (RedisException|JsonException $e) {
            $this->logError('Failed to set key', [
                'key' => $key,
                'ttl' => $ttl,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 获取键对应的值
     *
     * @param  string  $key  键名
     * @param  mixed  $default  默认值
     * @return mixed 反序列化后的值或默认值
     */
    public function get(string $key, mixed $default = null): mixed
    {
        try {
            $connection = $this->getConnection();
            $value = $connection->get($key);

            if ($value === null || $value === false) {
                return $default;
            }

            return $this->unserialize($value);
        } catch (RedisException|JsonException $e) {
            $this->logError('Failed to get key', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return $default;
        }
    }

    /**
     * 删除指定键
     *
     * @param  string  $key  键名
     * @return bool 操作是否成功
     */
    public function delete(string $key): bool
    {
        try {
            $connection = $this->getConnection();

            return $connection->del($key) > 0;
        } catch (RedisException $e) {
            $this->logError('Failed to delete key', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 检查键是否存在
     *
     * @param  string  $key  键名
     * @return bool 键是否存在
     */
    public function exists(string $key): bool
    {
        try {
            $connection = $this->getConnection();

            return $connection->exists($key) > 0;
        } catch (RedisException $e) {
            $this->logError('Failed to check key existence', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param  string  $key  键名
     * @param  int  $ttl  过期时间（秒）
     * @return bool 操作是否成功
     */
    public function expire(string $key, int $ttl): bool
    {
        try {
            $connection = $this->getConnection();

            return $connection->expire($key, $ttl);
        } catch (RedisException $e) {
            $this->logError('Failed to set key expiration', [
                'key' => $key,
                'ttl' => $ttl,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param  string  $key  键名
     * @return int 剩余过期时间（秒），-1 表示永不过期，-2 表示键不存在
     */
    public function ttl(string $key): int
    {
        try {
            $connection = $this->getConnection();

            return $connection->ttl($key);
        } catch (RedisException $e) {
            $this->logError('Failed to get key TTL', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);

            return -2; // 返回键不存在的状态
        }
    }

    /**
     * 批量设置键值对
     *
     * @param  array<string, mixed>  $data  键值对数组
     * @param  int|null  $ttl  过期时间（秒），null 表示永不过期
     * @return bool 操作是否成功
     */
    public function setMultiple(array $data, ?int $ttl = null): bool
    {
        if (empty($data)) {
            return true;
        }

        try {
            $connection = $this->getConnection();
            $serializedData = [];

            foreach ($data as $key => $value) {
                $serializedData[$key] = $this->serialize($value);
            }

            $result = $connection->mset($serializedData);

            // 如果设置了过期时间，需要为每个键单独设置
            if ($result && $ttl !== null && $ttl > 0) {
                foreach (array_keys($data) as $key) {
                    $connection->expire($key, $ttl);
                }
            }

            return $result;
        } catch (RedisException|JsonException $e) {
            $this->logError('Failed to set multiple keys', [
                'keys' => array_keys($data),
                'ttl' => $ttl,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 批量获取键对应的值
     *
     * @param  array<string>  $keys  键名数组
     * @return array<string, mixed> 键值对数组
     */
    public function getMultiple(array $keys): array
    {
        if (empty($keys)) {
            return [];
        }

        try {
            $connection = $this->getConnection();
            $values = $connection->mget($keys);
            $result = [];

            foreach ($keys as $index => $key) {
                $value = $values[$index] ?? null;
                if ($value !== null && $value !== false) {
                    $result[$key] = $this->unserialize($value);
                } else {
                    $result[$key] = null;
                }
            }

            return $result;
        } catch (RedisException|JsonException $e) {
            $this->logError('Failed to get multiple keys', [
                'keys' => $keys,
                'error' => $e->getMessage(),
            ]);

            return array_fill_keys($keys, null);
        }
    }

    /**
     * 批量删除键
     *
     * @param  array<string>  $keys  键名数组
     * @return int 成功删除的键数量
     */
    public function deleteMultiple(array $keys): int
    {
        if (empty($keys)) {
            return 0;
        }

        try {
            $connection = $this->getConnection();

            return $connection->del(...$keys);
        } catch (RedisException $e) {
            $this->logError('Failed to delete multiple keys', [
                'keys' => $keys,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * 清空当前数据库的所有键
     *
     * @return bool 操作是否成功
     */
    public function flush(): bool
    {
        try {
            $connection = $this->getConnection();

            return $connection->flushdb();
        } catch (RedisException $e) {
            $this->logError('Failed to flush database', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 获取匹配模式的所有键
     *
     * @param  string  $pattern  匹配模式（支持通配符 * 和 ?）
     * @return array<string> 匹配的键名数组
     */
    public function keys(string $pattern): array
    {
        try {
            $connection = $this->getConnection();

            return $connection->keys($pattern);
        } catch (RedisException $e) {
            $this->logError('Failed to get keys by pattern', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 原子性递增操作
     *
     * @param  string  $key  键名
     * @param  int  $increment  递增值，默认为 1
     * @return int 递增后的值
     */
    public function increment(string $key, int $increment = 1): int
    {
        try {
            $connection = $this->getConnection();

            return $connection->incrby($key, $increment);
        } catch (RedisException $e) {
            $this->logError('Failed to increment key', [
                'key' => $key,
                'increment' => $increment,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * 原子性递减操作
     *
     * @param  string  $key  键名
     * @param  int  $decrement  递减值，默认为 1
     * @return int 递减后的值
     */
    public function decrement(string $key, int $decrement = 1): int
    {
        try {
            $connection = $this->getConnection();

            return $connection->decrby($key, $decrement);
        } catch (RedisException $e) {
            $this->logError('Failed to decrement key', [
                'key' => $key,
                'decrement' => $decrement,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    /**
     * 获取 Redis 连接信息
     *
     * @return array<string, mixed> 连接信息
     */
    public function getConnectionInfo(): array
    {
        try {
            $connection = $this->getConnection();
            $info = $connection->info();

            return [
                'connection' => $this->connection,
                'server_info' => $info['server'] ?? [],
                'memory_info' => $info['memory'] ?? [],
                'clients_info' => $info['clients'] ?? [],
            ];
        } catch (RedisException $e) {
            $this->logError('Failed to get connection info', [
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * 测试 Redis 连接
     *
     * @return bool 连接是否正常
     */
    public function ping(): bool
    {
        try {
            $connection = $this->getConnection();
            $response = $connection->ping();

            return $response === 'PONG' || $response === true;
        } catch (RedisException $e) {
            $this->logError('Redis ping failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 获取 Redis 连接实例
     *
     * @return mixed Redis 连接实例
     */
    protected function getConnection(): mixed
    {
        return $this->redis->connection($this->connection);
    }

    /**
     * 序列化数据为 JSON 字符串
     *
     * @param  mixed  $value  要序列化的值
     * @return string JSON 字符串
     *
     * @throws JsonException 序列化失败时抛出异常
     */
    protected function serialize(mixed $value): string
    {
        if (is_string($value)) {
            return $value;
        }

        return json_encode($value, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 反序列化 JSON 字符串为原始数据
     *
     * @param  string  $value  JSON 字符串
     * @return mixed 反序列化后的值
     *
     * @throws JsonException 反序列化失败时抛出异常
     */
    protected function unserialize(string $value): mixed
    {
        // 尝试解析为 JSON，如果失败则返回原始字符串
        $decoded = json_decode($value, true, 512, JSON_THROW_ON_ERROR);

        // 如果解析结果与原始值相同，说明原始值就是字符串
        if (json_encode($decoded, JSON_UNESCAPED_UNICODE) === $value) {
            return $decoded;
        }

        return $value;
    }

    /**
     * 记录错误日志
     *
     * @param  string  $message  错误消息
     * @param  array<string, mixed>  $context  上下文信息
     */
    protected function logError(string $message, array $context = []): void
    {
        Log::error('[RedisService] '.$message, array_merge($context, [
            'connection' => $this->connection,
        ]));
    }
}

<?php

namespace App\Services;

use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;
use App\DTOs\Lead\LeadCreateDTO;
use App\DTOs\Lead\LeadListDTO;
use App\DTOs\Lead\LeadUpdateDTO;
use App\Exceptions\BusinessException;
use App\Exceptions\TransactionException;
use App\Models\Lead;
use App\Repositories\LeadRepositoryInterface;
use App\Services\Transaction\SimpleTransactionLogger;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 线索业务逻辑服务
 *
 * 集成数据库操作优化组件，提供高性能的业务逻辑处理
 */
class LeadService
{
    /**
     * 线索仓储接口
     */
    protected LeadRepositoryInterface $leadRepository;

    /**
     * 线索用户关联服务
     */
    protected LeadUserRelationService $leadUserRelationService;

    /**
     * 线索联系人关联服务
     */
    protected LeadContactRelationService $leadContactRelationService;

    /**
     * 查询构建器实例
     */
    protected QueryBuilderInterface $queryBuilder;

    /**
     * 事务管理器实例
     */
    protected TransactionManagerInterface $transactionManager;

    /**
     * 构造函数
     */
    public function __construct(
        LeadRepositoryInterface $leadRepository,
        LeadUserRelationService $leadUserRelationService,
        LeadContactRelationService $leadContactRelationService,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager,

    ) {
        $this->leadRepository = $leadRepository;
        $this->leadUserRelationService = $leadUserRelationService;
        $this->leadContactRelationService = $leadContactRelationService;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;

        // 注册事务日志回调
        SimpleTransactionLogger::registerCallbacks($this->transactionManager);
    }

    /**
     * 获取线索分页列表
     *
     * @param  LeadListDTO  $dto  查询条件DTO
     */
    public function getLeadsList(LeadListDTO $dto): LengthAwarePaginator
    {
        return $this->leadRepository->getLeadsList($dto);
    }

    /**
     * 根据ID获取线索详情
     *
     * @param  int  $id  线索ID
     * @return mixed
     *
     * @throws BusinessException
     */
    public function getLeadById(int $id)
    {
        $lead = $this->leadRepository->findById($id);

        if (! $lead) {
            throw BusinessException::fromErrorCode('Lead.not_found');
        }

        return $lead;
    }

    /**
     * 创建线索（使用事务管理）
     *
     * @param  LeadCreateDTO  $dto  线索创建数据传输对象
     *
     * @throws TransactionException
     */
    public function createLead(LeadCreateDTO $dto): mixed
    {
        return SimpleTransactionLogger::logBusinessTransaction(
            function () use ($dto) {
                return $this->transactionManager->executeInTransaction(function () use ($dto) {
                    // 1. 创建线索记录
                    $lead = $this->leadRepository->create($dto->toArray());
                    DB::table('daa')->insert(['a' => 1]);

                    return $lead;
                });
            },
            'Lead',
            'create',
            $dto->toArray(),
            ['company_name' => $dto->companyFullName]
        );
    }

    /**
     * 更新线索（使用事务管理）
     *
     * @param  int  $id  线索ID
     * @param  LeadUpdateDTO  $dto  线索更新数据传输对象
     *
     * @throws BusinessException
     */
    public function updateLead(int $id, LeadUpdateDTO $dto): Lead
    {
        return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
            // 检查线索是否存在（业务规则验证）
            $lead = $this->leadRepository->findById($id);
            if (! $lead) {
                throw BusinessException::fromErrorCode('Lead.not_found');
            }

            // 如果更新公司名称，检查重复（业务规则验证）
            if ($dto->isCompanyNameUpdated() &&
                $dto->companyFullName !== null &&
                $dto->companyFullName !== $lead->company_full_name) {
                if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
                    throw BusinessException::fromErrorCode('Lead.company_name_exists');
                }
            }

            $updated = $this->leadRepository->update($id, $dto->toArray());
            if (! $updated) {
                throw BusinessException::fromErrorCode('Lead.update_failed');
            }

            // 优化：直接在原始记录上应用更新的字段，避免重复查询
            $updatedData = $dto->toArray();
            foreach ($updatedData as $key => $value) {
                $lead->setAttribute($key, $value);
            }

            // 注意：关联数据已在 findById 时预加载，无需重新加载
            // 移除 load() 调用以避免重复查询

            Log::info('线索更新成功', [
                'lead_id' => $id,
                'updated_fields' => $dto->getUpdatedFields(),
            ]);

            return $lead;
        });
    }

    /**
     * 删除线索（使用事务管理）
     *
     * @param  int  $id  线索ID
     *
     * @throws BusinessException
     */
    public function deleteLead(int $id): bool
    {
        return $this->transactionManager->executeInTransaction(function () use ($id) {
            // 检查线索是否存在（业务规则验证）
            $lead = $this->leadRepository->findById($id);
            if (! $lead) {
                throw BusinessException::fromErrorCode('Lead.not_found');
            }

            // 检查线索状态是否允许删除（业务规则验证）
            if (isset($lead->status) && ! Lead::isDeletableStatus($lead->status)) {
                throw BusinessException::fromErrorCode('Lead.status_not_deletable');
            }

            // 删除线索的所有关联关系
            $this->leadContactRelationService->deleteAllLeadContactRelations($id);

            // 注意：这里不删除用户关联关系，因为可能需要保留历史记录
            // 如果需要删除用户关联关系，可以取消下面的注释
            // $this->leadUserRelationService->deleteAllLeadUserRelations($id);

            $result = $this->leadRepository->delete($id);

            if ($result) {
                // 清除相关缓存
                $this->clearLeadCaches($id);

                Log::info('线索删除成功', [
                    'lead_id' => $id,
                    'company_name' => $lead->company_full_name,
                ]);
            }

            return $result;
        });
    }

    /**
     * 清除线索相关缓存
     *
     * @param  int|null  $leadId  特定线索ID，为null时清除所有线索缓存
     */
    protected function clearLeadCaches(?int $leadId = null): void
    {
        // TODO: 实现缓存清除逻辑
        // 这里可以根据项目的缓存策略来实现具体的缓存清除逻辑
        // 例如：
        // - 清除线索列表缓存
        // - 清除线索详情缓存
        // - 清除线索统计缓存

        if ($leadId) {
            // 清除特定线索的缓存
            Log::debug('清除线索缓存', ['lead_id' => $leadId]);
        } else {
            // 清除所有线索相关缓存
            Log::debug('清除所有线索缓存');
        }
    }

    /**
     * 批量更新线索状态（使用事务管理和死锁重试）
     *
     * @param  array<int>  $ids  线索ID数组
     * @param  int  $status  新状态
     * @return int 更新成功的数量
     */
    public function batchUpdateStatus(array $ids, int $status): int
    {
        // 验证输入参数
        if (empty($ids)) {
            return 0;
        }

        if (! in_array($status, array_keys(Lead::STATUS_LABELS))) {
            throw BusinessException::fromErrorCode('Lead.invalid_status');
        }

        return $this->transactionManager->executeWithDeadlockRetry(function () use ($ids, $status) {
            $result = $this->leadRepository->batchUpdateStatus($ids, $status);

            if ($result > 0) {
                // 清除相关缓存
                $this->clearLeadCaches();

                Log::info('批量更新线索状态成功', [
                    'updated_count' => $result,
                    'status' => $status,
                    'total_ids' => count($ids),
                ]);
            }

            return $result;
        }, 3, 100); // 最多重试3次，延迟100ms
    }

    /**
     * 获取线索统计信息
     *
     * @param  array<string, mixed>  $filters  筛选条件
     * @return array<string, mixed> 统计信息
     */
    public function getLeadsStatistics(array $filters = []): array
    {
        try {
            return $this->leadRepository->getLeadsStatistics($filters);

        } catch (Exception $e) {
            Log::error('获取线索统计信息失败', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            return [
                'count' => 0,
                'group_count' => [],
                'error' => '获取统计信息失败',
            ];
        }
    }

    /**
     * 批量操作线索（创建、更新、删除）
     *
     * @param  array<int, array<string, mixed>>  $operations  操作数组
     * @return array<int, array<string, mixed>> 操作结果
     */
    public function batchOperateLeads(array $operations): array
    {
        $results = [];

        return $this->transactionManager->executeInTransaction(function () use ($operations, &$results) {
            foreach ($operations as $index => $operation) {
                try {
                    $result = match ($operation['type']) {
                        'create' => $this->leadRepository->create($operation['data']),
                        'update' => $this->leadRepository->update($operation['id'], $operation['data']),
                        'delete' => $this->leadRepository->delete($operation['id']),
                        default => throw BusinessException::fromErrorCode(
                            'Lead.operation_not_supported',
                            ['type' => $operation['type']]
                        )
                    };

                    $results[$index] = [
                        'success' => true,
                        'result' => $result,
                        'operation' => $operation['type'],
                    ];

                } catch (Exception $e) {
                    $results[$index] = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'operation' => $operation['type'],
                    ];

                    Log::warning('批量操作线索失败', [
                        'index' => $index,
                        'operation' => $operation,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // 清除相关缓存
            $this->clearLeadCaches();

            return $results;
        });
    }
}

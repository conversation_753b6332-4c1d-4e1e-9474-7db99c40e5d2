<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Models\Contact;
use App\Repositories\ContactRepositoryInterface;
use App\Repositories\LeadContactRelationRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

/**
 * 联系人业务逻辑服务
 */
class ContactService
{
    protected ContactRepositoryInterface $contactRepository;

    protected LeadContactRelationRepositoryInterface $leadContactRelationRepository;

    /**
     * 构造函数
     */
    public function __construct(
        ContactRepositoryInterface $contactRepository,
        LeadContactRelationRepositoryInterface $leadContactRelationRepository
    ) {
        $this->contactRepository = $contactRepository;
        $this->leadContactRelationRepository = $leadContactRelationRepository;
    }

    /**
     * 获取联系人分页列表
     *
     * @param  array<string, mixed>  $filters  筛选条件
     * @param  int  $perPage  每页数量
     * @param  int  $page  页码
     */
    public function getContactsList(array $filters = [], int $perPage = 15, int $page = 1): LengthAwarePaginator
    {
        return $this->contactRepository->getContactsList($filters, $perPage, $page);
    }

    /**
     * 根据ID获取联系人详情
     *
     * @param  int  $id  联系人ID
     *
     * @throws BusinessException
     */
    public function getContactById(int $id): Contact
    {
        $contact = $this->contactRepository->findById($id);

        if (! $contact) {
            throw new BusinessException('联系人不存在', 404);
        }

        return $contact;
    }

    /**
     * 创建联系人
     *
     * @param  array  $data  联系人数据
     *
     * @throws BusinessException
     */
    public function createContact(array $data): Contact
    {
        // 检查手机号重复（业务规则验证）
        if (! empty($data['mobile']) && $this->contactRepository->existsByMobile($data['mobile'])) {
            throw new BusinessException('该手机号已存在联系人记录', 409);
        }

        return $this->contactRepository->create($data);
    }

    /**
     * 更新联系人
     *
     * @param  int  $id  联系人ID
     * @param  array  $data  更新数据
     *
     * @throws BusinessException
     */
    public function updateContact(int $id, array $data): bool
    {
        // 检查联系人是否存在
        $contact = $this->contactRepository->findById($id);
        if (! $contact) {
            throw new BusinessException('联系人不存在', 404);
        }

        // 如果更新手机号，检查重复
        if (! empty($data['mobile']) && $data['mobile'] !== $contact->mobile) {
            if ($this->contactRepository->existsByMobile($data['mobile'], $id)) {
                throw new BusinessException('该手机号已被其他联系人使用', 409);
            }
        }

        return $this->contactRepository->update($id, $data);
    }

    /**
     * 删除联系人
     *
     * @param  int  $id  联系人ID
     *
     * @throws BusinessException
     */
    public function deleteContact(int $id): bool
    {
        // 检查联系人是否存在
        $contact = $this->contactRepository->findById($id);
        if (! $contact) {
            throw new BusinessException('联系人不存在', 404);
        }

        // 删除联系人的所有线索关联关系
        $this->leadContactRelationRepository->deleteByContactId($id);

        // 删除联系人
        return $this->contactRepository->delete($id);
    }

    /**
     * 批量创建联系人
     *
     * @param array<array{
     *     name: string,
     *     gender?: string|null,
     *     age?: int|null,
     *     mobile?: string|null,
     *     telephone?: string|null,
     *     email?: string|null,
     *     wx?: string|null,
     *     department?: string|null,
     *     position?: string|null,
     *     remark?: string|null
     * }> $contactsData 联系人数据数组
     *
     * @throws BusinessException
     */
    public function createBatchContacts(array $contactsData): Collection
    {
        // 验证手机号重复
        $mobiles = array_filter(array_column($contactsData, 'mobile'));
        foreach ($mobiles as $mobile) {
            if ($this->contactRepository->existsByMobile($mobile)) {
                throw new BusinessException("手机号 {$mobile} 已存在联系人记录", 409);
            }
        }

        // 检查批量数据中的手机号重复
        $uniqueMobiles = array_unique($mobiles);
        if (count($mobiles) !== count($uniqueMobiles)) {
            throw new BusinessException('批量数据中存在重复的手机号', 400);
        }

        return $this->contactRepository->createBatch($contactsData);
    }

    /**
     * 根据线索ID获取关联的联系人
     *
     * @param  int  $leadId  线索ID
     */
    public function getContactsByLeadId(int $leadId): Collection
    {
        return $this->contactRepository->getContactsByLeadId($leadId);
    }

    /**
     * 搜索联系人
     *
     * @param  string  $keyword  搜索关键词
     * @param  int  $limit  限制数量
     */
    public function searchContacts(string $keyword, int $limit = 10): Collection
    {
        return $this->contactRepository->searchContacts($keyword, $limit);
    }

    /**
     * 根据手机号获取或创建联系人
     *
     * @param  array  $contactData  联系人数据
     */
    public function getOrCreateContactByMobile(array $contactData): Contact
    {
        if (empty($contactData['mobile'])) {
            return $this->contactRepository->create($contactData);
        }

        $existingContact = $this->contactRepository->findByMobile($contactData['mobile']);

        if ($existingContact) {
            // 如果联系人已存在，可以选择更新部分信息
            $updateData = array_filter($contactData, function ($value) {
                return ! empty($value);
            });

            if (! empty($updateData)) {
                $this->contactRepository->update($existingContact->id, $updateData);
                $updatedContact = $this->contactRepository->findById($existingContact->id);

                return $updatedContact ?? $existingContact;
            }

            return $existingContact;
        }

        return $this->contactRepository->create($contactData);
    }
}

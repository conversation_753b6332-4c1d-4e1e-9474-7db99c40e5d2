<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Models\LeadContactRelation;
use App\Repositories\ContactRepositoryInterface;
use App\Repositories\LeadContactRelationRepositoryInterface;
use App\Repositories\LeadRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

/**
 * 线索联系人关联业务逻辑服务
 */
class LeadContactRelationService
{
    protected LeadContactRelationRepositoryInterface $leadContactRelationRepository;

    protected LeadRepositoryInterface $leadRepository;

    protected ContactRepositoryInterface $contactRepository;

    /**
     * 构造函数
     */
    public function __construct(
        LeadContactRelationRepositoryInterface $leadContactRelationRepository,
        LeadRepositoryInterface $leadRepository,
        ContactRepositoryInterface $contactRepository
    ) {
        $this->leadContactRelationRepository = $leadContactRelationRepository;
        $this->leadRepository = $leadRepository;
        $this->contactRepository = $contactRepository;
    }

    /**
     * 获取线索的联系人关联关系
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function getLeadContactRelations(int $leadId): Collection
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadContactRelationRepository->getRelationsByLeadId($leadId);
    }

    /**
     * 获取联系人的线索关联关系
     *
     * @param  int  $contactId  联系人ID
     *
     * @throws BusinessException
     */
    public function getContactLeadRelations(int $contactId): Collection
    {
        // 验证联系人是否存在
        if (! $this->contactRepository->findById($contactId)) {
            throw new BusinessException('联系人不存在', 404);
        }

        return $this->leadContactRelationRepository->getRelationsByContactId($contactId);
    }

    /**
     * 创建线索联系人关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     *
     * @throws BusinessException
     */
    public function createLeadContactRelation(int $leadId, int $contactId): LeadContactRelation
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 验证联系人是否存在
        if (! $this->contactRepository->findById($contactId)) {
            throw new BusinessException('联系人不存在', 404);
        }

        // 检查关联关系是否已存在
        if ($this->leadContactRelationRepository->existsRelation($leadId, $contactId)) {
            throw new BusinessException('线索和联系人已存在关联关系', 409);
        }

        return $this->leadContactRelationRepository->create([
            'lead_id' => $leadId,
            'contact_id' => $contactId,
        ]);
    }

    /**
     * 批量创建线索联系人关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  array<int>  $contactIds  联系人ID数组
     *
     * @throws BusinessException
     */
    public function createBatchLeadContactRelations(int $leadId, array $contactIds): Collection
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 验证所有联系人是否存在
        foreach ($contactIds as $contactId) {
            if (! $this->contactRepository->findById($contactId)) {
                throw new BusinessException("联系人ID {$contactId} 不存在", 404);
            }
        }

        // 准备批量创建数据
        $relationsData = [];
        foreach ($contactIds as $contactId) {
            // 跳过已存在的关联关系
            if (! $this->leadContactRelationRepository->existsRelation($leadId, $contactId)) {
                $relationsData[] = [
                    'lead_id' => $leadId,
                    'contact_id' => $contactId,
                ];
            }
        }

        return $this->leadContactRelationRepository->createBatch($relationsData);
    }

    /**
     * 删除线索联系人关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $contactId  联系人ID
     *
     * @throws BusinessException
     */
    public function deleteLeadContactRelation(int $leadId, int $contactId): bool
    {
        $relation = $this->leadContactRelationRepository->findRelation($leadId, $contactId);

        if (! $relation) {
            throw new BusinessException('关联关系不存在', 404);
        }

        return $this->leadContactRelationRepository->delete($relation->id);
    }

    /**
     * 同步线索的联系人关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  array<int>  $contactIds  联系人ID数组
     *
     * @throws BusinessException
     */
    public function syncLeadContacts(int $leadId, array $contactIds): bool
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 验证所有联系人是否存在
        foreach ($contactIds as $contactId) {
            if (! $this->contactRepository->findById($contactId)) {
                throw new BusinessException("联系人ID {$contactId} 不存在", 404);
            }
        }

        return $this->leadContactRelationRepository->syncLeadContacts($leadId, $contactIds);
    }

    /**
     * 删除线索的所有联系人关联关系
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function deleteAllLeadContactRelations(int $leadId): bool
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadContactRelationRepository->deleteByLeadId($leadId);
    }

    /**
     * 删除联系人的所有线索关联关系
     *
     * @param  int  $contactId  联系人ID
     *
     * @throws BusinessException
     */
    public function deleteAllContactLeadRelations(int $contactId): bool
    {
        // 验证联系人是否存在
        if (! $this->contactRepository->findById($contactId)) {
            throw new BusinessException('联系人不存在', 404);
        }

        return $this->leadContactRelationRepository->deleteByContactId($contactId);
    }

    /**
     * 批量删除关联关系
     *
     * @param  array<int>  $relationIds  关联ID数组
     * @return int 删除成功的数量
     */
    public function deleteBatchRelations(array $relationIds): int
    {
        return $this->leadContactRelationRepository->deleteBatch($relationIds);
    }
}

<?php

namespace App\Services;

use App\Jobs\ProcessFailedLogJob;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Throwable;

/**
 * 容错日志记录服务
 *
 * 提供多层容错机制确保日志记录失败不影响主业务流程
 */
class ResilientLoggerService
{
    private const CIRCUIT_BREAKER_KEY = 'api_logging_circuit_breaker';

    private const FAILURE_COUNT_KEY = 'api_logging_failure_count';

    private const LAST_FAILURE_TIME_KEY = 'api_logging_last_failure';

    private const BACKUP_LOG_KEY = 'api_logging_backup_';

    private int $maxFailures;

    private int $circuitBreakerTimeout;

    private int $logTimeout;

    private bool $enableBackup;

    private bool $enableQueue;

    public function __construct()
    {
        $this->maxFailures = config('logging.resilient.max_failures', 5);
        $this->circuitBreakerTimeout = config('logging.resilient.circuit_breaker_timeout', 300); // 5分钟
        $this->logTimeout = config('logging.resilient.log_timeout', 2); // 2秒
        $this->enableBackup = config('logging.resilient.enable_backup', true);
        $this->enableQueue = config('logging.resilient.enable_queue_fallback', true);
    }

    /**
     * 安全记录API请求日志
     *
     * @param  array  $logData  日志数据
     * @param  bool  $async  是否异步记录
     * @return bool 是否成功记录
     */
    public function logApiRequest(array $logData, bool $async = false): bool
    {
        // 检查熔断器状态
        if ($this->isCircuitBreakerOpen()) {
            $this->handleCircuitBreakerOpen($logData);

            return false;
        }

        try {
            if ($async) {
                return $this->logAsyncWithTimeout($logData);
            } else {
                return $this->logSyncWithTimeout($logData);
            }
        } catch (Throwable $e) {
            $this->handleLoggingFailure($e, $logData);

            return false;
        }
    }

    /**
     * 同步记录日志（带超时保护）
     */
    private function logSyncWithTimeout(array $logData): bool
    {
        $startTime = microtime(true);

        try {
            // 设置超时处理
            $this->executeWithTimeout(function () use ($logData) {
                Log::channel('api')->info('API Request', $logData);
            }, $this->logTimeout);

            $this->recordSuccess();

            return true;
        } catch (Exception $e) {
            $duration = microtime(true) - $startTime;
            throw new Exception("Sync logging failed after {$duration}s: ".$e->getMessage(), 0, $e);
        }
    }

    /**
     * 异步记录日志（带超时保护）
     */
    private function logAsyncWithTimeout(array $logData): bool
    {
        try {
            if (function_exists('fastcgi_finish_request')) {
                register_shutdown_function(function () use ($logData) {
                    $this->executeWithTimeout(function () use ($logData) {
                        Log::channel('api')->info('API Request', $logData);
                    }, $this->logTimeout);
                });
            } else {
                // 降级到同步记录
                return $this->logSyncWithTimeout($logData);
            }

            $this->recordSuccess();

            return true;
        } catch (Exception $e) {
            throw new Exception('Async logging setup failed: '.$e->getMessage(), 0, $e);
        }
    }

    /**
     * 带超时执行函数
     *
     * @throws Exception
     */
    private function executeWithTimeout(callable $callback, int $timeout): void
    {
        $startTime = time();

        try {
            $callback();

            // 检查是否超时
            if (time() - $startTime > $timeout) {
                throw new Exception("Operation timed out after {$timeout} seconds");
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 处理日志记录失败
     */
    private function handleLoggingFailure(Throwable $exception, array $logData): void
    {
        $this->recordFailure();

        // 尝试备用方案
        $this->tryFallbackStrategies($logData, $exception);

        // 记录失败信息到系统日志（使用不同的通道避免循环）
        $this->logFailureToSystem($exception, $logData);
    }

    /**
     * 尝试备用策略
     */
    private function tryFallbackStrategies(array $logData, Throwable $exception): void
    {
        // 策略1: 缓存备份
        if ($this->enableBackup) {
            $this->backupToCache($logData);
        }

        // 策略2: 队列延迟处理
        if ($this->enableQueue) {
            $this->queueForLaterProcessing($logData, $exception);
        }

        // 策略3: 文件备份（最后手段）
        $this->backupToFile($logData);
    }

    /**
     * 缓存备份日志数据
     */
    private function backupToCache(array $logData): void
    {
        try {
            $key = self::BACKUP_LOG_KEY.($logData['trace_id'] ?? uniqid());
            Cache::put($key, $logData, now()->addHours(24));
        } catch (Exception $e) {
            // 缓存失败也不影响主流程
        }
    }

    /**
     * 队列延迟处理
     */
    private function queueForLaterProcessing(array $logData, Throwable $exception): void
    {
        try {
            Queue::later(
                now()->addMinutes(5),
                new ProcessFailedLogJob($logData, $exception->getMessage())
            );
        } catch (Exception $e) {
            // 队列失败也不影响主流程
        }
    }

    /**
     * 文件备份（紧急备用方案）
     */
    private function backupToFile(array $logData): void
    {
        try {
            $backupPath = storage_path('logs/api_backup.log');
            $logLine = date('Y-m-d H:i:s').' '.json_encode($logData).PHP_EOL;
            file_put_contents($backupPath, $logLine, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // 文件备份失败也不影响主流程
        }
    }

    /**
     * 记录失败信息到系统日志
     */
    private function logFailureToSystem(Throwable $exception, array $logData): void
    {
        try {
            // 使用系统日志通道，避免循环依赖
            Log::channel('single')->error('API logging system failure', [
                'error' => $exception->getMessage(),
                'trace_id' => $logData['trace_id'] ?? 'unknown',
                'failure_time' => now()->toISOString(),
                'context' => 'resilient_logger_service',
            ]);
        } catch (Exception $e) {
            // 系统日志也失败时，使用 error_log
            error_log('Critical: API logging system completely failed - '.$exception->getMessage());
        }
    }

    /**
     * 检查熔断器是否开启
     */
    private function isCircuitBreakerOpen(): bool
    {
        $failureCount = Cache::get(self::FAILURE_COUNT_KEY, 0);
        $lastFailureTime = Cache::get(self::LAST_FAILURE_TIME_KEY, 0);

        if ($failureCount >= $this->maxFailures) {
            $timeSinceLastFailure = time() - $lastFailureTime;

            return $timeSinceLastFailure < $this->circuitBreakerTimeout;
        }

        return false;
    }

    /**
     * 处理熔断器开启状态
     */
    private function handleCircuitBreakerOpen(array $logData): void
    {
        // 熔断器开启时，直接使用备用策略
        if ($this->enableBackup) {
            $this->backupToCache($logData);
        }

        if ($this->enableQueue) {
            $this->queueForLaterProcessing($logData, new Exception('Circuit breaker open'));
        }
    }

    /**
     * 记录成功
     */
    private function recordSuccess(): void
    {
        Cache::forget(self::FAILURE_COUNT_KEY);
        Cache::forget(self::LAST_FAILURE_TIME_KEY);
        Cache::forget(self::CIRCUIT_BREAKER_KEY);
    }

    /**
     * 记录失败
     */
    private function recordFailure(): void
    {
        $failureCount = Cache::get(self::FAILURE_COUNT_KEY, 0) + 1;
        Cache::put(self::FAILURE_COUNT_KEY, $failureCount, now()->addHours(1));
        Cache::put(self::LAST_FAILURE_TIME_KEY, time(), now()->addHours(1));

        if ($failureCount >= $this->maxFailures) {
            Cache::put(self::CIRCUIT_BREAKER_KEY, true, now()->addSeconds($this->circuitBreakerTimeout));
        }
    }

    /**
     * 获取日志系统健康状态
     */
    public function getHealthStatus(): array
    {
        return [
            'circuit_breaker_open' => $this->isCircuitBreakerOpen(),
            'failure_count' => Cache::get(self::FAILURE_COUNT_KEY, 0),
            'last_failure_time' => Cache::get(self::LAST_FAILURE_TIME_KEY),
            'backup_logs_count' => $this->getBackupLogsCount(),
        ];
    }

    /**
     * 获取备份日志数量
     */
    private function getBackupLogsCount(): int
    {
        try {
            // 这里可以实现更复杂的统计逻辑
            return 0;
        } catch (Exception $e) {
            return 0;
        }
    }
}

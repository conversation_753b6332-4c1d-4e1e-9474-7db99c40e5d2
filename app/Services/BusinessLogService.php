<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 业务日志服务
 *
 * 提供统一的业务日志记录接口，包含 trace_id 追踪功能
 */
class BusinessLogService
{
    /**
     * 当前请求的 trace_id
     */
    private ?string $traceId = null;

    /**
     * 业务日志通道名称
     */
    private string $channel = 'business';

    /**
     * 支持的日志级别
     */
    private array $supportedLevels = [
        'debug',
        'info',
        'warning',
        'error',
        'critical',
    ];

    /**
     * 设置 trace_id
     */
    public function setTraceId(string $traceId): self
    {
        $this->traceId = $traceId;

        return $this;
    }

    /**
     * 获取或生成 trace_id
     */
    public function getTraceId(): string
    {
        if (! $this->traceId) {
            $this->traceId = Str::uuid()->toString();
        }

        return $this->traceId;
    }

    /**
     * 简化的日志记录方法
     *
     * 只需要传入日志内容的 JSON 数据，trace_id 会自动包含
     *
     * @param  array  $data  日志数据（JSON 格式）
     * @param  string  $level  日志级别 (debug, info, warning, error, critical)
     *
     * @throws \InvalidArgumentException 当日志级别不支持时
     */
    public function log(array $data, string $level = 'info'): void
    {
        $this->validateLogLevel($level);

        // 自动添加 trace_id 和时间戳
        $logData = array_merge($data, [
            'trace_id' => $this->getTraceId(),
            'timestamp' => now()->toISOString(),
        ]);

        // 提取消息，如果没有则使用默认消息
        $message = $logData['message'] ?? '业务日志记录';

        // 清理敏感数据
        $logData = $this->sanitizeData($logData);

        Log::channel($this->channel)->$level($message, $logData);
    }

    /**
     * 记录 debug 级别日志
     *
     * @param  array  $data  日志数据
     */
    public function debug(array $data): void
    {
        $this->log($data, 'debug');
    }

    /**
     * 记录 info 级别日志
     *
     * @param  array  $data  日志数据
     */
    public function info(array $data): void
    {
        $this->log($data, 'info');
    }

    /**
     * 记录 warning 级别日志
     *
     * @param  array  $data  日志数据
     */
    public function warning(array $data): void
    {
        $this->log($data, 'warning');
    }

    /**
     * 记录 error 级别日志
     *
     * @param  array  $data  日志数据
     */
    public function error(array $data): void
    {
        $this->log($data, 'error');
    }

    /**
     * 记录 critical 级别日志
     *
     * @param  array  $data  日志数据
     */
    public function critical(array $data): void
    {
        $this->log($data, 'critical');
    }

    /**
     * 验证日志级别是否支持
     *
     * @throws \InvalidArgumentException
     */
    private function validateLogLevel(string $level): void
    {
        if (! in_array($level, $this->supportedLevels)) {
            throw new \InvalidArgumentException(
                "不支持的日志级别: {$level}。支持的级别: ".implode(', ', $this->supportedLevels)
            );
        }
    }

    /**
     * 记录异常日志的便捷方法
     *
     * @param  string  $message  错误消息
     * @param  \Throwable|null  $exception  异常对象
     * @param  array  $additionalData  额外的上下文数据
     */
    public function logException(string $message, ?\Throwable $exception = null, array $additionalData = []): void
    {
        $logData = array_merge($additionalData, [
            'message' => $message,
            'level' => 'error',
        ]);

        if ($exception) {
            $logData['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        $this->error($logData);
    }

    /**
     * 清理敏感数据
     */
    private function sanitizeData(array $data): array
    {
        $sensitiveKeys = [
            'password',
            'password_confirmation',
            'token',
            'secret',
            'key',
            'authorization',
        ];

        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);

            foreach ($sensitiveKeys as $sensitiveKey) {
                if (str_contains($lowerKey, $sensitiveKey)) {
                    $data[$key] = '[REDACTED]';
                    break;
                }
            }

            if (is_array($value)) {
                $data[$key] = $this->sanitizeData($value);
            }
        }

        return $data;
    }

    /**
     * 从请求中提取基础信息
     */
    public function extractRequestInfo(?Request $request = null): array
    {
        if (! $request && app()->bound('request')) {
            $request = app('request');
        }

        if (! $request) {
            return [];
        }

        return [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id,
        ];
    }
}

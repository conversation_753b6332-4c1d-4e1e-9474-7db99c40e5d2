<?php

namespace App\Services;

use App\Contracts\RedisServiceInterface;

/**
 * 线索缓存服务
 *
 * 演示如何在业务服务中使用 Redis 服务进行数据缓存
 */
class LeadCacheService
{
    /**
     * Redis 服务实例
     */
    protected RedisServiceInterface $redisService;

    /**
     * 缓存键前缀
     */
    protected string $keyPrefix = 'Lead:cache:';

    /**
     * 默认缓存时间（秒）
     */
    protected int $defaultTtl = 3600; // 1小时

    /**
     * 构造函数
     */
    public function __construct(RedisServiceInterface $redisService)
    {
        $this->redisService = $redisService;
    }

    /**
     * 缓存线索详情
     *
     * @param  int  $leadId  线索ID
     * @param  array  $leadData  线索数据
     * @param  int|null  $ttl  缓存时间（秒）
     * @return bool 缓存是否成功
     */
    public function cacheLeadDetail(int $leadId, array $leadData, ?int $ttl = null): bool
    {
        $key = $this->getLeadDetailKey($leadId);
        $ttl = $ttl ?? $this->defaultTtl;

        return $this->redisService->set($key, $leadData, $ttl);
    }

    /**
     * 获取线索详情缓存键
     *
     * @param  int  $leadId  线索ID
     * @return string 缓存键
     */
    protected function getLeadDetailKey(int $leadId): string
    {
        return $this->keyPrefix."detail:{$leadId}";
    }

    /**
     * 获取缓存的线索详情
     *
     * @param  int  $leadId  线索ID
     * @return array|null 线索数据或null
     */
    public function getCachedLeadDetail(int $leadId): ?array
    {
        $key = $this->getLeadDetailKey($leadId);
        $data = $this->redisService->get($key);

        return is_array($data) ? $data : null;
    }

    /**
     * 删除线索详情缓存
     *
     * @param  int  $leadId  线索ID
     * @return bool 删除是否成功
     */
    public function clearLeadDetailCache(int $leadId): bool
    {
        $key = $this->getLeadDetailKey($leadId);

        return $this->redisService->delete($key);
    }

    /**
     * 缓存线索列表
     *
     * @param  string  $cacheKey  缓存键标识
     * @param  array<string, mixed>  $leadList  线索列表数据
     * @param  int|null  $ttl  缓存时间（秒）
     * @return bool 缓存是否成功
     */
    public function cacheLeadList(string $cacheKey, array $leadList, ?int $ttl = null): bool
    {
        $key = $this->getLeadListKey($cacheKey);
        $ttl = $ttl ?? ($this->defaultTtl / 2); // 列表缓存时间较短

        return $this->redisService->set($key, $leadList, $ttl);
    }

    /**
     * 获取线索列表缓存键
     *
     * @param  string  $cacheKey  缓存键标识
     * @return string 缓存键
     */
    protected function getLeadListKey(string $cacheKey): string
    {
        return $this->keyPrefix."list:{$cacheKey}";
    }

    /**
     * 获取缓存的线索列表
     *
     * @param  string  $cacheKey  缓存键标识
     * @return array<string, mixed>|null 线索列表数据或null
     */
    public function getCachedLeadList(string $cacheKey): ?array
    {
        $key = $this->getLeadListKey($cacheKey);
        $data = $this->redisService->get($key);

        return is_array($data) ? $data : null;
    }

    /**
     * 删除线索列表缓存
     *
     * @param  string  $cacheKey  缓存键标识
     * @return bool 删除是否成功
     */
    public function clearLeadListCache(string $cacheKey): bool
    {
        $key = $this->getLeadListKey($cacheKey);

        return $this->redisService->delete($key);
    }

    /**
     * 批量缓存线索详情
     *
     * @param  array<int, array<string, mixed>>  $leadsData  线索数据数组，键为线索ID
     * @param  int|null  $ttl  缓存时间（秒）
     * @return bool 批量缓存是否成功
     */
    public function batchCacheLeadDetails(array $leadsData, ?int $ttl = null): bool
    {
        if (empty($leadsData)) {
            return true;
        }

        $cacheData = [];
        foreach ($leadsData as $leadId => $leadData) {
            $key = $this->getLeadDetailKey($leadId);
            $cacheData[$key] = $leadData;
        }

        $ttl = $ttl ?? $this->defaultTtl;

        return $this->redisService->setMultiple($cacheData, $ttl);
    }

    /**
     * 批量获取线索详情缓存
     *
     * @param  array<int>  $leadIds  线索ID数组
     * @return array<int, array<string, mixed>> 线索数据数组，键为线索ID
     */
    public function batchGetCachedLeadDetails(array $leadIds): array
    {
        if (empty($leadIds)) {
            return [];
        }

        $keys = [];
        foreach ($leadIds as $leadId) {
            $keys[] = $this->getLeadDetailKey($leadId);
        }

        $cachedData = $this->redisService->getMultiple($keys);
        $result = [];

        foreach ($leadIds as $index => $leadId) {
            $key = $keys[$index];
            if (isset($cachedData[$key]) && is_array($cachedData[$key])) {
                $result[$leadId] = $cachedData[$key];
            }
        }

        return $result;
    }

    /**
     * 清除用户相关的所有线索缓存
     *
     * @param  int  $userId  用户ID
     * @return int 清除的缓存数量
     */
    public function clearUserLeadCaches(int $userId): int
    {
        $pattern = $this->keyPrefix."user:{$userId}:*";
        $keys = $this->redisService->keys($pattern);

        if (empty($keys)) {
            return 0;
        }

        return $this->redisService->deleteMultiple($keys);
    }

    /**
     * 记录线索访问次数
     *
     * @param  int  $leadId  线索ID
     * @return int 访问次数
     */
    public function incrementLeadViewCount(int $leadId): int
    {
        $key = $this->getLeadViewCountKey($leadId);

        return $this->redisService->increment($key);
    }

    /**
     * 获取线索访问次数缓存键
     *
     * @param  int  $leadId  线索ID
     * @return string 缓存键
     */
    protected function getLeadViewCountKey(int $leadId): string
    {
        return $this->keyPrefix."view_count:{$leadId}";
    }

    /**
     * 获取线索访问次数
     *
     * @param  int  $leadId  线索ID
     * @return int 访问次数
     */
    public function getLeadViewCount(int $leadId): int
    {
        $key = $this->getLeadViewCountKey($leadId);
        $count = $this->redisService->get($key);

        return is_numeric($count) ? (int) $count : 0;
    }

    /**
     * 设置线索热度分数
     *
     * @param  int  $leadId  线索ID
     * @param  float  $score  热度分数
     * @param  int|null  $ttl  缓存时间（秒）
     * @return bool 设置是否成功
     */
    public function setLeadHotScore(int $leadId, float $score, ?int $ttl = null): bool
    {
        $key = $this->getLeadHotScoreKey($leadId);
        $ttl = $ttl ?? ($this->defaultTtl * 24); // 热度分数缓存时间较长

        return $this->redisService->set($key, $score, $ttl);
    }

    /**
     * 获取线索热度分数缓存键
     *
     * @param  int  $leadId  线索ID
     * @return string 缓存键
     */
    protected function getLeadHotScoreKey(int $leadId): string
    {
        return $this->keyPrefix."hot_score:{$leadId}";
    }

    /**
     * 获取线索热度分数
     *
     * @param  int  $leadId  线索ID
     * @return float 热度分数
     */
    public function getLeadHotScore(int $leadId): float
    {
        $key = $this->getLeadHotScoreKey($leadId);
        $score = $this->redisService->get($key);

        return is_numeric($score) ? (float) $score : 0.0;
    }

    /**
     * 生成基于查询条件的缓存键
     *
     * @param  array<string, mixed>  $conditions  查询条件
     * @return string 缓存键标识
     */
    public function generateListCacheKey(array $conditions): string
    {
        // 移除敏感信息，只保留影响查询结果的条件
        $cacheConditions = array_intersect_key($conditions, array_flip([
            'status', 'stage', 'region', 'industry', 'source',
            'page', 'per_page', 'sort_by', 'sort_order',
        ]));

        ksort($cacheConditions); // 确保键的顺序一致

        $jsonString = json_encode($cacheConditions);

        return md5($jsonString !== false ? $jsonString : '');
    }
}

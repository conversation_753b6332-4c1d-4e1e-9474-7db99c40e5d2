<?php

namespace App\Services;

use App\Utils\UnitFormatter;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 日志健康监控服务
 */
class LogHealthMonitorService
{
    private const HEALTH_CHECK_KEY = 'log_health_check_';

    private const ALERT_COOLDOWN_KEY = 'log_alert_cooldown_';

    private int $alertCooldown;

    private array $thresholds;

    public function __construct()
    {
        $this->alertCooldown = config('logging.monitoring.alert_cooldown', 1800); // 30分钟
        $this->thresholds = config('logging.monitoring.thresholds', [
            'disk_usage_percent' => 85,
            'log_file_size_mb' => 100,
            'failure_rate_percent' => 10,
            'response_time_ms' => 1000,
        ]);
    }

    /**
     * 执行全面健康检查
     */
    public function performHealthCheck(): array
    {
        $results = [
            'overall_status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'checks' => [],
            'alerts' => [],
        ];

        // 检查磁盘空间
        $diskCheck = $this->checkDiskSpace();
        $results['checks']['disk_space'] = $diskCheck;
        if (! $diskCheck['healthy']) {
            $results['overall_status'] = 'unhealthy';
            $results['alerts'][] = $diskCheck['message'];
        }

        // 检查日志文件大小
        $fileSizeCheck = $this->checkLogFileSize();
        $results['checks']['log_file_size'] = $fileSizeCheck;
        if (! $fileSizeCheck['healthy']) {
            $results['overall_status'] = 'degraded';
            $results['alerts'][] = $fileSizeCheck['message'];
        }

        // 检查日志写入权限
        $permissionCheck = $this->checkLogPermissions();
        $results['checks']['permissions'] = $permissionCheck;
        if (! $permissionCheck['healthy']) {
            $results['overall_status'] = 'unhealthy';
            $results['alerts'][] = $permissionCheck['message'];
        }

        // 检查日志记录性能
        $performanceCheck = $this->checkLogPerformance();
        $results['checks']['performance'] = $performanceCheck;
        if (! $performanceCheck['healthy']) {
            $results['overall_status'] = 'degraded';
            $results['alerts'][] = $performanceCheck['message'];
        }

        // 检查熔断器状态
        $circuitBreakerCheck = $this->checkCircuitBreakerStatus();
        $results['checks']['circuit_breaker'] = $circuitBreakerCheck;
        if (! $circuitBreakerCheck['healthy']) {
            $results['overall_status'] = 'degraded';
            $results['alerts'][] = $circuitBreakerCheck['message'];
        }

        // 发送告警（如果需要）
        if (! empty($results['alerts'])) {
            $this->sendAlertsIfNeeded($results['alerts']);
        }

        return $results;
    }

    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace(): array
    {
        try {
            $logPath = storage_path('logs');
            $freeBytes = disk_free_space($logPath);
            $totalBytes = disk_total_space($logPath);

            if ($freeBytes === false || $totalBytes === false) {
                return [
                    'healthy' => false,
                    'message' => 'Unable to determine disk space',
                    'details' => ['path' => $logPath],
                ];
            }

            $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
            $threshold = $this->thresholds['disk_usage_percent'];

            return [
                'healthy' => $usedPercent < $threshold,
                'message' => $usedPercent >= $threshold
                    ? "Disk usage {$usedPercent}% exceeds threshold {$threshold}%"
                    : "Disk usage normal: {$usedPercent}%",
                'details' => [
                    'used_percent' => round($usedPercent, 2),
                    'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
                    'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
                ],
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Disk space check failed: '.$e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查日志文件大小
     */
    private function checkLogFileSize(): array
    {
        try {
            $logFiles = [
                'api' => storage_path('logs/api.log'),
                'laravel' => storage_path('logs/laravel.log'),
            ];

            $oversizedFiles = [];
            $threshold = $this->thresholds['log_file_size_mb'] * 1024 * 1024; // 转换为字节

            foreach ($logFiles as $name => $path) {
                if (file_exists($path)) {
                    $size = filesize($path);
                    if ($size > $threshold) {
                        $oversizedFiles[] = [
                            'file' => $name,
                            'size_mb' => round($size / 1024 / 1024, 2),
                            'threshold_mb' => $this->thresholds['log_file_size_mb'],
                        ];
                    }
                }
            }

            return [
                'healthy' => empty($oversizedFiles),
                'message' => empty($oversizedFiles)
                    ? 'All log files within size limits'
                    : 'Some log files exceed size threshold',
                'details' => ['oversized_files' => $oversizedFiles],
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Log file size check failed: '.$e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查日志写入权限
     */
    private function checkLogPermissions(): array
    {
        try {
            $logPath = storage_path('logs');
            $testFile = $logPath.'/health_check_'.time().'.tmp';

            // 尝试写入测试文件
            $testData = 'health check test';
            $writeResult = file_put_contents($testFile, $testData);

            if ($writeResult === false) {
                return [
                    'healthy' => false,
                    'message' => 'Cannot write to log directory',
                    'details' => ['path' => $logPath],
                ];
            }

            // 尝试读取测试文件
            $readData = file_get_contents($testFile);
            if ($readData !== $testData) {
                return [
                    'healthy' => false,
                    'message' => 'Cannot read from log directory',
                    'details' => ['path' => $logPath],
                ];
            }

            // 清理测试文件
            unlink($testFile);

            return [
                'healthy' => true,
                'message' => 'Log directory permissions OK',
                'details' => ['path' => $logPath],
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Permission check failed: '.$e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查日志记录性能
     */
    private function checkLogPerformance(): array
    {
        try {
            $startTime = microtime(true);

            // 执行测试日志写入
            Log::channel('single')->info('Health check test log entry', [
                'timestamp' => now()->toISOString(),
                'test_data' => 'performance_check',
            ]);

            $durationSeconds = microtime(true) - $startTime;
            $durationMs = $durationSeconds * 1000; // 保留毫秒用于阈值比较
            $threshold = $this->thresholds['response_time_ms'];
            $formattedDuration = UnitFormatter::formatTime($durationSeconds);

            return [
                'healthy' => $durationMs < $threshold,
                'message' => $durationMs >= $threshold
                    ? "Log write time {$formattedDuration} exceeds threshold {$threshold}ms"
                    : "Log write performance OK: {$formattedDuration}",
                'details' => [
                    'duration' => $formattedDuration,
                    'duration_ms' => round($durationMs, 2), // 保留原始毫秒值用于兼容性
                    'threshold_ms' => $threshold,
                ],
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Performance check failed: '.$e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 检查熔断器状态
     */
    private function checkCircuitBreakerStatus(): array
    {
        try {
            $failureCount = Cache::get('api_logging_failure_count', 0);
            $isOpen = Cache::get('api_logging_circuit_breaker', false);
            $lastFailureTime = Cache::get('api_logging_last_failure_time', 0);

            return [
                'healthy' => ! $isOpen,
                'message' => $isOpen
                    ? "Circuit breaker is OPEN (failures: {$failureCount})"
                    : 'Circuit breaker is CLOSED',
                'details' => [
                    'is_open' => $isOpen,
                    'failure_count' => $failureCount,
                    'last_failure_time' => $lastFailureTime ? date('Y-m-d H:i:s', $lastFailureTime) : null,
                ],
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'Circuit breaker check failed: '.$e->getMessage(),
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * 发送告警（如果需要且未在冷却期）
     */
    private function sendAlertsIfNeeded(array $alerts): void
    {
        foreach ($alerts as $alert) {
            $alertKey = self::ALERT_COOLDOWN_KEY.md5($alert);

            if (! Cache::has($alertKey)) {
                // 发送告警
                $this->sendAlert($alert);

                // 设置冷却期
                Cache::put($alertKey, true, now()->addSeconds($this->alertCooldown));
            }
        }
    }

    /**
     * 发送告警
     */
    private function sendAlert(string $message): void
    {
        try {
            // 这里可以集成各种告警渠道：邮件、短信、Slack、钉钉等
            Log::channel('single')->critical('Log System Alert', [
                'alert_message' => $message,
                'timestamp' => now()->toISOString(),
                'server' => gethostname(),
            ]);

            // 示例：发送到监控系统
            // $this->sendToMonitoringSystem($message);

        } catch (Exception $e) {
            // 告警发送失败也不应该影响系统
            error_log('Failed to send log system alert: '.$e->getMessage());
        }
    }

    /**
     * 获取历史健康状态
     *
     * @param  int  $hours  获取过去几小时的数据
     */
    public function getHealthHistory(int $hours = 24): array
    {
        // 这里可以实现从缓存或数据库获取历史健康状态
        // 简化实现，返回当前状态
        return [
            'period_hours' => $hours,
            'current_status' => $this->performHealthCheck(),
            'summary' => [
                'total_checks' => 1,
                'healthy_checks' => 1,
                'unhealthy_checks' => 0,
            ],
        ];
    }
}

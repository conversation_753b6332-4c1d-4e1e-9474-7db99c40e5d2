<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Models\LeadUserRelation;
use App\Repositories\LeadRepositoryInterface;
use App\Repositories\LeadUserRelationRepository;
use App\Repositories\LeadUserRelationRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

/**
 * 线索用户关联业务逻辑服务
 */
class LeadUserRelationService
{
    protected LeadUserRelationRepositoryInterface $leadUserRelationRepository;

    protected LeadRepositoryInterface $leadRepository;

    /**
     * 构造函数
     */
    public function __construct(
        LeadUserRelationRepositoryInterface $leadUserRelationRepository,
        LeadRepositoryInterface $leadRepository
    ) {
        $this->leadUserRelationRepository = $leadUserRelationRepository;
        $this->leadRepository = $leadRepository;
    }

    /**
     * 获取线索的用户关联关系
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function getLeadUserRelations(int $leadId): Collection
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadUserRelationRepository->getRelationsByLeadId($leadId);
    }

    /**
     * 获取线索的主负责人
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function getLeadPrimaryOwner(int $leadId): ?LeadUserRelation
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadUserRelationRepository->getPrimaryOwnerByLeadId($leadId);
    }

    /**
     * 获取线索的协同人员
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function getLeadCollaborators(int $leadId): Collection
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadUserRelationRepository->getCollaboratorsByLeadId($leadId);
    }

    /**
     * 设置线索的主负责人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     *
     * @throws BusinessException
     */
    public function setLeadPrimaryOwner(int $leadId, int $userId): bool
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // TODO: 这里可以添加用户存在性验证
        // if (!$this->userRepository->findById($userId)) {
        //     throw new BusinessException('用户不存在', 404);
        // }

        return $this->leadUserRelationRepository->setPrimaryOwner($leadId, $userId);
    }

    /**
     * 添加线索协同人员
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     *
     * @throws BusinessException
     */
    public function addLeadCollaborator(int $leadId, int $userId): LeadUserRelation
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 检查是否已经是协同人员
        if ($this->leadUserRelationRepository->existsRelation($leadId, $userId, LeadUserRelationRepository::ROLE_TYPE_COLLABORATOR)) {
            throw new BusinessException('该用户已是线索的协同人员', 409);
        }

        return $this->leadUserRelationRepository->create([
            'lead_id' => $leadId,
            'user_id' => $userId,
            'role_type' => LeadUserRelationRepository::ROLE_TYPE_COLLABORATOR,
            'is_primary' => 0,
        ]);
    }

    /**
     * 移除线索用户关联关系
     *
     * @param  int  $leadId  线索ID
     * @param  int  $userId  用户ID
     * @param  int  $roleType  角色类型
     *
     * @throws BusinessException
     */
    public function removeLeadUserRelation(int $leadId, int $userId, int $roleType): bool
    {
        $relation = $this->leadUserRelationRepository->findRelation($leadId, $userId, $roleType);

        if (! $relation) {
            throw new BusinessException('关联关系不存在', 404);
        }

        // 如果是主负责人，不允许删除
        if ($roleType === LeadUserRelationRepository::ROLE_TYPE_OWNER && $relation->is_primary) {
            throw new BusinessException('不能删除主负责人，请先设置其他负责人', 403);
        }

        return $this->leadUserRelationRepository->delete($relation->id);
    }

    /**
     * 批量设置线索的协同人员
     *
     * @param  int  $leadId  线索ID
     * @param  array<int>  $userIds  用户ID数组
     *
     * @throws BusinessException
     */
    public function setLeadCollaborators(int $leadId, array $userIds): Collection
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 删除现有的协同人员关系
        $existingCollaborators = $this->leadUserRelationRepository->getCollaboratorsByLeadId($leadId);
        foreach ($existingCollaborators as $collaborator) {
            $this->leadUserRelationRepository->delete($collaborator->id);
        }

        // 创建新的协同人员关系
        $relationsData = [];
        foreach ($userIds as $userId) {
            $relationsData[] = [
                'lead_id' => $leadId,
                'user_id' => $userId,
                'role_type' => LeadUserRelationRepository::ROLE_TYPE_COLLABORATOR,
                'is_primary' => 0,
            ];
        }

        return $this->leadUserRelationRepository->createBatch($relationsData);
    }

    /**
     * 获取用户负责的线索关联关系
     *
     * @param  int  $userId  用户ID
     */
    public function getUserLeadRelations(int $userId): Collection
    {
        return $this->leadUserRelationRepository->getRelationsByUserId($userId);
    }

    /**
     * 转移线索负责人
     *
     * @param  int  $leadId  线索ID
     * @param  int  $fromUserId  原负责人用户ID
     * @param  int  $toUserId  新负责人用户ID
     *
     * @throws BusinessException
     */
    public function transferLeadOwnership(int $leadId, int $fromUserId, int $toUserId): bool
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        // 验证原负责人关系是否存在
        $fromRelation = $this->leadUserRelationRepository->findRelation($leadId, $fromUserId, LeadUserRelationRepository::ROLE_TYPE_OWNER);
        if (! $fromRelation || ! $fromRelation->is_primary) {
            throw new BusinessException('原用户不是该线索的主负责人', 403);
        }

        // 设置新的主负责人
        return $this->leadUserRelationRepository->setPrimaryOwner($leadId, $toUserId);
    }

    /**
     * 删除线索的所有用户关联关系
     *
     * @param  int  $leadId  线索ID
     *
     * @throws BusinessException
     */
    public function deleteAllLeadUserRelations(int $leadId): bool
    {
        // 验证线索是否存在
        if (! $this->leadRepository->findById($leadId)) {
            throw new BusinessException('线索不存在', 404);
        }

        return $this->leadUserRelationRepository->deleteByLeadId($leadId);
    }
}

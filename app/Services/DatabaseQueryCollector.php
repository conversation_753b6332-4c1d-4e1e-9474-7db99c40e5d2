<?php

namespace App\Services;

use App\Contracts\DatabaseQueryCollectorInterface;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 数据库查询收集器实现
 *
 * 通过监听 Laravel 数据库事件来收集查询统计信息
 */
class DatabaseQueryCollector implements DatabaseQueryCollectorInterface
{
    /**
     * 存储查询数据的数组
     */
    private array $queries = [];

    /**
     * 是否正在收集查询
     */
    private bool $isCollecting = false;

    /**
     * 慢查询阈值（毫秒）
     */
    private float $slowQueryThreshold;

    /**
     * 最大查询记录数
     */
    private int $maxQueriesLogged;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->slowQueryThreshold = config('database.query_logging.slow_query_threshold', 100);
        $this->maxQueriesLogged = config('database.query_logging.max_queries_logged', 50);
    }

    /**
     * 开始收集数据库查询
     */
    public function startCollection(): void
    {
        if ($this->isCollecting) {
            return;
        }

        $this->isCollecting = true;
        $this->queries = [];

        try {
            DB::listen(function ($query) {
                $this->recordQuery($query);
            });
        } catch (Exception $e) {
            Log::warning('Failed to start database query collection', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->isCollecting = false;
        }
    }

    /**
     * 记录单个查询
     *
     * @param  mixed  $query  Laravel 查询事件对象
     */
    private function recordQuery($query): void
    {
        if (! $this->isCollecting) {
            return;
        }

        // 防止记录过多查询导致内存问题
        if (count($this->queries) >= $this->maxQueriesLogged) {
            Log::warning('Maximum queries logged limit reached', [
                'limit' => $this->maxQueriesLogged,
                'current_count' => count($this->queries),
            ]);

            return;
        }

        try {
            $executionTime = $query->time ?? 0;
            $isSlowQuery = $executionTime > $this->slowQueryThreshold;

            $this->queries[] = [
                'sql' => $query->sql,
                'bindings' => $query->bindings ?? [],
                'execution_time' => round($executionTime, 2),
                'is_slow' => $isSlowQuery,
                'timestamp' => microtime(true),
            ];
        } catch (Exception $e) {
            Log::warning('Failed to record database query', [
                'error' => $e->getMessage(),
                'sql' => $query->sql ?? 'unknown',
            ]);
        }
    }

    /**
     * 停止收集数据库查询
     */
    public function stopCollection(): void
    {
        $this->isCollecting = false;
    }

    /**
     * 获取查询统计信息
     *
     * @return array 包含查询统计信息的数组
     */
    public function getQueryStatistics(): array
    {
        try {
            // 确保查询按执行顺序排列（基于timestamp）
            $sortedQueries = $this->queries;
            usort($sortedQueries, fn ($a, $b) => $a['timestamp'] <=> $b['timestamp']);

            $totalQueries = count($sortedQueries);
            $totalTime = array_sum(array_column($sortedQueries, 'execution_time'));
            $slowQueries = array_filter($sortedQueries, fn ($query) => $query['is_slow']);
            $slowQueriesCount = count($slowQueries);

            // 按执行时间排序慢查询（从高到低）
            usort($slowQueries, fn ($a, $b) => $b['execution_time'] <=> $a['execution_time']);

            // 为日志输出优化查询记录格式
            $formattedQueries = array_map(function ($query) {
                return [
                    'sql' => $this->formatSqlWithBindings($query['sql'], $query['bindings']),
                    //                    'raw_sql' => $query['sql'],
                    //                    'bindings' => $query['bindings'],
                    'execution_time' => $query['execution_time'],
                    'is_slow' => $query['is_slow'],
                    'timestamp' => $query['timestamp'],
                ];
            }, $sortedQueries);

            return [
                'total_queries' => $totalQueries,
                'total_time_ms' => round($totalTime, 2),
                'slow_queries_count' => $slowQueriesCount,
                'queries' => $formattedQueries,
                'slow_queries_summary' => array_map(function ($query) {
                    return [
                        'sql' => $this->formatSqlWithBindings($query['sql'], $query['bindings']),
                        'time_ms' => $query['execution_time'],
                    ];
                }, $slowQueries),
            ];
        } catch (Exception $e) {
            Log::warning('Failed to generate query statistics', [
                'error' => $e->getMessage(),
                'queries_count' => count($this->queries),
            ]);

            return [
                'error' => 'Query statistics unavailable',
                'total_queries' => 0,
                'total_time_ms' => 0,
                'slow_queries_count' => 0,
                'queries' => [],
                'slow_queries_summary' => [],
            ];
        }
    }

    /**
     * 格式化SQL语句，将参数绑定替换到SQL中
     *
     * @param  string  $sql  原始SQL语句
     * @param  array  $bindings  参数绑定数组
     * @return string 格式化后的SQL语句
     */
    private function formatSqlWithBindings(string $sql, array $bindings): string
    {
        if (empty($bindings)) {
            return $sql;
        }

        try {
            $formattedSql = $sql;

            foreach ($bindings as $binding) {
                // 处理不同类型的参数绑定
                if (is_string($binding)) {
                    $value = "'".addslashes($binding)."'";
                } elseif (is_null($binding)) {
                    $value = 'NULL';
                } elseif (is_bool($binding)) {
                    $value = $binding ? '1' : '0';
                } elseif (is_numeric($binding)) {
                    $value = (string) $binding;
                } else {
                    $value = "'".addslashes((string) $binding)."'";
                }

                // 替换第一个问号占位符
                $pos = strpos($formattedSql, '?');
                if ($pos !== false) {
                    $formattedSql = substr_replace($formattedSql, $value, $pos, 1);
                }
            }

            return $formattedSql;
        } catch (Exception $e) {
            Log::warning('Failed to format SQL with bindings', [
                'error' => $e->getMessage(),
                'sql' => $sql,
                'bindings_count' => count($bindings),
            ]);

            // 如果格式化失败，返回原始SQL
            return $sql;
        }
    }

    /**
     * 重置收集器状态，清除当前请求的数据
     */
    public function reset(): void
    {
        $this->queries = [];
        $this->isCollecting = false;
    }
}

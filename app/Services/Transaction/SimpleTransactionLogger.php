<?php

namespace App\Services\Transaction;

use App\Contracts\TransactionManagerInterface;
use App\Facades\BusinessLog;
use App\Utils\UnitFormatter;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 简化版事务日志记录器
 *
 * 使用 TransactionManager 的回调机制实现事务日志记录
 */
class SimpleTransactionLogger
{
    /**
     * 当前事务上下文存储
     *
     * @var array<string, mixed>
     */
    private static array $transactionContext = [];

    /**
     * 是否已注册回调
     */
    private static bool $callbacksRegistered = false;

    /**
     * 注册事务日志回调到 TransactionManager
     */
    public static function registerCallbacks(TransactionManagerInterface $transactionManager): void
    {
        if (self::$callbacksRegistered) {
            return; // 避免重复注册
        }

        // 注册事务开始前回调
        $transactionManager->registerCallback('before_begin', function () {
            self::logTransactionBegin();
        });

        // 注册事务提交后回调
        $transactionManager->registerCallback('after_commit', function () {
            self::logTransactionCommit();
        });

        // 注册事务回滚后回调
        $transactionManager->registerCallback('after_rollback', function () {
            self::logTransactionRollback();
        });

        self::$callbacksRegistered = true;
        Log::debug('事务日志回调已注册');
    }

    /**
     * 事务开始日志记录（回调方法）
     */
    private static function logTransactionBegin(): void
    {
        $context = self::$transactionContext;

        $logData = [
            'message' => '事务开始',
            'transaction_id' => $context['transaction_id'] ?? 'unknown',
            'trace_id' => $context['trace_id'] ?? self::getTraceId(),
            'operation_type' => self::detectOperationType(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
            'memory_usage' => UnitFormatter::formatMemory(memory_get_usage(true)),
            'module' => $context['module'] ?? 'unknown',
            'action' => $context['action'] ?? 'unknown',
            'business_data' => $context['business_data'] ?? null,
        ];

        Log::channel('transaction')->info('事务开始', $logData);
    }

    /**
     * 获取 trace_id
     */
    private static function getTraceId(): string
    {
        return request()->headers->get('X-Request-ID') ?? 'unknown';
    }

    /**
     * 检测操作类型
     *
     * 优先使用事务上下文中的模块和操作信息，回退到 HTTP 请求推断
     */
    private static function detectOperationType(): string
    {
        $context = self::$transactionContext;

        // 优先使用事务上下文中的信息
        if (isset($context['module']) && isset($context['action'])
            && $context['module'] !== 'unknown' && $context['action'] !== 'unknown') {
            return strtolower($context['module']).'_'.$context['action'];
        }

        // 回退到 HTTP 请求推断（向后兼容）
        return self::detectOperationTypeFromHttp();
    }

    /**
     * 从 HTTP 请求推断操作类型（回退机制）
     */
    private static function detectOperationTypeFromHttp(): string
    {
        if (! app()->bound('request')) {
            return 'unknown';
        }

        $request = app('request');

        $method = $request->method();
        $path = $request->path();

        // 根据 HTTP 方法和路径推断操作类型
        if (str_contains($path, 'leads')) {
            return match ($method) {
                'POST' => 'lead_create',
                'PUT', 'PATCH' => 'lead_update',
                'DELETE' => 'lead_delete',
                'GET' => 'lead_read',
                default => 'lead_unknown',
            };
        }

        if (str_contains($path, 'contacts')) {
            return match ($method) {
                'POST' => 'contact_create',
                'PUT', 'PATCH' => 'contact_update',
                'DELETE' => 'contact_delete',
                'GET' => 'contact_read',
                default => 'contact_unknown',
            };
        }

        return 'unknown';
    }

    /**
     * 事务提交成功日志记录（回调方法）
     */
    private static function logTransactionCommit(): void
    {
        $context = self::$transactionContext;
        $startTime = $context['start_time'] ?? microtime(true);
        $startMemory = $context['start_memory'] ?? memory_get_usage(true);

        $logData = [
            'message' => '事务提交成功',
            'transaction_id' => $context['transaction_id'] ?? 'unknown',
            'trace_id' => $context['trace_id'] ?? self::getTraceId(),
            'operation_type' => self::detectOperationType(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
            'duration' => UnitFormatter::formatTime(microtime(true) - $startTime),
            'memory_usage' => UnitFormatter::formatMemory(memory_get_usage(true)),
            'memory_peak' => UnitFormatter::formatMemory(memory_get_peak_usage(true)),
            'memory_diff' => UnitFormatter::formatMemory(memory_get_usage(true) - $startMemory),
            'module' => $context['module'] ?? 'unknown',
            'action' => $context['action'] ?? 'unknown',
            'result' => $context['result'] ?? null,
        ];

        Log::channel('transaction')->info('事务提交成功', $logData);
        BusinessLog::info($logData);

        // 清理事务上下文
        self::$transactionContext = [];
    }

    /**
     * 事务回滚日志记录（回调方法）
     */
    private static function logTransactionRollback(): void
    {
        $context = self::$transactionContext;
        $startTime = $context['start_time'] ?? microtime(true);
        $startMemory = $context['start_memory'] ?? memory_get_usage(true);

        $logData = [
            'message' => '事务回滚',
            'transaction_id' => $context['transaction_id'] ?? 'unknown',
            'trace_id' => $context['trace_id'] ?? self::getTraceId(),
            'operation_type' => self::detectOperationType(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
            'duration' => UnitFormatter::formatTime(microtime(true) - $startTime),
            'memory_usage' => UnitFormatter::formatMemory(memory_get_usage(true)),
            'memory_peak' => UnitFormatter::formatMemory(memory_get_peak_usage(true)),
            'memory_diff' => UnitFormatter::formatMemory(memory_get_usage(true) - $startMemory),
            'module' => $context['module'] ?? 'unknown',
            'action' => $context['action'] ?? 'unknown',
            'error_context' => $context['error_context'] ?? null,
        ];

        Log::channel('transaction')->error('事务回滚', $logData);
        BusinessLog::error($logData);

        // 清理事务上下文
        self::$transactionContext = [];
    }

    /**
     * 线索删除事务日志（向后兼容方法）
     *
     * @param  callable(): mixed  $callback  事务回调函数
     * @param  int  $leadId  线索ID
     * @return mixed 回调函数返回值
     *
     * @deprecated 请使用 logBusinessTransaction() 方法
     */
    public static function logLeadDelete(callable $callback, int $leadId): mixed
    {
        return self::logBusinessTransaction(
            $callback,
            'Lead',
            'delete',
            [],
            ['lead_id' => $leadId]
        );
    }

    /**
     * 通用业务事务日志包装方法
     *
     * 为任意业务模块的事务操作提供统一的日志记录功能。
     * 该方法会自动设置事务上下文、处理异常、记录结果信息。
     *
     * @param  callable(): mixed  $callback  事务回调函数，包含具体的业务逻辑
     * @param  string  $module  业务模块名称（如 'Lead', 'Contact', 'User'）
     * @param  string  $action  操作类型（如 'create', 'update', 'delete'）
     * @param  array<string, mixed>  $businessData  业务数据，将记录在事务日志中
     * @param  array<string, mixed>  $additionalContext  可选的额外上下文信息
     * @return mixed 回调函数的返回值
     *
     * @throws Exception 当事务执行失败时抛出异常
     *
     * @example
     * // 线索创建示例
     * SimpleTransactionLogger::logBusinessTransaction(
     *     fn() => $this->transactionManager->executeInTransaction($callback),
     *     'Lead',
     *     'create',
     *     $leadData
     * );
     *
     * // 联系人更新示例
     * SimpleTransactionLogger::logBusinessTransaction(
     *     fn() => $this->transactionManager->executeInTransaction($callback),
     *     'Contact',
     *     'update',
     *     $contactData,
     *     ['contact_id' => $contactId]
     * );
     */
    public static function logBusinessTransaction(
        callable $callback,
        string $module,
        string $action,
        array $businessData,
        array $additionalContext = []
    ): mixed {
        // 构建基础事务上下文
        $baseContext = [
            'module' => $module,
            'action' => $action,
            'business_data' => $businessData,
        ];

        // 合并额外上下文信息
        $context = array_merge($baseContext, $additionalContext);

        // 设置事务上下文
        self::setTransactionContext($context);

        try {
            $result = $callback();

            // 更新结果上下文
            if ($result) {
                self::$transactionContext['result'] = self::extractResultContext($result, $module, $action);
            }

            return $result;
        } catch (Exception $e) {
            // 设置错误上下文
            self::$transactionContext['error_context'] = [
                'failed_data' => $businessData,
                'additional_context' => $additionalContext,
                'exception_class' => get_class($e),
                'exception_message' => $e->getMessage(),
                'exception_code' => $e->getCode(),
                'error_severity' => self::classifyErrorSeverity($e),
            ];
            throw $e;
        }
    }

    /**
     * 设置当前事务上下文
     *
     * @param  array<string, mixed>  $context  事务上下文数据
     */
    public static function setTransactionContext(array $context): void
    {
        $transactionId = 'tx_'.Str::uuid()->toString();
        self::$transactionContext = array_merge($context, [
            'transaction_id' => $transactionId,
            'trace_id' => self::getTraceId(),
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
        ]);
    }

    /**
     * 从结果中提取上下文信息
     *
     * 根据不同的模块和操作类型，从回调结果中提取相关的上下文信息
     *
     * @param  mixed  $result  回调函数返回值
     * @param  string  $module  业务模块名称
     * @param  string  $action  操作类型
     * @return array<string, mixed> 提取的结果上下文
     */
    private static function extractResultContext(mixed $result, string $module, string $action): array
    {
        $context = [
            'operation_success' => true,
            'module' => $module,
            'action' => $action,
        ];

        // 根据操作类型提取不同的信息
        switch ($action) {
            case 'create':
                if (is_object($result) && property_exists($result, 'id')) {
                    $context['created_id'] = $result->id;

                    // 根据模块提取特定信息
                    if ($module === 'Lead' && property_exists($result, 'company_full_name')) {
                        $context['company_name'] = $result->company_full_name;
                    } elseif ($module === 'Contact' && property_exists($result, 'name')) {
                        $context['contact_name'] = $result->name;
                    } elseif ($module === 'User' && property_exists($result, 'name')) {
                        $context['user_name'] = $result->name;
                    }
                }
                break;

            case 'update':
                if (is_bool($result)) {
                    $context['updated'] = $result;
                } elseif (is_object($result) && property_exists($result, 'id')) {
                    $context['updated'] = true;
                    $context['updated_id'] = $result->id;
                }
                break;

            case 'delete':
                if (is_bool($result)) {
                    $context['deleted'] = $result;
                } else {
                    $context['deleted'] = true;
                }
                break;

            default:
                $context['result_type'] = gettype($result);
                if (is_object($result) && property_exists($result, 'id')) {
                    $context['entity_id'] = $result->id;
                }
                break;
        }

        return $context;
    }

    /**
     * 分类错误严重程度
     */
    private static function classifyErrorSeverity(Exception $exception): string
    {
        $message = $exception->getMessage();

        if (str_contains($message, 'Deadlock') || str_contains($message, 'Lock wait timeout')) {
            return 'medium'; // 死锁通常可以重试
        }

        if (str_contains($message, 'Duplicate entry') || str_contains($message, 'Integrity constraint')) {
            return 'low'; // 数据约束错误通常是业务逻辑问题
        }

        if (str_contains($message, 'Connection') || str_contains($message, 'Server has gone away')) {
            return 'high'; // 连接问题比较严重
        }

        return 'medium';
    }
}

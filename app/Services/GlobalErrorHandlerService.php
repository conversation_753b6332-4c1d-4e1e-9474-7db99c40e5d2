<?php

namespace App\Services;

use ErrorException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * 全局错误处理服务
 *
 * 处理所有类型的错误，包括 Fatal Error、Parse Error 等无法被 Exception 捕获的错误
 */
class GlobalErrorHandlerService
{
    private static bool $registered = false;

    private static array $errorContext = [];

    /**
     * 注册全局错误处理器
     */
    public static function register(): void
    {
        if (self::$registered) {
            return;
        }

        // 注册错误处理器
        set_error_handler([self::class, 'handleError']);

        // 注册致命错误处理器
        register_shutdown_function([self::class, 'handleFatalError']);

        // 注册异常处理器
        set_exception_handler([self::class, 'handleException']);
        // 兜底错误日志
        ini_set('error_log', storage_path('logs/php_error.log'));

        self::$registered = true;
    }

    /**
     * 设置错误上下文信息
     */
    public static function setErrorContext(array $context): void
    {
        self::$errorContext = array_merge(self::$errorContext, $context);
    }

    /**
     * 处理 PHP 错误
     *
     * @param  int  $severity  错误级别
     * @param  string  $message  错误消息
     * @param  string  $file  错误文件
     * @param  int  $line  错误行号
     */
    public static function handleError(int $severity, string $message, string $file, int $line): bool
    {
        // 检查错误报告级别
        if (! (error_reporting() & $severity)) {
            return false;
        }

        $errorData = [
            'type' => 'PHP_ERROR',
            'severity' => self::getSeverityName($severity),
            'severity_code' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => now()->toISOString(),
            'context' => self::$errorContext,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];

        // 记录错误日志
        self::logError($errorData, $severity);

        // 对于致命错误，抛出异常以便统一处理
        if (self::isFatalError($severity)) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        }

        return true;
    }

    /**
     * 处理致命错误
     */
    public static function handleFatalError(): void
    {
        $error = error_get_last();

        if ($error && self::isFatalError($error['type'])) {
            $errorData = [
                'type' => 'FATAL_ERROR',
                'severity' => self::getSeverityName($error['type']),
                'severity_code' => $error['type'],
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => now()->toISOString(),
                'context' => self::$errorContext,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
            ];

            // 记录致命错误
            self::logFatalError($errorData);

            // 如果是 API 请求，尝试返回 JSON 响应
            if (self::isApiRequest()) {
                self::sendFatalErrorResponse($errorData);
            }
        }
    }

    /**
     * 处理未捕获的异常
     */
    public static function handleException(Throwable $exception): void
    {
        $errorData = [
            'type' => 'UNCAUGHT_EXCEPTION',
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => now()->toISOString(),
            'context' => self::$errorContext,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];

        // 记录异常日志
        self::logError($errorData, E_ERROR);

        // 如果是 API 请求，返回 JSON 响应
        if (self::isApiRequest()) {
            self::sendErrorResponse($errorData, 500);
        }
    }

    /**
     * 记录错误日志
     */
    private static function logError(array $errorData, int $severity): void
    {
        try {
            $channel = self::isFatalError($severity) ? 'fatal_error' : 'error';

            Log::channel($channel)->error('Global Error Captured', $errorData);

            // 同时记录到系统日志作为备份
            Log::channel('single')->error('Global Error Backup', [
                'error_type' => $errorData['type'],
                'message' => $errorData['message'],
                'file' => $errorData['file'] ?? 'unknown',
                'line' => $errorData['line'] ?? 0,
            ]);

        } catch (Throwable $e) {
            // 日志记录失败时使用 error_log
            error_log('Critical: Failed to log error - '.json_encode($errorData));
        }
    }

    /**
     * 记录致命错误日志
     */
    private static function logFatalError(array $errorData): void
    {
        try {
            Log::channel('fatal_error')->emergency('Fatal Error Occurred', $errorData);

            // 使用 error_log 作为最后的备份
            error_log('FATAL ERROR: '.$errorData['message'].' in '.$errorData['file'].':'.$errorData['line']);

        } catch (Throwable $e) {
            error_log('Critical: Failed to log fatal error - '.json_encode($errorData));
        }
    }

    /**
     * 发送致命错误响应
     */
    private static function sendFatalErrorResponse(array $errorData): void
    {
        try {
            // 清理输出缓冲区
            while (ob_get_level()) {
                ob_end_clean();
            }

            // 设置响应头
            http_response_code(500);
            header('Content-Type: application/json; charset=utf-8');

            $errors = [
                'details' => $errorData,
            ];

            // 从错误上下文中获取 trace_id 并添加到 errors 中
            $context = self::getErrorContext();
            if (isset($context['trace_id'])) {
                $errors['trace_id'] = $context['trace_id'];
            }

            $response = [
                'code' => 500,
                'message' => '服务器内部错误',
                'errors' => $errors,
            ];

            echo json_encode($response, JSON_UNESCAPED_UNICODE);

        } catch (Throwable $e) {
            // 最后的备用响应
            echo '{"code":500,"message":"服务器发生致命错误","errors":{}}';
        }

        exit(1);
    }

    /**
     * 发送错误响应
     */
    private static function sendErrorResponse(array $errorData, int $httpCode = 500): void
    {
        try {
            $errors = [
                'details' => $errorData,
            ];

            // 从错误上下文中获取 trace_id 并添加到 errors 中
            $context = self::getErrorContext();
            if (isset($context['trace_id'])) {
                $errors['trace_id'] = $context['trace_id'];
            }

            $response = [
                'code' => $httpCode,
                'message' => '服务器内部错误',
                'errors' => $errors,
            ];

            $response = new JsonResponse($response, $httpCode);
            $response->send();

        } catch (Throwable $e) {
            self::sendFatalErrorResponse($errorData);
        }

        exit(1);
    }

    /**
     * 检查是否为致命错误
     */
    private static function isFatalError(int $severity): bool
    {
        return in_array($severity, [
            E_ERROR,
            E_PARSE,
            E_CORE_ERROR,
            E_CORE_WARNING,
            E_COMPILE_ERROR,
            E_COMPILE_WARNING,
            E_USER_ERROR,
        ]);
    }

    /**
     * 获取错误级别名称
     */
    private static function getSeverityName(int $severity): string
    {
        $severityNames = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED',
        ];

        return $severityNames[$severity] ?? 'UNKNOWN_ERROR';
    }

    /**
     * 检查是否为 API 请求
     */
    private static function isApiRequest(): bool
    {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';

        return str_contains($requestUri, '/api/') ||
               str_contains($acceptHeader, 'application/json') ||
               isset($_SERVER['HTTP_X_REQUESTED_WITH']);
    }

    /**
     * 获取当前错误上下文
     */
    public static function getErrorContext(): array
    {
        return self::$errorContext;
    }

    /**
     * 清理错误上下文
     */
    public static function clearErrorContext(): void
    {
        self::$errorContext = [];
    }
}

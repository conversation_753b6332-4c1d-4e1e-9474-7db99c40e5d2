<?php

namespace App\Services\Database;

use App\Contracts\QueryBuilderInterface;
use App\Exceptions\QueryOptimizationException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 增强查询构建器实现
 *
 * 提供复杂查询构建、动态排序、查询优化等功能
 */
class EnhancedQueryBuilder implements QueryBuilderInterface
{
    /**
     * 缓存管理器实例
     */
    protected CacheManager $cacheManager;

    /**
     * 支持的查询操作符
     *
     * @var array<string>
     */
    protected array $supportedOperators = [
        '=', '!=', '<>', '>', '>=', '<', '<=',
        'like', 'not like', 'ilike', 'not ilike',
        'in', 'not in', 'between', 'not between',
        'null', 'not null', 'exists', 'not exists',
    ];

    /**
     * 支持的排序方向
     *
     * @var array<string>
     */
    protected array $supportedSortDirections = ['asc', 'desc'];

    /**
     * 查询复杂度计算权重
     *
     * @var array<string, int>
     */
    protected array $complexityWeights = [
        'where' => 1,
        'join' => 3,
        'subquery' => 5,
        'union' => 4,
        'group_by' => 2,
        'having' => 2,
        'order_by' => 1,
    ];

    /**
     * 最大查询复杂度
     */
    protected int $maxComplexity = 100;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->cacheManager = new CacheManager;
    }

    /**
     * 构建复杂查询条件
     *
     * @param  array<string, mixed>  $conditions  查询条件数组
     * @param  Builder|QueryBuilder|null  $query  基础查询构建器
     *
     * @throws QueryOptimizationException
     */
    public function buildComplexQuery(array $conditions, Builder|QueryBuilder|null $query = null): Builder|QueryBuilder
    {
        try {
            // 验证查询条件
            $this->validateConditions($conditions);

            // 如果没有提供查询构建器，创建一个基础的
            if ($query === null) {
                $query = DB::table('dummy'); // 需要在实际使用时替换为具体表名
            }

            // 处理基础条件
            $query = $this->applyBasicConditions($query, $conditions);

            // 处理范围条件
            $query = $this->applyRangeConditions($query, $conditions);

            // 处理搜索条件
            $query = $this->applySearchConditions($query, $conditions);

            // 处理关联条件
            $query = $this->applyRelationConditions($query, $conditions);

            // 检查查询复杂度
            $this->validateQueryComplexity($query);

            return $query;

        } catch (\Exception $e) {
            Log::error('查询构建失败', [
                'conditions' => $conditions,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            if ($e instanceof QueryOptimizationException) {
                throw $e;
            }

            throw QueryOptimizationException::optimizationFailed(
                $e->getMessage(),
                ['conditions' => $conditions]
            );
        }
    }

    /**
     * 添加动态排序
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $sortRules  排序规则数组
     */
    public function addDynamicSorting(Builder|QueryBuilder $query, array $sortRules): Builder|QueryBuilder
    {
        if (empty($sortRules)) {
            return $query;
        }

        try {
            foreach ($sortRules as $rule) {
                if (is_string($rule)) {
                    // 简单格式：'field_name' 或 'field_name:desc'
                    $this->applySingleSortRule($query, $rule);
                } elseif (is_array($rule)) {
                    // 复杂格式：['field' => 'name', 'direction' => 'desc', 'nulls' => 'last']
                    $this->applyComplexSortRule($query, $rule);
                }
            }

            return $query;

        } catch (\Exception $e) {
            Log::warning('动态排序应用失败', [
                'sort_rules' => $sortRules,
                'error' => $e->getMessage(),
            ]);

            // 排序失败不应该中断查询，返回原查询
            return $query;
        }
    }

    /**
     * 优化查询性能
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $options  优化选项
     */
    public function optimizeQuery(Builder|QueryBuilder $query, array $options = []): Builder|QueryBuilder
    {
        try {
            // 添加查询提示
            if ($options['use_index'] ?? false) {
                $query = $this->addIndexHints($query, $options['indexes'] ?? []);
            }

            // 优化 SELECT 字段
            if ($options['optimize_select'] ?? true) {
                $query = $this->optimizeSelectFields($query, $options);
            }

            // 优化 JOIN 顺序
            if ($options['optimize_joins'] ?? true) {
                $query = $this->optimizeJoinOrder($query);
            }

            // 添加查询缓存
            if ($options['cache'] ?? false) {
                $query = $this->setCacheForQuery(
                    $query,
                    $options['cache_ttl'] ?? 3600,
                    $options['cache_key'] ?? null
                );
            }

            return $query;

        } catch (\Exception $e) {
            Log::warning('查询优化失败', [
                'options' => $options,
                'error' => $e->getMessage(),
            ]);

            // 优化失败不应该中断查询，返回原查询
            return $query;
        }
    }

    /**
     * 构建范围查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $field  字段名
     * @param  mixed  $start  开始值
     * @param  mixed  $end  结束值
     * @param  string  $type  范围类型
     */
    public function addRangeCondition(
        Builder|QueryBuilder $query,
        string $field,
        mixed $start,
        mixed $end,
        string $type = 'between'
    ): Builder|QueryBuilder {
        try {
            switch ($type) {
                case 'between':
                    return $query->whereBetween($field, [$start, $end]);

                case 'date_range':
                    return $query->whereDate($field, '>=', $start)
                        ->whereDate($field, '<=', $end);

                case 'number_range':
                    return $query->where($field, '>=', $start)
                        ->where($field, '<=', $end);

                case 'time_range':
                    return $query->whereTime($field, '>=', $start)
                        ->whereTime($field, '<=', $end);

                default:
                    throw QueryOptimizationException::unsupportedOperation(
                        "范围查询类型：{$type}",
                        ['field' => $field, 'start' => $start, 'end' => $end]
                    );
            }

        } catch (\Exception $e) {
            Log::error('范围条件构建失败', [
                'field' => $field,
                'start' => $start,
                'end' => $end,
                'type' => $type,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 构建模糊匹配条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string>  $fields  搜索字段数组
     * @param  string  $keyword  搜索关键词
     * @param  string  $matchType  匹配类型
     */
    public function addSearchCondition(
        Builder|QueryBuilder $query,
        array $fields,
        string $keyword,
        string $matchType = 'like'
    ): Builder|QueryBuilder {
        if (empty($fields) || empty($keyword)) {
            return $query;
        }

        try {
            return $query->where(function ($subQuery) use ($fields, $keyword, $matchType) {
                foreach ($fields as $field) {
                    switch ($matchType) {
                        case 'like':
                            $subQuery->orWhere($field, 'like', "%{$keyword}%");
                            break;

                        case 'exact':
                            $subQuery->orWhere($field, '=', $keyword);
                            break;

                        case 'starts_with':
                            $subQuery->orWhere($field, 'like', "{$keyword}%");
                            break;

                        case 'ends_with':
                            $subQuery->orWhere($field, 'like', "%{$keyword}");
                            break;

                        case 'full_text':
                            // MySQL 全文搜索
                            $subQuery->orWhereRaw("MATCH({$field}) AGAINST(? IN BOOLEAN MODE)", [$keyword]);
                            break;

                        default:
                            $subQuery->orWhere($field, 'like', "%{$keyword}%");
                    }
                }
            });

        } catch (\Exception $e) {
            Log::error('搜索条件构建失败', [
                'fields' => $fields,
                'keyword' => $keyword,
                'match_type' => $matchType,
                'error' => $e->getMessage(),
            ]);

            throw QueryOptimizationException::optimizationFailed(
                "搜索条件构建失败：{$e->getMessage()}",
                ['fields' => $fields, 'keyword' => $keyword]
            );
        }
    }

    /**
     * 构建关联查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $relation  关联关系名
     * @param  array<string, mixed>  $conditions  关联条件
     */
    public function addRelationCondition(
        Builder|QueryBuilder $query,
        string $relation,
        array $conditions
    ): Builder|QueryBuilder {
        if (empty($conditions)) {
            return $query;
        }

        try {
            // 只有 Eloquent Builder 支持关联查询
            if (! ($query instanceof Builder)) {
                throw QueryOptimizationException::unsupportedOperation(
                    '关联查询仅支持 Eloquent Builder',
                    ['relation' => $relation, 'conditions' => $conditions]
                );
            }

            return $query->whereHas($relation, function ($subQuery) use ($conditions) {
                $this->applyBasicConditions($subQuery, $conditions);
            });

        } catch (\Exception $e) {
            Log::error('关联条件构建失败', [
                'relation' => $relation,
                'conditions' => $conditions,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 获取查询优化建议
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed>
     */
    public function getOptimizationSuggestions(Builder|QueryBuilder $query): array
    {
        $suggestions = [];

        try {
            // 分析查询结构
            $sql = $query->toSql();
            $bindings = $query->getBindings();

            // 检查是否使用了索引
            if (str_contains(strtolower($sql), 'where')) {
                $suggestions[] = [
                    'type' => 'index',
                    'message' => '建议为 WHERE 条件中的字段添加索引',
                    'priority' => 'high',
                ];
            }

            // 检查是否有 SELECT *
            if (str_contains($sql, 'select *')) {
                $suggestions[] = [
                    'type' => 'select',
                    'message' => '建议明确指定需要的字段，避免使用 SELECT *',
                    'priority' => 'medium',
                ];
            }

            // 检查是否有复杂的 JOIN
            $joinCount = substr_count(strtolower($sql), 'join');
            if ($joinCount > 3) {
                $suggestions[] = [
                    'type' => 'join',
                    'message' => "查询包含 {$joinCount} 个 JOIN，建议考虑查询拆分或添加索引",
                    'priority' => 'high',
                ];
            }

            // 检查是否有子查询
            if (str_contains(strtolower($sql), 'select') && substr_count(strtolower($sql), 'select') > 1) {
                $suggestions[] = [
                    'type' => 'subquery',
                    'message' => '查询包含子查询，建议考虑使用 JOIN 替代',
                    'priority' => 'medium',
                ];
            }

            return [
                'suggestions' => $suggestions,
                'complexity_score' => $this->calculateQueryComplexity($query),
                'estimated_cost' => $this->estimateQueryCost($query),
                'sql' => $sql,
                'bindings_count' => count($bindings),
            ];

        } catch (\Exception $e) {
            Log::warning('获取查询优化建议失败', [
                'error' => $e->getMessage(),
            ]);

            return [
                'suggestions' => [],
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 分析查询执行计划
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed>
     */
    public function analyzeQueryPlan(Builder|QueryBuilder $query): array
    {
        try {
            $sql = $query->toSql();
            $bindings = $query->getBindings();

            // 执行 EXPLAIN 分析
            $explainSql = 'EXPLAIN '.$sql;
            $explainResult = DB::select($explainSql, $bindings);

            // 分析执行计划
            $analysis = [
                'explain_result' => $explainResult,
                'performance_issues' => [],
                'recommendations' => [],
            ];

            foreach ($explainResult as $row) {
                $row = (array) $row;

                // 检查是否使用了索引
                if (isset($row['key']) && empty($row['key'])) {
                    $analysis['performance_issues'][] = [
                        'type' => 'no_index',
                        'table' => $row['table'] ?? 'unknown',
                        'message' => '表扫描未使用索引',
                    ];
                }

                // 检查扫描行数
                if (isset($row['rows']) && $row['rows'] > 1000) {
                    $analysis['performance_issues'][] = [
                        'type' => 'high_row_scan',
                        'table' => $row['table'] ?? 'unknown',
                        'rows' => $row['rows'],
                        'message' => "扫描行数过多：{$row['rows']}",
                    ];
                }

                // 检查是否使用了临时表
                if (isset($row['Extra']) && str_contains($row['Extra'], 'Using temporary')) {
                    $analysis['performance_issues'][] = [
                        'type' => 'temporary_table',
                        'table' => $row['table'] ?? 'unknown',
                        'message' => '使用了临时表，可能影响性能',
                    ];
                }
            }

            return $analysis;

        } catch (\Exception $e) {
            Log::warning('查询执行计划分析失败', [
                'error' => $e->getMessage(),
            ]);

            return [
                'explain_result' => [],
                'performance_issues' => [],
                'recommendations' => [],
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 构建分页查询
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  int  $page  页码
     * @param  int  $perPage  每页数量
     * @param  array<string>  $columns  查询字段
     */
    public function buildPaginatedQuery(
        Builder|QueryBuilder $query,
        int $page = 1,
        int $perPage = 15,
        array $columns = ['*']
    ): LengthAwarePaginator {
        try {
            // 验证分页参数
            if ($page < 1) {
                $page = 1;
            }

            if ($perPage < 1 || $perPage > 1000) {
                $perPage = 15;
            }

            return $query->paginate($perPage, $columns, 'page', $page);

        } catch (\Exception $e) {
            Log::error('分页查询构建失败', [
                'page' => $page,
                'per_page' => $perPage,
                'error' => $e->getMessage(),
            ]);

            throw QueryOptimizationException::optimizationFailed(
                "分页查询构建失败：{$e->getMessage()}",
                ['page' => $page, 'per_page' => $perPage]
            );
        }
    }

    /**
     * 应用基础查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $conditions  查询条件
     */
    protected function applyBasicConditions(Builder|QueryBuilder $query, array $conditions): Builder|QueryBuilder
    {
        // 定义特殊字段，这些字段不应该作为普通数据库字段处理
        $specialFields = ['search', 'relations', 'sort', 'company_name'];

        foreach ($conditions as $field => $condition) {
            // 跳过特殊字段，这些字段由专门的方法处理
            if (in_array($field, $specialFields)) {
                continue;
            }

            // 跳过以特殊后缀结尾的字段（如 _range）
            if (str_ends_with($field, '_range')) {
                continue;
            }

            if (is_array($condition)) {
                // 检查是否是搜索条件的格式
                if (isset($condition['keyword']) && isset($condition['fields'])) {
                    // 这是搜索条件，跳过在基础条件中处理
                    continue;
                }

                // 复杂条件格式：['operator' => '=', 'value' => 'test']
                $operator = $condition['operator'] ?? '=';
                $value = $condition['value'] ?? null;

                if (! in_array($operator, $this->supportedOperators)) {
                    continue;
                }

                switch ($operator) {
                    case 'in':
                        if (is_array($value)) {
                            $query->whereIn($field, $value);
                        }
                        break;

                    case 'not in':
                        if (is_array($value)) {
                            $query->whereNotIn($field, $value);
                        }
                        break;

                    case 'null':
                        $query->whereNull($field);
                        break;

                    case 'not null':
                        $query->whereNotNull($field);
                        break;

                    case 'between':
                        if (is_array($value) && count($value) === 2) {
                            $query->whereBetween($field, $value);
                        }
                        break;

                    case 'not between':
                        if (is_array($value) && count($value) === 2) {
                            $query->whereNotBetween($field, $value);
                        }
                        break;

                    default:
                        $query->where($field, $operator, $value);
                }
            } else {
                // 简单条件格式：直接等值比较
                $query->where($field, '=', $condition);
            }
        }

        return $query;
    }

    /**
     * 应用范围查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $conditions  查询条件
     */
    protected function applyRangeConditions(Builder|QueryBuilder $query, array $conditions): Builder|QueryBuilder
    {
        // 处理特殊的范围条件
        foreach ($conditions as $key => $value) {
            if (str_ends_with($key, '_range') && is_array($value)) {
                $field = str_replace('_range', '', $key);
                $start = $value['start'] ?? $value[0] ?? null;
                $end = $value['end'] ?? $value[1] ?? null;
                $type = $value['type'] ?? 'between';

                if ($start !== null && $end !== null) {
                    $this->addRangeCondition($query, $field, $start, $end, $type);
                }
            }
        }

        return $query;
    }

    /**
     * 应用搜索条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $conditions  查询条件
     */
    protected function applySearchConditions(Builder|QueryBuilder $query, array $conditions): Builder|QueryBuilder
    {
        // 处理搜索条件
        if (isset($conditions['search'])) {
            $searchConfig = $conditions['search'];
            $keyword = $searchConfig['keyword'] ?? '';
            $fields = $searchConfig['fields'] ?? [];
            $matchType = $searchConfig['match_type'] ?? 'like';

            if (! empty($keyword) && ! empty($fields)) {
                $this->addSearchCondition($query, $fields, $keyword, $matchType);
            }
        }

        return $query;
    }

    /**
     * 应用关联查询条件
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $conditions  查询条件
     */
    protected function applyRelationConditions(Builder|QueryBuilder $query, array $conditions): Builder|QueryBuilder
    {
        // 处理关联条件
        if (isset($conditions['relations']) && is_array($conditions['relations'])) {
            foreach ($conditions['relations'] as $relation => $relationConditions) {
                if (is_array($relationConditions)) {
                    $this->addRelationCondition($query, $relation, $relationConditions);
                }
            }
        }

        return $query;
    }

    /**
     * 应用单个排序规则
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $rule  排序规则
     */
    protected function applySingleSortRule(Builder|QueryBuilder $query, string $rule): void
    {
        if (str_contains($rule, ':')) {
            [$field, $direction] = explode(':', $rule, 2);
            $direction = strtolower(trim($direction));
        } else {
            $field = $rule;
            $direction = 'asc';
        }

        $field = trim($field);

        if (! empty($field) && in_array($direction, $this->supportedSortDirections)) {
            $query->orderBy($field, $direction);
        }
    }

    /**
     * 应用复杂排序规则
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $rule  排序规则
     */
    protected function applyComplexSortRule(Builder|QueryBuilder $query, array $rule): void
    {
        $field = $rule['field'] ?? '';
        $direction = strtolower($rule['direction'] ?? 'asc');

        if (empty($field) || ! in_array($direction, $this->supportedSortDirections)) {
            return;
        }

        // 处理 NULL 值排序
        if (isset($rule['nulls'])) {
            $nullsPosition = strtolower($rule['nulls']);
            if (in_array($nullsPosition, ['first', 'last'])) {
                $query->orderByRaw("ISNULL({$field}) ".($nullsPosition === 'first' ? 'DESC' : 'ASC'));
            }
        }

        $query->orderBy($field, $direction);
    }

    /**
     * 计算查询复杂度
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     */
    protected function calculateQueryComplexity(Builder|QueryBuilder $query): int
    {
        $sql = strtolower($query->toSql());
        $complexity = 0;

        // 计算各种操作的复杂度
        $complexity += substr_count($sql, 'where') * $this->complexityWeights['where'];
        $complexity += substr_count($sql, 'join') * $this->complexityWeights['join'];
        $complexity += (substr_count($sql, 'select') - 1) * $this->complexityWeights['subquery']; // 减1是因为主查询的select
        $complexity += substr_count($sql, 'union') * $this->complexityWeights['union'];
        $complexity += substr_count($sql, 'group by') * $this->complexityWeights['group_by'];
        $complexity += substr_count($sql, 'having') * $this->complexityWeights['having'];
        $complexity += substr_count($sql, 'order by') * $this->complexityWeights['order_by'];

        return $complexity;
    }

    /**
     * 验证查询复杂度
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     *
     * @throws QueryOptimizationException
     */
    protected function validateQueryComplexity(Builder|QueryBuilder $query): void
    {
        $complexity = $this->calculateQueryComplexity($query);

        if ($complexity > $this->maxComplexity) {
            throw QueryOptimizationException::queryTooComplex(
                $complexity,
                $this->maxComplexity,
                ['sql' => $query->toSql()]
            );
        }
    }

    /**
     * 估算查询成本
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     */
    protected function estimateQueryCost(Builder|QueryBuilder $query): int
    {
        // 简单的查询成本估算
        $complexity = $this->calculateQueryComplexity($query);
        $bindingsCount = count($query->getBindings());

        return $complexity * 10 + $bindingsCount;
    }

    /**
     * 添加索引提示
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string>  $indexes  索引名称数组
     */
    protected function addIndexHints(Builder|QueryBuilder $query, array $indexes): Builder|QueryBuilder
    {
        // MySQL 索引提示实现
        foreach ($indexes as $index) {
            $query->from(DB::raw($query->from." USE INDEX ({$index})"));
        }

        return $query;
    }

    /**
     * 优化 SELECT 字段
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $options  优化选项
     */
    protected function optimizeSelectFields(Builder|QueryBuilder $query, array $options): Builder|QueryBuilder
    {
        // 如果指定了需要的字段，替换 SELECT *
        if (isset($options['select_fields']) && is_array($options['select_fields'])) {
            $query->select($options['select_fields']);
        }

        return $query;
    }

    /**
     * 优化 JOIN 顺序
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     */
    protected function optimizeJoinOrder(Builder|QueryBuilder $query): Builder|QueryBuilder
    {
        // 这里可以实现 JOIN 顺序优化逻辑
        // 目前返回原查询
        return $query;
    }

    /**
     * 构建聚合查询
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  array<string, mixed>  $aggregations  聚合配置
     * @return array<string, mixed>
     */
    public function buildAggregateQuery(Builder|QueryBuilder $query, array $aggregations): array
    {
        $results = [];

        try {
            foreach ($aggregations as $type => $config) {
                switch ($type) {
                    case 'count':
                        $field = $config['field'] ?? '*';
                        $results['count'] = $query->count($field);
                        break;

                    case 'sum':
                        if (isset($config['field'])) {
                            $results['sum'] = $query->sum($config['field']);
                        }
                        break;

                    case 'avg':
                        if (isset($config['field'])) {
                            $results['avg'] = $query->avg($config['field']);
                        }
                        break;

                    case 'max':
                        if (isset($config['field'])) {
                            $results['max'] = $query->max($config['field']);
                        }
                        break;

                    case 'min':
                        if (isset($config['field'])) {
                            $results['min'] = $query->min($config['field']);
                        }
                        break;

                    case 'group_count':
                        if (isset($config['field'])) {
                            $results['group_count'] = $query->groupBy($config['field'])
                                ->selectRaw("{$config['field']}, COUNT(*) as count")
                                ->get()
                                ->toArray();
                        }
                        break;
                }
            }

            return $results;

        } catch (\Exception $e) {
            Log::error('聚合查询构建失败', [
                'aggregations' => $aggregations,
                'error' => $e->getMessage(),
            ]);

            throw QueryOptimizationException::optimizationFailed(
                "聚合查询构建失败：{$e->getMessage()}",
                ['aggregations' => $aggregations]
            );
        }
    }

    /**
     * 构建批量查询
     *
     * @param  array<array<string, mixed>>  $conditions  多个查询条件
     * @param  string  $model  模型类名
     * @return array<int, \Illuminate\Support\Collection>
     */
    public function buildBatchQuery(array $conditions, string $model): array
    {
        $results = [];

        try {
            foreach ($conditions as $index => $condition) {
                $query = $model::query();
                $query = $this->buildComplexQuery($condition, $query);
                $results[$index] = $query->get();
            }

            return $results;

        } catch (\Exception $e) {
            Log::error('批量查询构建失败', [
                'conditions_count' => count($conditions),
                'model' => $model,
                'error' => $e->getMessage(),
            ]);

            throw QueryOptimizationException::optimizationFailed(
                "批量查询构建失败：{$e->getMessage()}",
                ['model' => $model, 'conditions_count' => count($conditions)]
            );
        }
    }

    /**
     * 验证查询条件
     *
     * @param  array<string, mixed>  $conditions  查询条件
     * @param  array<string>  $allowedFields  允许的字段列表
     *
     * @throws \InvalidArgumentException
     */
    public function validateConditions(array $conditions, array $allowedFields = []): bool
    {
        try {
            foreach ($conditions as $field => $condition) {
                // 跳过特殊字段
                if (in_array($field, ['search', 'relations', 'sort'])) {
                    continue;
                }

                // 检查字段是否在允许列表中
                if (! empty($allowedFields) && ! in_array($field, $allowedFields)) {
                    throw QueryOptimizationException::fieldValidationFailed(
                        [$field],
                        $allowedFields,
                        ['invalid_field' => $field]
                    );
                }

                // 验证条件格式
                if (is_array($condition)) {
                    if (isset($condition['operator'])) {
                        $operator = $condition['operator'];
                        if (! in_array($operator, $this->supportedOperators)) {
                            throw QueryOptimizationException::unsupportedOperation(
                                "查询操作符：{$operator}",
                                ['field' => $field, 'operator' => $operator]
                            );
                        }
                    }
                }
            }

            return true;

        } catch (QueryOptimizationException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw QueryOptimizationException::invalidConditions(
                $conditions,
                ['validation_error' => $e->getMessage()]
            );
        }
    }

    /**
     * 获取查询统计信息
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @return array<string, mixed>
     */
    public function getQueryStatistics(Builder|QueryBuilder $query): array
    {
        try {
            $sql = $query->toSql();
            $bindings = $query->getBindings();

            return [
                'sql' => $sql,
                'bindings_count' => count($bindings),
                'complexity_score' => $this->calculateQueryComplexity($query),
                'estimated_cost' => $this->estimateQueryCost($query),
                'has_joins' => str_contains(strtolower($sql), 'join'),
                'has_subqueries' => substr_count(strtolower($sql), 'select') > 1,
                'has_aggregations' => str_contains(strtolower($sql), 'group by') ||
                                    str_contains(strtolower($sql), 'having'),
                'query_type' => $this->detectQueryType($sql),
                'table_count' => $this->countTables($sql),
            ];

        } catch (\Exception $e) {
            Log::warning('获取查询统计信息失败', [
                'error' => $e->getMessage(),
            ]);

            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 设置查询缓存
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  int  $ttl  缓存时间（秒）
     * @param  string|null  $cacheKey  缓存键
     */
    public function setCacheForQuery(
        Builder|QueryBuilder $query,
        int $ttl = 3600,
        ?string $cacheKey = null
    ): Builder|QueryBuilder {
        try {
            if ($cacheKey === null) {
                $cacheKey = $this->cacheManager->generateCacheKey($query);
            }

            // 尝试从缓存获取结果
            $cachedResult = $this->cacheManager->get($cacheKey);
            if ($cachedResult !== null) {
                // 返回缓存的查询结果
                return $this->buildQueryFromCache($query, $cachedResult);
            }

            // 对于 Eloquent Builder，可以使用 remember 方法
            if ($query instanceof Builder && method_exists($query, 'remember')) {
                return $query->remember($ttl);
            }

            // 缓存未命中，需要执行查询并缓存结果
            return $this->executeAndCacheQuery($query, $cacheKey, $ttl);

        } catch (\Exception $e) {
            Log::warning('设置查询缓存失败', [
                'cache_key' => $cacheKey,
                'ttl' => $ttl,
                'error' => $e->getMessage(),
            ]);

            return $query;
        }
    }

    /**
     * 清除查询缓存
     *
     * @param  string|array<string>  $cacheKeys  缓存键或缓存键数组
     */
    public function clearQueryCache(string|array $cacheKeys): bool
    {
        try {
            $deletedCount = $this->cacheManager->forget($cacheKeys);

            return $deletedCount > 0;

        } catch (\Exception $e) {
            Log::error('清除查询缓存失败', [
                'cache_keys' => $cacheKeys,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 生成缓存键
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     */
    protected function generateCacheKey(Builder|QueryBuilder $query): string
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        return 'query_cache:'.md5($sql.serialize($bindings));
    }

    /**
     * 检测查询类型
     *
     * @param  string  $sql  SQL 语句
     */
    protected function detectQueryType(string $sql): string
    {
        $sql = strtolower(trim($sql));

        if (str_starts_with($sql, 'select')) {
            return 'select';
        } elseif (str_starts_with($sql, 'insert')) {
            return 'insert';
        } elseif (str_starts_with($sql, 'update')) {
            return 'update';
        } elseif (str_starts_with($sql, 'delete')) {
            return 'delete';
        } else {
            return 'unknown';
        }
    }

    /**
     * 统计查询中的表数量
     *
     * @param  string  $sql  SQL 语句
     */
    protected function countTables(string $sql): int
    {
        $sql = strtolower($sql);

        // 简单统计 FROM 和 JOIN 中的表
        $fromCount = substr_count($sql, 'from ');
        $joinCount = substr_count($sql, 'join ');

        return $fromCount + $joinCount;
    }

    /**
     * 从缓存构建查询结果
     *
     * @param  Builder|QueryBuilder  $query  原查询构建器
     * @param  mixed  $cachedResult  缓存的结果
     */
    protected function buildQueryFromCache(Builder|QueryBuilder $query, mixed $cachedResult): Builder|QueryBuilder
    {
        // 这里可以根据需要实现从缓存结果构建查询的逻辑
        // 目前返回原查询，实际应用中可能需要更复杂的处理
        return $query;
    }

    /**
     * 执行查询并缓存结果
     *
     * @param  Builder|QueryBuilder  $query  查询构建器
     * @param  string  $cacheKey  缓存键
     * @param  int  $ttl  缓存时间
     */
    protected function executeAndCacheQuery(Builder|QueryBuilder $query, string $cacheKey, int $ttl): Builder|QueryBuilder
    {
        try {
            // 执行查询（这里只是示例，实际可能需要不同的处理）
            $startTime = microtime(true);

            // 对于实际应用，这里需要根据查询类型执行相应的操作
            // 目前返回原查询，让调用方处理实际的查询执行和缓存

            $executionTime = microtime(true) - $startTime;

            Log::debug('查询执行完成，准备缓存', [
                'cache_key' => $cacheKey,
                'execution_time' => $executionTime,
                'ttl' => $ttl,
            ]);

            return $query;

        } catch (\Exception $e) {
            Log::error('执行查询并缓存失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);

            return $query;
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return array<string, mixed>
     */
    public function getCacheStatistics(): array
    {
        return $this->cacheManager->getStatistics();
    }

    /**
     * 根据表名失效相关缓存
     *
     * @param  string  $tableName  表名
     * @return int 失效的缓存数量
     */
    public function invalidateCacheByTable(string $tableName): int
    {
        return $this->cacheManager->invalidateByTable($tableName);
    }
}

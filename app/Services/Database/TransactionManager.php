<?php

namespace App\Services\Database;

use App\Contracts\TransactionManagerInterface;
use App\Exceptions\BusinessException;
use App\Exceptions\TransactionException;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 事务管理器实现
 *
 * 提供增强的事务管理功能，支持嵌套事务、超时控制和死锁检测
 */
class TransactionManager implements TransactionManagerInterface
{
    /**
     * 活动保存点列表
     *
     * @var array<string, array<string, mixed>>
     */
    protected array $activeSavepoints = [];

    /**
     * 事务回调函数
     *
     * @var array<string, array<callable>>
     */
    protected array $callbacks = [
        'before_begin' => [],
        'after_commit' => [],
        'after_rollback' => [],
    ];

    /**
     * 事务执行历史
     *
     * @var array<array<string, mixed>>
     */
    protected array $transactionHistory = [];

    /**
     * 默认事务超时时间（秒）
     */
    protected int $defaultTimeout = 30;

    /**
     * 最大事务历史记录数
     */
    protected int $maxHistorySize = 1000;

    /**
     * 死锁检测关键词
     *
     * @var array<string>
     */
    protected array $deadlockKeywords = [
        'deadlock found',
        'lock wait timeout',
        'deadlock detected',
        'try restarting transaction',
    ];

    /**
     * 开始嵌套事务
     *
     * @param  string|null  $savepointName  保存点名称
     *
     * @throws TransactionException
     */
    public function beginNestedTransaction(?string $savepointName = null): string
    {
        try {
            if ($savepointName === null) {
                $savepointName = 'sp_'.Str::random(8).'_'.time();
            }

            // 检查保存点名称是否已存在
            if (isset($this->activeSavepoints[$savepointName])) {
                throw TransactionException::savepointError(
                    $savepointName,
                    'create',
                    ['reason' => '保存点名称已存在']
                );
            }

            // 创建保存点
            DB::statement("SAVEPOINT {$savepointName}");

            // 记录保存点信息
            $this->activeSavepoints[$savepointName] = [
                'created_at' => microtime(true),
                'transaction_level' => $this->getTransactionLevel(),
                'status' => 'active',
            ];

            Log::debug('创建嵌套事务保存点', [
                'savepoint_name' => $savepointName,
                'transaction_level' => $this->getTransactionLevel(),
            ]);

            return $savepointName;

        } catch (Exception $e) {
            Log::error('创建嵌套事务失败', [
                'savepoint_name' => $savepointName,
                'error' => $e->getMessage(),
            ]);

            throw TransactionException::nestedTransactionError(
                "创建保存点失败：{$e->getMessage()}",
                ['savepoint_name' => $savepointName]
            );
        }
    }

    /**
     * 获取当前事务级别
     */
    public function getTransactionLevel(): int
    {
        return DB::transactionLevel();
    }

    /**
     * 提交嵌套事务
     *
     * @param  string  $savepointName  保存点名称
     *
     * @throws TransactionException
     */
    public function commitNestedTransaction(string $savepointName): bool
    {
        try {
            // 检查保存点是否存在
            if (! isset($this->activeSavepoints[$savepointName])) {
                throw TransactionException::savepointError(
                    $savepointName,
                    'commit',
                    ['reason' => '保存点不存在']
                );
            }

            // 释放保存点
            DB::statement("RELEASE SAVEPOINT {$savepointName}");

            // 更新保存点状态
            $this->activeSavepoints[$savepointName]['status'] = 'committed';
            $this->activeSavepoints[$savepointName]['committed_at'] = microtime(true);

            Log::debug('提交嵌套事务保存点', [
                'savepoint_name' => $savepointName,
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('提交嵌套事务失败', [
                'savepoint_name' => $savepointName,
                'error' => $e->getMessage(),
            ]);

            throw TransactionException::commitError(
                "提交保存点失败：{$e->getMessage()}",
                ['savepoint_name' => $savepointName]
            );
        }
    }

    /**
     * 释放保存点
     *
     * @param  string  $savepointName  保存点名称
     */
    public function releaseSavepoint(string $savepointName): bool
    {
        try {
            if (isset($this->activeSavepoints[$savepointName])) {
                unset($this->activeSavepoints[$savepointName]);
            }

            return true;

        } catch (Exception $e) {
            Log::warning('释放保存点失败', [
                'savepoint_name' => $savepointName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 设置事务隔离级别
     *
     * @param  string  $level  隔离级别
     *
     * @throws TransactionException
     */
    public function setIsolationLevel(string $level): bool
    {
        $validLevels = [
            'READ_UNCOMMITTED',
            'READ_COMMITTED',
            'REPEATABLE_READ',
            'SERIALIZABLE',
        ];

        if (! in_array($level, $validLevels)) {
            throw TransactionException::isolationLevelError(
                $level,
                ['valid_levels' => $validLevels]
            );
        }

        try {
            DB::statement("SET SESSION TRANSACTION ISOLATION LEVEL {$level}");

            Log::debug('设置事务隔离级别', [
                'isolation_level' => $level,
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('设置事务隔离级别失败', [
                'isolation_level' => $level,
                'error' => $e->getMessage(),
            ]);

            throw TransactionException::isolationLevelError(
                $level,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * 检测死锁并重试
     *
     * @param  callable  $callback  要执行的回调函数
     * @param  int  $maxRetries  最大重试次数
     * @param  int  $retryDelay  重试延迟时间（毫秒）
     *
     * @throws TransactionException
     */
    public function executeWithDeadlockRetry(callable $callback, int $maxRetries = 3, int $retryDelay = 100): mixed
    {
        return $this->executeInTransaction($callback, $maxRetries + 1, $this->defaultTimeout);
    }

    /**
     * 在事务中执行回调函数
     *
     * @param  callable  $callback  要执行的回调函数
     * @param  int  $attempts  重试次数
     * @param  int  $timeout  事务超时时间（秒）
     *
     * @throws TransactionException
     */
    public function executeInTransaction(callable $callback, int $attempts = 1, int $timeout = 30): mixed
    {
        $startTime = microtime(true);
        $lastException = null;

        for ($attempt = 1; $attempt <= $attempts; $attempt++) {
            try {
                // 触发开始前回调
                $this->triggerCallbacks('before_begin');

                // 设置事务超时
                $this->setTimeout($timeout);

                $result = DB::transaction(function () use ($callback, $timeout, $startTime) {
                    // 检查超时
                    if (microtime(true) - $startTime > $timeout) {
                        throw TransactionException::timeout($timeout);
                    }

                    return $callback();
                });

                // 触发提交后回调
                $this->triggerCallbacks('after_commit');

                // 记录成功的事务
                $this->recordTransactionHistory('commit', [
                    'attempts' => $attempt,
                    'duration' => microtime(true) - $startTime,
                    'timeout' => $timeout,
                ]);

                return $result;

            } catch (Exception $e) {
                $lastException = $e;
                // 触发回滚后回调
                $this->triggerCallbacks('after_rollback');

                // 检查是否为死锁异常
                if ($this->isDeadlockException($e) && $attempt < $attempts) {
                    Log::warning('检测到死锁，准备重试', [
                        'attempt' => $attempt,
                        'max_attempts' => $attempts,
                        'error' => $e->getMessage(),
                    ]);

                    // 等待一段时间后重试
                    usleep(rand(50000, 200000)); // 50-200ms 随机延迟

                    continue;
                }

                // 记录失败的事务
                $this->recordTransactionHistory('rollback', [
                    'attempts' => $attempt,
                    'duration' => microtime(true) - $startTime,
                    'error' => $e->getMessage(),
                    'timeout' => $timeout,
                ]);

                // 如果不是死锁或已达到最大重试次数，抛出异常
                if ($e instanceof TransactionException) {
                    throw $e;
                }

                // 如果是业务异常，直接抛出，不包装成事务异常
                // 这样业务异常能正确传播到 Handler 并返回正确的 HTTP 状态码
                if ($e instanceof BusinessException) {
                    throw $e;
                }

                throw TransactionException::rollbackError(
                    $e->getMessage(),
                    ['original_exception' => get_class($e), 'attempts' => $attempt]
                );
            }
        }

        // 如果所有重试都失败了
        if ($lastException) {
            throw TransactionException::rollbackError(
                "事务执行失败，已重试 {$attempts} 次：".$lastException->getMessage(),
                ['attempts' => $attempts, 'last_error' => $lastException->getMessage()]
            );
        }

        throw TransactionException::rollbackError('未知错误');
    }

    /**
     * 触发事务回调
     *
     * @param  string  $event  事件类型
     */
    protected function triggerCallbacks(string $event): void
    {
        if (isset($this->callbacks[$event])) {
            foreach ($this->callbacks[$event] as $callback) {
                try {
                    $callback();
                } catch (Exception $e) {
                    Log::warning('事务回调执行失败', [
                        'event' => $event,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }
    }

    /**
     * 设置事务超时时间
     *
     * @param  int  $timeout  超时时间（秒）
     */
    public function setTimeout(int $timeout): bool
    {
        try {
            $this->defaultTimeout = max(1, $timeout);

            // 设置 MySQL 会话超时
            DB::statement("SET SESSION innodb_lock_wait_timeout = {$timeout}");

            return true;

        } catch (Exception $e) {
            Log::warning('设置事务超时时间失败', [
                'timeout' => $timeout,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 记录事务执行历史
     *
     * @param  string  $action  操作类型
     * @param  array<string, mixed>  $context  上下文信息
     */
    protected function recordTransactionHistory(string $action, array $context = []): void
    {
        try {
            $this->transactionHistory[] = [
                'action' => $action,
                'timestamp' => microtime(true),
                'context' => $context,
                'transaction_level' => $this->getTransactionLevel(),
                'memory_usage' => memory_get_usage(true),
            ];

            // 限制历史记录数量
            if (count($this->transactionHistory) > $this->maxHistorySize) {
                $this->transactionHistory = array_slice(
                    $this->transactionHistory,
                    -$this->maxHistorySize
                );
            }
            Log::channel('transaction')->info('Transaction action: '.$action, [
                'action' => $action,
                'timestamp' => microtime(true),
                'context' => $context,
                'transaction_level' => $this->getTransactionLevel(),
                'memory_usage' => memory_get_usage(true),
            ]);
        } catch (Exception $e) {
            Log::warning('记录事务历史失败', [
                'action' => $action,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 检查是否为死锁异常
     *
     * @param  Exception  $exception  异常对象
     */
    protected function isDeadlockException(Exception $exception): bool
    {
        $message = strtolower($exception->getMessage());

        foreach ($this->deadlockKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 批量执行事务
     *
     * @param  array<callable>  $callbacks  回调函数数组
     * @param  bool  $stopOnError  遇到错误时是否停止执行
     * @return array<int, mixed>
     *
     * @throws TransactionException
     */
    public function executeBatchTransactions(array $callbacks, bool $stopOnError = true): array
    {
        $results = [];
        $errors = [];

        try {
            return $this->executeInTransaction(function () use ($callbacks, $stopOnError, &$results, &$errors) {
                foreach ($callbacks as $index => $callback) {
                    try {
                        $results[$index] = $callback();
                    } catch (Exception $e) {
                        $errors[$index] = $e;

                        if ($stopOnError) {
                            throw TransactionException::rollbackError(
                                "批量事务执行失败，索引：{$index}，错误：{$e->getMessage()}",
                                ['failed_index' => $index, 'error' => $e->getMessage()]
                            );
                        }

                        $results[$index] = null;
                    }
                }

                return $results;
            });

        } catch (Exception $e) {
            Log::error('批量事务执行失败', [
                'callbacks_count' => count($callbacks),
                'stop_on_error' => $stopOnError,
                'errors' => array_map(fn ($err) => $err->getMessage(), $errors),
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 获取事务统计信息
     *
     * @return array<string, mixed>
     */
    public function getTransactionStatistics(): array
    {
        $totalTransactions = count($this->transactionHistory);
        $commits = array_filter($this->transactionHistory, fn ($tx) => $tx['action'] === 'commit');
        $rollbacks = array_filter($this->transactionHistory, fn ($tx) => $tx['action'] === 'rollback');

        $avgDuration = 0;
        if ($totalTransactions > 0) {
            $totalDuration = array_sum(array_column($this->transactionHistory, 'context.duration'));
            $avgDuration = $totalDuration / $totalTransactions;
        }

        return [
            'total_transactions' => $totalTransactions,
            'successful_commits' => count($commits),
            'failed_rollbacks' => count($rollbacks),
            'success_rate' => $totalTransactions > 0 ? (count($commits) / $totalTransactions) * 100 : 0,
            'average_duration' => $avgDuration,
            'active_savepoints' => count($this->activeSavepoints),
            'current_transaction_level' => $this->getTransactionLevel(),
            'in_transaction' => $this->inTransaction(),
            'isolation_level' => $this->getIsolationLevel(),
        ];
    }

    /**
     * 检查是否在事务中
     */
    public function inTransaction(): bool
    {
        return DB::transactionLevel() > 0;
    }

    /**
     * 获取当前事务隔离级别
     */
    public function getIsolationLevel(): string
    {
        try {
            $result = DB::select('SELECT @@transaction_isolation as isolation_level');

            return $result[0]->isolation_level ?? 'UNKNOWN';

        } catch (Exception $e) {
            Log::warning('获取事务隔离级别失败', [
                'error' => $e->getMessage(),
            ]);

            return 'UNKNOWN';
        }
    }

    /**
     * 获取事务超时时间
     */
    public function getTimeout(): int
    {
        return $this->defaultTimeout;
    }

    /**
     * 强制回滚所有活动事务
     */
    public function forceRollbackAll(): bool
    {
        try {
            // 回滚所有活动保存点
            foreach ($this->activeSavepoints as $savepointName => $info) {
                if ($info['status'] === 'active') {
                    try {
                        $this->rollbackNestedTransaction($savepointName);
                    } catch (Exception $e) {
                        Log::warning('强制回滚保存点失败', [
                            'savepoint_name' => $savepointName,
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            }

            // 如果在事务中，回滚主事务
            if ($this->inTransaction()) {
                DB::rollBack();
            }

            // 清理保存点记录
            $this->activeSavepoints = [];

            Log::info('强制回滚所有活动事务完成');

            return true;

        } catch (Exception $e) {
            Log::error('强制回滚所有活动事务失败', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 回滚嵌套事务
     *
     * @param  string  $savepointName  保存点名称
     *
     * @throws TransactionException
     */
    public function rollbackNestedTransaction(string $savepointName): bool
    {
        try {
            // 检查保存点是否存在
            if (! isset($this->activeSavepoints[$savepointName])) {
                throw TransactionException::savepointError(
                    $savepointName,
                    'rollback',
                    ['reason' => '保存点不存在']
                );
            }

            // 回滚到保存点
            DB::statement("ROLLBACK TO SAVEPOINT {$savepointName}");

            // 更新保存点状态
            $this->activeSavepoints[$savepointName]['status'] = 'rolled_back';
            $this->activeSavepoints[$savepointName]['rolled_back_at'] = microtime(true);

            Log::debug('回滚嵌套事务保存点', [
                'savepoint_name' => $savepointName,
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('回滚嵌套事务失败', [
                'savepoint_name' => $savepointName,
                'error' => $e->getMessage(),
            ]);

            throw TransactionException::rollbackError(
                "回滚保存点失败：{$e->getMessage()}",
                ['savepoint_name' => $savepointName]
            );
        }
    }

    /**
     * 获取活动保存点列表
     *
     * @return array<string>
     */
    public function getActiveSavepoints(): array
    {
        return array_keys(
            array_filter($this->activeSavepoints, fn ($info) => $info['status'] === 'active')
        );
    }

    /**
     * 清理过期的保存点
     *
     * @param  int  $maxAge  最大存活时间（秒）
     */
    public function cleanupExpiredSavepoints(int $maxAge = 300): int
    {
        $cleaned = 0;
        $currentTime = microtime(true);

        foreach ($this->activeSavepoints as $savepointName => $info) {
            if (($currentTime - $info['created_at']) > $maxAge) {
                try {
                    if ($info['status'] === 'active') {
                        $this->rollbackNestedTransaction($savepointName);
                    }
                    unset($this->activeSavepoints[$savepointName]);
                    $cleaned++;
                } catch (Exception $e) {
                    Log::warning('清理过期保存点失败', [
                        'savepoint_name' => $savepointName,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if ($cleaned > 0) {
            Log::info('清理过期保存点完成', [
                'cleaned_count' => $cleaned,
                'max_age' => $maxAge,
            ]);
        }

        return $cleaned;
    }

    /**
     * 注册事务回调
     *
     * @param  string  $event  事件类型
     * @param  callable  $callback  回调函数
     */
    public function registerCallback(string $event, callable $callback): bool
    {
        if (! isset($this->callbacks[$event])) {
            return false;
        }

        $this->callbacks[$event][] = $callback;

        return true;
    }

    /**
     * 移除事务回调
     *
     * @param  string  $event  事件类型
     * @param  callable|null  $callback  回调函数
     */
    public function removeCallback(string $event, ?callable $callback = null): bool
    {
        if (! isset($this->callbacks[$event])) {
            return false;
        }

        if ($callback === null) {
            $this->callbacks[$event] = [];
        } else {
            $this->callbacks[$event] = array_filter(
                $this->callbacks[$event],
                fn ($cb) => $cb !== $callback
            );
        }

        return true;
    }

    /**
     * 获取事务执行历史
     *
     * @param  int  $limit  限制数量
     * @return array<array<string, mixed>>
     */
    public function getTransactionHistory(int $limit = 100): array
    {
        return array_slice($this->transactionHistory, -$limit);
    }

    /**
     * 清除事务执行历史
     */
    public function clearTransactionHistory(): bool
    {
        $this->transactionHistory = [];

        return true;
    }
}

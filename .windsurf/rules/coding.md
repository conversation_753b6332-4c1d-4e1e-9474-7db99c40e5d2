---
trigger: always_on
description:
globs:
---

## 需求
- 不要单独创建分支开发
- 每个需求要生成对应的任务文档合理命名为xx-task.md，存储在.cursor/tasks目录下
- 完成任务后生成文档在docs目录下

## 编码原则：

    •	SOLID 原则：每个类/函数只承担单一职责，依赖于抽象
    •	分层设计：严格区分 Controller、Request、Service、Repository、Model
    •	业务抽象：业务逻辑通过服务类抽象，便于扩展与复用
    •	可测试性：结构天然支持单元测试与接口测试
    •	优雅扩展：每一层都支持替换与拓展（如策略模式、事件驱动等）
    •   完整的 Laravel API 模块（每个功能分为：模型、控制器、请求、服务、仓储）
    •	RESTful 接口响应结构（支持响应格式统一封装）
    •	高内聚、低耦合的服务抽象（领域层 Service）
    •	Repository 模式实现数据隔离，便于未来替换为缓存/远程源
    •	每个类包含 PHPDoc 与类型约束，便于 PHPStorm/IDE 补全与维护
    •	所有命名符合 PSR-12 和 Laravel 命名约定

## 测试
- 执行完每个任务要做单元测试

<?php declare(strict_types = 1);

// odsl-/Users/<USER>/work/crm-api/app
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/work/crm-api/app/ApiResponses/ApiResponse.php' => 
    array (
      0 => '53de9786bf392a21f6084f95098632a3026afc28',
      1 => 
      array (
        0 => 'app\\apiresponses\\apiresponse',
      ),
      2 => 
      array (
        0 => 'app\\apiresponses\\success',
        1 => 'app\\apiresponses\\error',
        2 => 'app\\apiresponses\\errorwitherrors',
        3 => 'app\\apiresponses\\paginated',
        4 => 'app\\apiresponses\\validationerror',
        5 => 'app\\apiresponses\\systemerror',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Contracts/QueryBuilderInterface.php' => 
    array (
      0 => 'c8fe6c28cb698d2fc917e751117400d54b998ff6',
      1 => 
      array (
        0 => 'app\\contracts\\querybuilderinterface',
      ),
      2 => 
      array (
        0 => 'app\\contracts\\buildcomplexquery',
        1 => 'app\\contracts\\adddynamicsorting',
        2 => 'app\\contracts\\optimizequery',
        3 => 'app\\contracts\\addrangecondition',
        4 => 'app\\contracts\\addsearchcondition',
        5 => 'app\\contracts\\addrelationcondition',
        6 => 'app\\contracts\\getoptimizationsuggestions',
        7 => 'app\\contracts\\analyzequeryplan',
        8 => 'app\\contracts\\buildpaginatedquery',
        9 => 'app\\contracts\\buildaggregatequery',
        10 => 'app\\contracts\\buildbatchquery',
        11 => 'app\\contracts\\validateconditions',
        12 => 'app\\contracts\\getquerystatistics',
        13 => 'app\\contracts\\setcacheforquery',
        14 => 'app\\contracts\\clearquerycache',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Contracts/DatabaseQueryCollectorInterface.php' => 
    array (
      0 => 'f278d2868477879df6803f466280896823534440',
      1 => 
      array (
        0 => 'app\\contracts\\databasequerycollectorinterface',
      ),
      2 => 
      array (
        0 => 'app\\contracts\\startcollection',
        1 => 'app\\contracts\\stopcollection',
        2 => 'app\\contracts\\getquerystatistics',
        3 => 'app\\contracts\\reset',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Contracts/TransactionManagerInterface.php' => 
    array (
      0 => 'eb88037575f521106af08758df61aec7e16ab84d',
      1 => 
      array (
        0 => 'app\\contracts\\transactionmanagerinterface',
      ),
      2 => 
      array (
        0 => 'app\\contracts\\executeintransaction',
        1 => 'app\\contracts\\beginnestedtransaction',
        2 => 'app\\contracts\\commitnestedtransaction',
        3 => 'app\\contracts\\rollbacknestedtransaction',
        4 => 'app\\contracts\\releasesavepoint',
        5 => 'app\\contracts\\intransaction',
        6 => 'app\\contracts\\gettransactionlevel',
        7 => 'app\\contracts\\setisolationlevel',
        8 => 'app\\contracts\\getisolationlevel',
        9 => 'app\\contracts\\executewithdeadlockretry',
        10 => 'app\\contracts\\executebatchtransactions',
        11 => 'app\\contracts\\gettransactionstatistics',
        12 => 'app\\contracts\\settimeout',
        13 => 'app\\contracts\\gettimeout',
        14 => 'app\\contracts\\forcerollbackall',
        15 => 'app\\contracts\\getactivesavepoints',
        16 => 'app\\contracts\\cleanupexpiredsavepoints',
        17 => 'app\\contracts\\registercallback',
        18 => 'app\\contracts\\removecallback',
        19 => 'app\\contracts\\gettransactionhistory',
        20 => 'app\\contracts\\cleartransactionhistory',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Contracts/RedisServiceInterface.php' => 
    array (
      0 => '7effd0eb3b71b0ab9cdf7eb26eee84c1722f583e',
      1 => 
      array (
        0 => 'app\\contracts\\redisserviceinterface',
      ),
      2 => 
      array (
        0 => 'app\\contracts\\set',
        1 => 'app\\contracts\\get',
        2 => 'app\\contracts\\delete',
        3 => 'app\\contracts\\exists',
        4 => 'app\\contracts\\expire',
        5 => 'app\\contracts\\ttl',
        6 => 'app\\contracts\\setmultiple',
        7 => 'app\\contracts\\getmultiple',
        8 => 'app\\contracts\\deletemultiple',
        9 => 'app\\contracts\\flush',
        10 => 'app\\contracts\\keys',
        11 => 'app\\contracts\\increment',
        12 => 'app\\contracts\\decrement',
        13 => 'app\\contracts\\getconnectioninfo',
        14 => 'app\\contracts\\ping',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/AppServiceProvider.php' => 
    array (
      0 => '914ac39c9cb073135750a886cd6593b3135c41b6',
      1 => 
      array (
        0 => 'app\\providers\\appserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/TelescopeServiceProvider.php' => 
    array (
      0 => '0c0ab4cf45314f1e1f3355ab3f99bc9e3901020e',
      1 => 
      array (
        0 => 'app\\providers\\telescopeserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\hidesensitiverequestdetails',
        2 => 'app\\providers\\gate',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/AuthServiceProvider.php' => 
    array (
      0 => '494bb0a4861384d9ba08f6b49d67830d54e54284',
      1 => 
      array (
        0 => 'app\\providers\\authserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/RouteServiceProvider.php' => 
    array (
      0 => '911cae39925a7232a369b3ee67b77a71306d7433',
      1 => 
      array (
        0 => 'app\\providers\\routeserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/RepositoryServiceProvider.php' => 
    array (
      0 => '1ff183e2e9decd70c11d0e6e3db95ab70fbddc59',
      1 => 
      array (
        0 => 'app\\providers\\repositoryserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/BusinessLogServiceProvider.php' => 
    array (
      0 => '1d48a943f41e439bb7383bd5ed9bc2451f0c7c67',
      1 => 
      array (
        0 => 'app\\providers\\businesslogserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/RedisServiceProvider.php' => 
    array (
      0 => 'bf00fca13bd993d31ef08a8218e6d4aca351ab85',
      1 => 
      array (
        0 => 'app\\providers\\redisserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\registernamedconnections',
        3 => 'app\\providers\\provides',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/BroadcastServiceProvider.php' => 
    array (
      0 => '65964eb912591e8f2a962d7f2930702f3223f9be',
      1 => 
      array (
        0 => 'app\\providers\\broadcastserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/DatabaseServiceProvider.php' => 
    array (
      0 => '0725ccaeeba78b7f3b0f7af7f5ced0a80fa98e42',
      1 => 
      array (
        0 => 'app\\providers\\databaseserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\registerquerybuilder',
        3 => 'app\\providers\\registertransactionmanager',
        4 => 'app\\providers\\registerconfig',
        5 => 'app\\providers\\registerquerylisteners',
        6 => 'app\\providers\\handlequeryexecution',
        7 => 'app\\providers\\collectquerystatistics',
        8 => 'app\\providers\\getquerytype',
        9 => 'app\\providers\\registercommands',
        10 => 'app\\providers\\provides',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/LoggingServiceProvider.php' => 
    array (
      0 => '0ab5d60c6cfa359ceac7371502194e9e3129aa84',
      1 => 
      array (
        0 => 'app\\providers\\loggingserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\provides',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Providers/EventServiceProvider.php' => 
    array (
      0 => '6b51385b69bc839413122fec5f86420770d308e2',
      1 => 
      array (
        0 => 'app\\providers\\eventserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\boot',
        1 => 'app\\providers\\registerdatabasetransactionevents',
        2 => 'app\\providers\\shoulddiscoverevents',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Resources/ContactResource.php' => 
    array (
      0 => '24cd8040491f6d9db5d74dfd6258333d6c97059f',
      1 => 
      array (
        0 => 'app\\resources\\contactresource',
      ),
      2 => 
      array (
        0 => 'app\\resources\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Resources/UserResource.php' => 
    array (
      0 => 'da372b9ac98340dda08964f265735a422b655917',
      1 => 
      array (
        0 => 'app\\resources\\userresource',
      ),
      2 => 
      array (
        0 => 'app\\resources\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Resources/Lead/LeadResource.php' => 
    array (
      0 => '4f7277f3d0a672cc685355212353914c6bfe28a5',
      1 => 
      array (
        0 => 'app\\resources\\lead\\leadresource',
      ),
      2 => 
      array (
        0 => 'app\\resources\\lead\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Resources/Lead/LeadCollection.php' => 
    array (
      0 => '7f07fff17a7e52afb39e851effb564f9a493c8e1',
      1 => 
      array (
        0 => 'app\\resources\\lead\\leadcollection',
      ),
      2 => 
      array (
        0 => 'app\\resources\\lead\\toarray',
        1 => 'app\\resources\\lead\\getpaginationdata',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Resources/BaseResource.php' => 
    array (
      0 => '1537c9bf1ff15840b0a970873e123c8efbdbe498',
      1 => 
      array (
        0 => 'app\\resources\\baseresource',
      ),
      2 => 
      array (
        0 => 'app\\resources\\formattimestamp',
        1 => 'app\\resources\\safeformattimestamp',
        2 => 'app\\resources\\formatcreatedat',
        3 => 'app\\resources\\formatupdatedat',
        4 => 'app\\resources\\formatdeletedat',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Utils/UnitFormatter.php' => 
    array (
      0 => 'd7ec8522d7698b94612c73b5bc33111ac1ae7202',
      1 => 
      array (
        0 => 'app\\utils\\unitformatter',
      ),
      2 => 
      array (
        0 => 'app\\utils\\formatmemory',
        1 => 'app\\utils\\formattime',
        2 => 'app\\utils\\formatcompoundtime',
        3 => 'app\\utils\\gettimeprecision',
        4 => 'app\\utils\\formatdatarate',
        5 => 'app\\utils\\formatpercentage',
        6 => 'app\\utils\\formatcount',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadRepositoryInterface.php' => 
    array (
      0 => 'a140a6a1a200f4a54356f3b65f8631a5c4a27888',
      1 => 
      array (
        0 => 'app\\repositories\\leadrepositoryinterface',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\getleadslist',
        1 => 'app\\repositories\\findbyid',
        2 => 'app\\repositories\\create',
        3 => 'app\\repositories\\update',
        4 => 'app\\repositories\\delete',
        5 => 'app\\repositories\\existsbycompanyname',
        6 => 'app\\repositories\\batchupdatestatus',
        7 => 'app\\repositories\\getleadsstatistics',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadContactRelationRepositoryInterface.php' => 
    array (
      0 => '9ad0bc80cf70e5cf919b92c4badc4b04f60fb126',
      1 => 
      array (
        0 => 'app\\repositories\\leadcontactrelationrepositoryinterface',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\getrelationsbyleadid',
        1 => 'app\\repositories\\getrelationsbycontactid',
        2 => 'app\\repositories\\create',
        3 => 'app\\repositories\\createbatch',
        4 => 'app\\repositories\\delete',
        5 => 'app\\repositories\\deletebyleadid',
        6 => 'app\\repositories\\deletebycontactid',
        7 => 'app\\repositories\\existsrelation',
        8 => 'app\\repositories\\findrelation',
        9 => 'app\\repositories\\deletebatch',
        10 => 'app\\repositories\\syncleadcontacts',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadUserRelationRepositoryInterface.php' => 
    array (
      0 => '7a16d69b2212069821ae789f77a013b568be56d3',
      1 => 
      array (
        0 => 'app\\repositories\\leaduserrelationrepositoryinterface',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\getrelationsbyleadid',
        1 => 'app\\repositories\\getrelationsbyuserid',
        2 => 'app\\repositories\\getprimaryownerbyleadid',
        3 => 'app\\repositories\\getcollaboratorsbyleadid',
        4 => 'app\\repositories\\create',
        5 => 'app\\repositories\\createbatch',
        6 => 'app\\repositories\\update',
        7 => 'app\\repositories\\delete',
        8 => 'app\\repositories\\deletebyleadid',
        9 => 'app\\repositories\\existsrelation',
        10 => 'app\\repositories\\setprimaryowner',
        11 => 'app\\repositories\\findrelation',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadUserRelationRepository.php' => 
    array (
      0 => 'c107ae16c109df532f86addbd883955550a49faa',
      1 => 
      array (
        0 => 'app\\repositories\\leaduserrelationrepository',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\__construct',
        1 => 'app\\repositories\\getrelationsbyleadid',
        2 => 'app\\repositories\\getbyleadid',
        3 => 'app\\repositories\\getrelationsbyuserid',
        4 => 'app\\repositories\\getprimaryownerbyleadid',
        5 => 'app\\repositories\\getcollaboratorsbyleadid',
        6 => 'app\\repositories\\createbatch',
        7 => 'app\\repositories\\create',
        8 => 'app\\repositories\\deletebyleadid',
        9 => 'app\\repositories\\delete',
        10 => 'app\\repositories\\existsrelation',
        11 => 'app\\repositories\\setprimaryowner',
        12 => 'app\\repositories\\update',
        13 => 'app\\repositories\\findrelation',
        14 => 'app\\repositories\\isowner',
        15 => 'app\\repositories\\iscollaborator',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadRepository.php' => 
    array (
      0 => 'e039bb7843be59b3cfd9b14269db5571e1188b0e',
      1 => 
      array (
        0 => 'app\\repositories\\leadrepository',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\__construct',
        1 => 'app\\repositories\\getleadslist',
        2 => 'app\\repositories\\buildqueryconditions',
        3 => 'app\\repositories\\buildsortrules',
        4 => 'app\\repositories\\findbyid',
        5 => 'app\\repositories\\create',
        6 => 'app\\repositories\\delete',
        7 => 'app\\repositories\\clearrelatedcache',
        8 => 'app\\repositories\\existsbycompanyname',
        9 => 'app\\repositories\\batchupdatestatus',
        10 => 'app\\repositories\\update',
        11 => 'app\\repositories\\getleadsstatistics',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/ContactRepository.php' => 
    array (
      0 => '2a4937e692fda08476f5e90dccfbd747fb5f260f',
      1 => 
      array (
        0 => 'app\\repositories\\contactrepository',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\__construct',
        1 => 'app\\repositories\\getcontactslist',
        2 => 'app\\repositories\\findbyid',
        3 => 'app\\repositories\\findbymobile',
        4 => 'app\\repositories\\create',
        5 => 'app\\repositories\\update',
        6 => 'app\\repositories\\delete',
        7 => 'app\\repositories\\existsbymobile',
        8 => 'app\\repositories\\createbatch',
        9 => 'app\\repositories\\getcontactsbyleadid',
        10 => 'app\\repositories\\searchcontacts',
        11 => 'app\\repositories\\buildcontactqueryconditions',
        12 => 'app\\repositories\\buildcontactsortrules',
        13 => 'app\\repositories\\getfallbackcontactslist',
        14 => 'app\\repositories\\clearrelatedcache',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/ContactRepositoryInterface.php' => 
    array (
      0 => 'efa497e52a5696c4b5145ccaf7db2d944fb7b0e2',
      1 => 
      array (
        0 => 'app\\repositories\\contactrepositoryinterface',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\getcontactslist',
        1 => 'app\\repositories\\findbyid',
        2 => 'app\\repositories\\findbymobile',
        3 => 'app\\repositories\\create',
        4 => 'app\\repositories\\update',
        5 => 'app\\repositories\\delete',
        6 => 'app\\repositories\\existsbymobile',
        7 => 'app\\repositories\\createbatch',
        8 => 'app\\repositories\\getcontactsbyleadid',
        9 => 'app\\repositories\\searchcontacts',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/OptimizedLeadRepository.php' => 
    array (
      0 => '7b72f6186a277271096d972a8de6cf8616909b76',
      1 => 
      array (
        0 => 'app\\repositories\\optimizedleadrepository',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\__construct',
        1 => 'app\\repositories\\getleadslist',
        2 => 'app\\repositories\\buildqueryconditions',
        3 => 'app\\repositories\\buildsortrules',
        4 => 'app\\repositories\\getfallbackleadslist',
        5 => 'app\\repositories\\findbyid',
        6 => 'app\\repositories\\create',
        7 => 'app\\repositories\\clearrelatedcache',
        8 => 'app\\repositories\\delete',
        9 => 'app\\repositories\\existsbycompanyname',
        10 => 'app\\repositories\\batchupdatestatus',
        11 => 'app\\repositories\\update',
        12 => 'app\\repositories\\getleadsstatistics',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Repositories/LeadContactRelationRepository.php' => 
    array (
      0 => 'd7a537eaaeec1f39f07d46ba0b5d8bfa22fb95a3',
      1 => 
      array (
        0 => 'app\\repositories\\leadcontactrelationrepository',
      ),
      2 => 
      array (
        0 => 'app\\repositories\\__construct',
        1 => 'app\\repositories\\getrelationsbyleadid',
        2 => 'app\\repositories\\getrelationsbycontactid',
        3 => 'app\\repositories\\create',
        4 => 'app\\repositories\\delete',
        5 => 'app\\repositories\\deletebyleadid',
        6 => 'app\\repositories\\deletebycontactid',
        7 => 'app\\repositories\\existsrelation',
        8 => 'app\\repositories\\findrelation',
        9 => 'app\\repositories\\deletebatch',
        10 => 'app\\repositories\\syncleadcontacts',
        11 => 'app\\repositories\\createbatch',
        12 => 'app\\repositories\\clearrelatedcache',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Models/LeadContactRelation.php' => 
    array (
      0 => '0efc5f96a95d7a83262844e4ca2b0cdef3bb6ea2',
      1 => 
      array (
        0 => 'app\\models\\leadcontactrelation',
      ),
      2 => 
      array (
        0 => 'app\\models\\lead',
        1 => 'app\\models\\contact',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Models/User.php' => 
    array (
      0 => '5eac4ff041f949f04b0492af499f384a7e4a9ca6',
      1 => 
      array (
        0 => 'app\\models\\user',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Models/LeadUserRelation.php' => 
    array (
      0 => 'c126d36104b5e2ea15a33563172fd0c49a80543b',
      1 => 
      array (
        0 => 'app\\models\\leaduserrelation',
      ),
      2 => 
      array (
        0 => 'app\\models\\lead',
        1 => 'app\\models\\user',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Models/Contact.php' => 
    array (
      0 => 'e0dc7852757d5c5e7a290a5aa7456e49f28f50d8',
      1 => 
      array (
        0 => 'app\\models\\contact',
      ),
      2 => 
      array (
        0 => 'app\\models\\leads',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Models/Lead.php' => 
    array (
      0 => '14b61fa8f7c82dd32f7d562cce2b9fc66dc64dba',
      1 => 
      array (
        0 => 'app\\models\\lead',
      ),
      2 => 
      array (
        0 => 'app\\models\\isdeletablestatus',
        1 => 'app\\models\\getnondeletablestatuses',
        2 => 'app\\models\\creator',
        3 => 'app\\models\\users',
        4 => 'app\\models\\contacts',
        5 => 'app\\models\\getstatuslabelattribute',
        6 => 'app\\models\\getregionlabelattribute',
        7 => 'app\\models\\getsourcelabelattribute',
        8 => 'app\\models\\getindustrylabelattribute',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Exceptions/TransactionException.php' => 
    array (
      0 => '123cd39608f2b238f9dc7a813eefcfd2be5cd456',
      1 => 
      array (
        0 => 'app\\exceptions\\transactionexception',
      ),
      2 => 
      array (
        0 => 'app\\exceptions\\__construct',
        1 => 'app\\exceptions\\getcontext',
        2 => 'app\\exceptions\\setcontext',
        3 => 'app\\exceptions\\timeout',
        4 => 'app\\exceptions\\deadlock',
        5 => 'app\\exceptions\\savepointerror',
        6 => 'app\\exceptions\\isolationlevelerror',
        7 => 'app\\exceptions\\nestedtransactionerror',
        8 => 'app\\exceptions\\rollbackerror',
        9 => 'app\\exceptions\\commiterror',
        10 => 'app\\exceptions\\isdeadlock',
        11 => 'app\\exceptions\\istimeout',
        12 => 'app\\exceptions\\issavepointerror',
        13 => 'app\\exceptions\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Exceptions/QueryOptimizationException.php' => 
    array (
      0 => 'e3783460770e1dd69f93ced9c48c11c8a7b8a854',
      1 => 
      array (
        0 => 'app\\exceptions\\queryoptimizationexception',
      ),
      2 => 
      array (
        0 => 'app\\exceptions\\__construct',
        1 => 'app\\exceptions\\getcontext',
        2 => 'app\\exceptions\\setcontext',
        3 => 'app\\exceptions\\invalidconditions',
        4 => 'app\\exceptions\\unsupportedoperation',
        5 => 'app\\exceptions\\optimizationfailed',
        6 => 'app\\exceptions\\fieldvalidationfailed',
        7 => 'app\\exceptions\\querytimeout',
        8 => 'app\\exceptions\\querytoocomplex',
        9 => 'app\\exceptions\\isfieldvalidationerror',
        10 => 'app\\exceptions\\isquerytimeout',
        11 => 'app\\exceptions\\isquerytoocomplex',
        12 => 'app\\exceptions\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Exceptions/BusinessException.php' => 
    array (
      0 => '0a978871aa944fdcd7eda63fca7e9aed5fb0e6a2',
      1 => 
      array (
        0 => 'app\\exceptions\\businessexception',
      ),
      2 => 
      array (
        0 => 'app\\exceptions\\__construct',
        1 => 'app\\exceptions\\fromerrorcode',
        2 => 'app\\exceptions\\haserrorcode',
        3 => 'app\\exceptions\\getallerrorcodes',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/DTOs/BaseDTO.php' => 
    array (
      0 => '7a70bf0f9129645216d44c8518682ce07c7fb3bf',
      1 => 
      array (
        0 => 'app\\dtos\\basedto',
      ),
      2 => 
      array (
        0 => 'app\\dtos\\fromarray',
        1 => 'app\\dtos\\toarray',
        2 => 'app\\dtos\\jsonserialize',
        3 => 'app\\dtos\\getnonnullproperties',
        4 => 'app\\dtos\\convertpropertyname',
        5 => 'app\\dtos\\toint',
        6 => 'app\\dtos\\tostring',
        7 => 'app\\dtos\\todatetime',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/DTOs/Lead/LeadCreateDTO.php' => 
    array (
      0 => '8b3749c594469381d8000e43839bbb24862122ba',
      1 => 
      array (
        0 => 'app\\dtos\\lead\\leadcreatedto',
      ),
      2 => 
      array (
        0 => 'app\\dtos\\lead\\__construct',
        1 => 'app\\dtos\\lead\\getdefaultstatus',
        2 => 'app\\dtos\\lead\\getdefaultstage',
        3 => 'app\\dtos\\lead\\fromrequest',
        4 => 'app\\dtos\\lead\\toarray',
        5 => 'app\\dtos\\lead\\hasfollowuptime',
        6 => 'app\\dtos\\lead\\getstatuslabel',
        7 => 'app\\dtos\\lead\\getregionlabel',
        8 => 'app\\dtos\\lead\\getsourcelabel',
        9 => 'app\\dtos\\lead\\getindustrylabel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/DTOs/Lead/LeadListDTO.php' => 
    array (
      0 => 'dfa1cf06d45a66130a88706e501706cfb5b8b3ea',
      1 => 
      array (
        0 => 'app\\dtos\\lead\\leadlistdto',
      ),
      2 => 
      array (
        0 => 'app\\dtos\\lead\\__construct',
        1 => 'app\\dtos\\lead\\toarray',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/DTOs/Lead/LeadUpdateDTO.php' => 
    array (
      0 => '444686e078c64cc99f9bd46254f78e9562609a3d',
      1 => 
      array (
        0 => 'app\\dtos\\lead\\leadupdatedto',
      ),
      2 => 
      array (
        0 => 'app\\dtos\\lead\\__construct',
        1 => 'app\\dtos\\lead\\fromrequest',
        2 => 'app\\dtos\\lead\\hasupdates',
        3 => 'app\\dtos\\lead\\toarray',
        4 => 'app\\dtos\\lead\\getupdatedfields',
        5 => 'app\\dtos\\lead\\iscompanynameupdated',
        6 => 'app\\dtos\\lead\\isstatusupdated',
        7 => 'app\\dtos\\lead\\isstageupdated',
        8 => 'app\\dtos\\lead\\getstatuslabel',
        9 => 'app\\dtos\\lead\\getstagelabel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/VerifyCsrfToken.php' => 
    array (
      0 => 'f40416cadebf79cecb5ffaa1d7bed8d360f8addd',
      1 => 
      array (
        0 => 'app\\http\\middleware\\verifycsrftoken',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/RedirectIfAuthenticated.php' => 
    array (
      0 => '22865d62613f580ef172e96566cf7b7221117c3d',
      1 => 
      array (
        0 => 'app\\http\\middleware\\redirectifauthenticated',
      ),
      2 => 
      array (
        0 => 'app\\http\\middleware\\handle',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/TrimStrings.php' => 
    array (
      0 => 'fdae9e792d68b2827446435ff17d6848726a1e81',
      1 => 
      array (
        0 => 'app\\http\\middleware\\trimstrings',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/Authenticate.php' => 
    array (
      0 => 'aa7a909ab8e4a8d509f2cecfe890fd7f19df8938',
      1 => 
      array (
        0 => 'app\\http\\middleware\\authenticate',
      ),
      2 => 
      array (
        0 => 'app\\http\\middleware\\redirectto',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/TrustProxies.php' => 
    array (
      0 => '4c915478c380e43a2889f66130fe95f9b90ca5f0',
      1 => 
      array (
        0 => 'app\\http\\middleware\\trustproxies',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/ValidateSignature.php' => 
    array (
      0 => 'ef66937ccd20e6a189479dcc98e564cc72d4c92c',
      1 => 
      array (
        0 => 'app\\http\\middleware\\validatesignature',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/ApiLoggingMiddleware.php' => 
    array (
      0 => '65085f0090a81d031d37aee05fd98305b72f6583',
      1 => 
      array (
        0 => 'app\\http\\middleware\\apiloggingmiddleware',
      ),
      2 => 
      array (
        0 => 'app\\http\\middleware\\__construct',
        1 => 'app\\http\\middleware\\handle',
        2 => 'app\\http\\middleware\\shouldlogrequest',
        3 => 'app\\http\\middleware\\isqueryloggingenabled',
        4 => 'app\\http\\middleware\\startquerycollection',
        5 => 'app\\http\\middleware\\getquerystatistics',
        6 => 'app\\http\\middleware\\resetquerycollector',
        7 => 'app\\http\\middleware\\isvalidquerystatistics',
        8 => 'app\\http\\middleware\\getdefaultquerystatistics',
        9 => 'app\\http\\middleware\\filtersensitivedata',
        10 => 'app\\http\\middleware\\recursivefilter',
        11 => 'app\\http\\middleware\\prepareinputdata',
        12 => 'app\\http\\middleware\\getuserid',
        13 => 'app\\http\\middleware\\getmemoryusage',
        14 => 'app\\http\\middleware\\sanitizeuseragent',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/GlobalErrorHandlerMiddleware.php' => 
    array (
      0 => 'ca18fe42d43a386d56ad7c1d82c0003e028f1c98',
      1 => 
      array (
        0 => 'app\\http\\middleware\\globalerrorhandlermiddleware',
      ),
      2 => 
      array (
        0 => 'app\\http\\middleware\\handle',
        1 => 'app\\http\\middleware\\seterrorcontext',
        2 => 'app\\http\\middleware\\getuserid',
        3 => 'app\\http\\middleware\\filtersensitivedata',
        4 => 'app\\http\\middleware\\recursivefilter',
        5 => 'app\\http\\middleware\\cleanuperrorcontext',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/PreventRequestsDuringMaintenance.php' => 
    array (
      0 => '3f1cc28f08b8a3a8bbd1495e187fc197110cdab1',
      1 => 
      array (
        0 => 'app\\http\\middleware\\preventrequestsduringmaintenance',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/EncryptCookies.php' => 
    array (
      0 => 'fcbbfaae9d7ac781cd043ee7ce2ee66800d075ee',
      1 => 
      array (
        0 => 'app\\http\\middleware\\encryptcookies',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Middleware/TrustHosts.php' => 
    array (
      0 => '67a8483c3620b6cd2c6354e8282fb08a98feb608',
      1 => 
      array (
        0 => 'app\\http\\middleware\\trusthosts',
      ),
      2 => 
      array (
        0 => 'app\\http\\middleware\\hosts',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Requests/Lead/LeadListRequest.php' => 
    array (
      0 => '79b67a277bea889be62901a1069cfe2d28c522cf',
      1 => 
      array (
        0 => 'app\\http\\requests\\lead\\leadlistrequest',
      ),
      2 => 
      array (
        0 => 'app\\http\\requests\\lead\\authorize',
        1 => 'app\\http\\requests\\lead\\rules',
        2 => 'app\\http\\requests\\lead\\attributes',
        3 => 'app\\http\\requests\\lead\\messages',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Requests/Lead/UpdateLeadRequest.php' => 
    array (
      0 => '590df80cc9e7719cf0ce7d1c18a7f8966e26d2b3',
      1 => 
      array (
        0 => 'app\\http\\requests\\lead\\updateleadrequest',
      ),
      2 => 
      array (
        0 => 'app\\http\\requests\\lead\\authorize',
        1 => 'app\\http\\requests\\lead\\rules',
        2 => 'app\\http\\requests\\lead\\attributes',
        3 => 'app\\http\\requests\\lead\\messages',
        4 => 'app\\http\\requests\\lead\\prepareforvalidation',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Requests/Lead/ShowLeadRequest.php' => 
    array (
      0 => 'c0cfc82d8dfbe3d8c020971a0a0c2069bcbdfc03',
      1 => 
      array (
        0 => 'app\\http\\requests\\lead\\showleadrequest',
      ),
      2 => 
      array (
        0 => 'app\\http\\requests\\lead\\authorize',
        1 => 'app\\http\\requests\\lead\\rules',
        2 => 'app\\http\\requests\\lead\\attributes',
        3 => 'app\\http\\requests\\lead\\messages',
        4 => 'app\\http\\requests\\lead\\prepareforvalidation',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Requests/Lead/BatchUpdateStatusRequest.php' => 
    array (
      0 => '5cf2a70d4cb61a6c381ab754f353315f59c255d1',
      1 => 
      array (
        0 => 'app\\http\\requests\\lead\\batchupdatestatusrequest',
      ),
      2 => 
      array (
        0 => 'app\\http\\requests\\lead\\authorize',
        1 => 'app\\http\\requests\\lead\\rules',
        2 => 'app\\http\\requests\\lead\\attributes',
        3 => 'app\\http\\requests\\lead\\messages',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Requests/Lead/CreateLeadRequest.php' => 
    array (
      0 => 'be99a019d504012ca9f30297dff8cd1345e0fd27',
      1 => 
      array (
        0 => 'app\\http\\requests\\lead\\createleadrequest',
      ),
      2 => 
      array (
        0 => 'app\\http\\requests\\lead\\authorize',
        1 => 'app\\http\\requests\\lead\\rules',
        2 => 'app\\http\\requests\\lead\\attributes',
        3 => 'app\\http\\requests\\lead\\messages',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Controllers/ErrorTestController.php' => 
    array (
      0 => 'f30a188709c33f596375d59695ac714e54ff601e',
      1 => 
      array (
        0 => 'app\\http\\controllers\\errortestcontroller',
      ),
      2 => 
      array (
        0 => 'app\\http\\controllers\\testfatalerror',
        1 => 'app\\http\\controllers\\testparseerror',
        2 => 'app\\http\\controllers\\testwarning',
        3 => 'app\\http\\controllers\\testnotice',
        4 => 'app\\http\\controllers\\testexception',
        5 => 'app\\http\\controllers\\testbusinessexception',
        6 => 'app\\http\\controllers\\testmemoryerror',
        7 => 'app\\http\\controllers\\testdivisionbyzero',
        8 => 'app\\http\\controllers\\testtypeerror',
        9 => 'app\\http\\controllers\\testarrayerror',
        10 => 'app\\http\\controllers\\teststackoverflow',
        11 => 'app\\http\\controllers\\testnormal',
        12 => 'app\\http\\controllers\\gettestlist',
        13 => 'app\\http\\controllers\\requirestring',
        14 => 'app\\http\\controllers\\infiniterecursion',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Controllers/Controller.php' => 
    array (
      0 => '1bff29ea03648d6fedf22b3619d8964630d46a69',
      1 => 
      array (
        0 => 'app\\http\\controllers\\controller',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Controllers/LogHealthController.php' => 
    array (
      0 => '57d23614bdb6533119c6a5a4732e4e5acfa9b924',
      1 => 
      array (
        0 => 'app\\http\\controllers\\loghealthcontroller',
      ),
      2 => 
      array (
        0 => 'app\\http\\controllers\\__construct',
        1 => 'app\\http\\controllers\\health',
        2 => 'app\\http\\controllers\\resilientstatus',
        3 => 'app\\http\\controllers\\healthhistory',
        4 => 'app\\http\\controllers\\triggerhealthcheck',
        5 => 'app\\http\\controllers\\resetcircuitbreaker',
        6 => 'app\\http\\controllers\\testlogging',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Controllers/LeadController.php' => 
    array (
      0 => '6d748655ad17b853aa60bdcea40ebb4382b08102',
      1 => 
      array (
        0 => 'app\\http\\controllers\\leadcontroller',
      ),
      2 => 
      array (
        0 => 'app\\http\\controllers\\__construct',
        1 => 'app\\http\\controllers\\list',
        2 => 'app\\http\\controllers\\show',
        3 => 'app\\http\\controllers\\store',
        4 => 'app\\http\\controllers\\update',
        5 => 'app\\http\\controllers\\destroy',
        6 => 'app\\http\\controllers\\batchupdatestatus',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Http/Controllers/RedisTestController.php' => 
    array (
      0 => '127171968ee7f8c9029bffa945ff6863142bcb9d',
      1 => 
      array (
        0 => 'app\\http\\controllers\\redistestcontroller',
      ),
      2 => 
      array (
        0 => 'app\\http\\controllers\\__construct',
        1 => 'app\\http\\controllers\\ping',
        2 => 'app\\http\\controllers\\basicoperations',
        3 => 'app\\http\\controllers\\batchoperations',
        4 => 'app\\http\\controllers\\counteroperations',
        5 => 'app\\http\\controllers\\patternmatching',
        6 => 'app\\http\\controllers\\comprehensivetest',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Facades/BusinessLog.php' => 
    array (
      0 => 'd6e9a8e32115fa475766b12046e802bb2a84cb15',
      1 => 
      array (
        0 => 'app\\facades\\businesslog',
      ),
      2 => 
      array (
        0 => 'app\\facades\\getfacadeaccessor',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Jobs/ProcessFailedLogJob.php' => 
    array (
      0 => 'aca4a93a1ba933a05a3e1dcb45ac07622dc875a7',
      1 => 
      array (
        0 => 'app\\jobs\\processfailedlogjob',
      ),
      2 => 
      array (
        0 => 'app\\jobs\\__construct',
        1 => 'app\\jobs\\handle',
        2 => 'app\\jobs\\failed',
        3 => 'app\\jobs\\retryafter',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Logging/BusinessLogFormatter.php' => 
    array (
      0 => 'ff6316303933aad3a0d22eaf35853ef2f8e5413f',
      1 => 
      array (
        0 => 'app\\logging\\businesslogformatter',
      ),
      2 => 
      array (
        0 => 'app\\logging\\format',
        1 => 'app\\logging\\generatetraceid',
        2 => 'app\\logging\\sanitizecontext',
        3 => 'app\\logging\\recursivesanitize',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Logging/TransactionLogFormatter.php' => 
    array (
      0 => 'd15ba39f48e1c41c481de79b0ad2a133ff1e3be2',
      1 => 
      array (
        0 => 'app\\logging\\transactionlogformatter',
      ),
      2 => 
      array (
        0 => 'app\\logging\\format',
        1 => 'app\\logging\\generatetraceid',
        2 => 'app\\logging\\sanitizecontext',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/Transaction/SimpleTransactionLogger.php' => 
    array (
      0 => '12be759a6123a0b7a5994a2813d0f718ae3bb977',
      1 => 
      array (
        0 => 'app\\services\\transaction\\simpletransactionlogger',
      ),
      2 => 
      array (
        0 => 'app\\services\\transaction\\registercallbacks',
        1 => 'app\\services\\transaction\\logtransactionbegin',
        2 => 'app\\services\\transaction\\gettraceid',
        3 => 'app\\services\\transaction\\detectoperationtype',
        4 => 'app\\services\\transaction\\detectoperationtypefromhttp',
        5 => 'app\\services\\transaction\\logtransactioncommit',
        6 => 'app\\services\\transaction\\logtransactionrollback',
        7 => 'app\\services\\transaction\\logleaddelete',
        8 => 'app\\services\\transaction\\logbusinesstransaction',
        9 => 'app\\services\\transaction\\settransactioncontext',
        10 => 'app\\services\\transaction\\extractresultcontext',
        11 => 'app\\services\\transaction\\classifyerrorseverity',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/Database/EnhancedQueryBuilder.php' => 
    array (
      0 => 'ce660cff74ad6033627eae4ad535cb56bb797b01',
      1 => 
      array (
        0 => 'app\\services\\database\\enhancedquerybuilder',
      ),
      2 => 
      array (
        0 => 'app\\services\\database\\__construct',
        1 => 'app\\services\\database\\buildcomplexquery',
        2 => 'app\\services\\database\\adddynamicsorting',
        3 => 'app\\services\\database\\optimizequery',
        4 => 'app\\services\\database\\addrangecondition',
        5 => 'app\\services\\database\\addsearchcondition',
        6 => 'app\\services\\database\\addrelationcondition',
        7 => 'app\\services\\database\\getoptimizationsuggestions',
        8 => 'app\\services\\database\\analyzequeryplan',
        9 => 'app\\services\\database\\buildpaginatedquery',
        10 => 'app\\services\\database\\applybasicconditions',
        11 => 'app\\services\\database\\applyrangeconditions',
        12 => 'app\\services\\database\\applysearchconditions',
        13 => 'app\\services\\database\\applyrelationconditions',
        14 => 'app\\services\\database\\applysinglesortrule',
        15 => 'app\\services\\database\\applycomplexsortrule',
        16 => 'app\\services\\database\\calculatequerycomplexity',
        17 => 'app\\services\\database\\validatequerycomplexity',
        18 => 'app\\services\\database\\estimatequerycost',
        19 => 'app\\services\\database\\addindexhints',
        20 => 'app\\services\\database\\optimizeselectfields',
        21 => 'app\\services\\database\\optimizejoinorder',
        22 => 'app\\services\\database\\buildaggregatequery',
        23 => 'app\\services\\database\\buildbatchquery',
        24 => 'app\\services\\database\\validateconditions',
        25 => 'app\\services\\database\\getquerystatistics',
        26 => 'app\\services\\database\\setcacheforquery',
        27 => 'app\\services\\database\\clearquerycache',
        28 => 'app\\services\\database\\generatecachekey',
        29 => 'app\\services\\database\\detectquerytype',
        30 => 'app\\services\\database\\counttables',
        31 => 'app\\services\\database\\buildqueryfromcache',
        32 => 'app\\services\\database\\executeandcachequery',
        33 => 'app\\services\\database\\getcachestatistics',
        34 => 'app\\services\\database\\invalidatecachebytable',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/Database/CacheManager.php' => 
    array (
      0 => 'e6d25c0ac4d6743bcdf872123cc526301056c543',
      1 => 
      array (
        0 => 'app\\services\\database\\cachemanager',
      ),
      2 => 
      array (
        0 => 'app\\services\\database\\generatecachekey',
        1 => 'app\\services\\database\\get',
        2 => 'app\\services\\database\\set',
        3 => 'app\\services\\database\\forget',
        4 => 'app\\services\\database\\forgetbypattern',
        5 => 'app\\services\\database\\invalidatebytable',
        6 => 'app\\services\\database\\getstatistics',
        7 => 'app\\services\\database\\cleanupexpiredmemorycache',
        8 => 'app\\services\\database\\normalizesql',
        9 => 'app\\services\\database\\generatebindingssignature',
        10 => 'app\\services\\database\\getquerycontext',
        11 => 'app\\services\\database\\getfrommemorycache',
        12 => 'app\\services\\database\\settomemorycache',
        13 => 'app\\services\\database\\forgetfrommemorycache',
        14 => 'app\\services\\database\\evictleastrecentlyused',
        15 => 'app\\services\\database\\calculateadaptivettl',
        16 => 'app\\services\\database\\findkeysbypattern',
        17 => 'app\\services\\database\\recordcachehit',
        18 => 'app\\services\\database\\recordcachemiss',
        19 => 'app\\services\\database\\recordcacheset',
        20 => 'app\\services\\database\\reportmetrics',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/Database/TransactionManager.php' => 
    array (
      0 => 'c7cefff020e7c817d32e2d9e708f99812b71bcda',
      1 => 
      array (
        0 => 'app\\services\\database\\transactionmanager',
      ),
      2 => 
      array (
        0 => 'app\\services\\database\\beginnestedtransaction',
        1 => 'app\\services\\database\\gettransactionlevel',
        2 => 'app\\services\\database\\commitnestedtransaction',
        3 => 'app\\services\\database\\releasesavepoint',
        4 => 'app\\services\\database\\setisolationlevel',
        5 => 'app\\services\\database\\executewithdeadlockretry',
        6 => 'app\\services\\database\\executeintransaction',
        7 => 'app\\services\\database\\triggercallbacks',
        8 => 'app\\services\\database\\settimeout',
        9 => 'app\\services\\database\\recordtransactionhistory',
        10 => 'app\\services\\database\\isdeadlockexception',
        11 => 'app\\services\\database\\executebatchtransactions',
        12 => 'app\\services\\database\\gettransactionstatistics',
        13 => 'app\\services\\database\\intransaction',
        14 => 'app\\services\\database\\getisolationlevel',
        15 => 'app\\services\\database\\gettimeout',
        16 => 'app\\services\\database\\forcerollbackall',
        17 => 'app\\services\\database\\rollbacknestedtransaction',
        18 => 'app\\services\\database\\getactivesavepoints',
        19 => 'app\\services\\database\\cleanupexpiredsavepoints',
        20 => 'app\\services\\database\\registercallback',
        21 => 'app\\services\\database\\removecallback',
        22 => 'app\\services\\database\\gettransactionhistory',
        23 => 'app\\services\\database\\cleartransactionhistory',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/LeadUserRelationService.php' => 
    array (
      0 => 'bc28c22b1fa9a267faa52bfb219b8f422943b90c',
      1 => 
      array (
        0 => 'app\\services\\leaduserrelationservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\getleaduserrelations',
        2 => 'app\\services\\getleadprimaryowner',
        3 => 'app\\services\\getleadcollaborators',
        4 => 'app\\services\\setleadprimaryowner',
        5 => 'app\\services\\addleadcollaborator',
        6 => 'app\\services\\removeleaduserrelation',
        7 => 'app\\services\\setleadcollaborators',
        8 => 'app\\services\\getuserleadrelations',
        9 => 'app\\services\\transferleadownership',
        10 => 'app\\services\\deleteallleaduserrelations',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/BusinessLogService.php' => 
    array (
      0 => '55cfe7b4c14de361d6e91c24ab12f40b11b78c56',
      1 => 
      array (
        0 => 'app\\services\\businesslogservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\settraceid',
        1 => 'app\\services\\gettraceid',
        2 => 'app\\services\\log',
        3 => 'app\\services\\debug',
        4 => 'app\\services\\info',
        5 => 'app\\services\\warning',
        6 => 'app\\services\\error',
        7 => 'app\\services\\critical',
        8 => 'app\\services\\validateloglevel',
        9 => 'app\\services\\logexception',
        10 => 'app\\services\\sanitizedata',
        11 => 'app\\services\\extractrequestinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/LeadService.php' => 
    array (
      0 => '81683553b4872472bb87906541c65d6bd8024bf3',
      1 => 
      array (
        0 => 'app\\services\\leadservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\getleadslist',
        2 => 'app\\services\\getleadbyid',
        3 => 'app\\services\\createlead',
        4 => 'app\\services\\updatelead',
        5 => 'app\\services\\deletelead',
        6 => 'app\\services\\clearleadcaches',
        7 => 'app\\services\\batchupdatestatus',
        8 => 'app\\services\\getleadsstatistics',
        9 => 'app\\services\\batchoperateleads',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/LogHealthMonitorService.php' => 
    array (
      0 => '05e9074e80463976425d968e24730b101fa3748b',
      1 => 
      array (
        0 => 'app\\services\\loghealthmonitorservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\performhealthcheck',
        2 => 'app\\services\\checkdiskspace',
        3 => 'app\\services\\checklogfilesize',
        4 => 'app\\services\\checklogpermissions',
        5 => 'app\\services\\checklogperformance',
        6 => 'app\\services\\checkcircuitbreakerstatus',
        7 => 'app\\services\\sendalertsifneeded',
        8 => 'app\\services\\sendalert',
        9 => 'app\\services\\gethealthhistory',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/LeadContactRelationService.php' => 
    array (
      0 => 'b49591879eb4f1f0ba90ff0949f42d94174cfbdc',
      1 => 
      array (
        0 => 'app\\services\\leadcontactrelationservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\getleadcontactrelations',
        2 => 'app\\services\\getcontactleadrelations',
        3 => 'app\\services\\createleadcontactrelation',
        4 => 'app\\services\\createbatchleadcontactrelations',
        5 => 'app\\services\\deleteleadcontactrelation',
        6 => 'app\\services\\syncleadcontacts',
        7 => 'app\\services\\deleteallleadcontactrelations',
        8 => 'app\\services\\deleteallcontactleadrelations',
        9 => 'app\\services\\deletebatchrelations',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/LeadCacheService.php' => 
    array (
      0 => '96c741172ef56e6bd1ba1b5aeb252076b83160da',
      1 => 
      array (
        0 => 'app\\services\\leadcacheservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\cacheleaddetail',
        2 => 'app\\services\\getleaddetailkey',
        3 => 'app\\services\\getcachedleaddetail',
        4 => 'app\\services\\clearleaddetailcache',
        5 => 'app\\services\\cacheleadlist',
        6 => 'app\\services\\getleadlistkey',
        7 => 'app\\services\\getcachedleadlist',
        8 => 'app\\services\\clearleadlistcache',
        9 => 'app\\services\\batchcacheleaddetails',
        10 => 'app\\services\\batchgetcachedleaddetails',
        11 => 'app\\services\\clearuserleadcaches',
        12 => 'app\\services\\incrementleadviewcount',
        13 => 'app\\services\\getleadviewcountkey',
        14 => 'app\\services\\getleadviewcount',
        15 => 'app\\services\\setleadhotscore',
        16 => 'app\\services\\getleadhotscorekey',
        17 => 'app\\services\\getleadhotscore',
        18 => 'app\\services\\generatelistcachekey',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/DatabaseQueryCollector.php' => 
    array (
      0 => '2306a587476afa4fdf2804af64b443e0edbbe3a7',
      1 => 
      array (
        0 => 'app\\services\\databasequerycollector',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\startcollection',
        2 => 'app\\services\\recordquery',
        3 => 'app\\services\\stopcollection',
        4 => 'app\\services\\getquerystatistics',
        5 => 'app\\services\\formatsqlwithbindings',
        6 => 'app\\services\\reset',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/RedisService.php' => 
    array (
      0 => 'f182580fd967077cb67b56b00e275fc5d3b26511',
      1 => 
      array (
        0 => 'app\\services\\redisservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\set',
        2 => 'app\\services\\get',
        3 => 'app\\services\\delete',
        4 => 'app\\services\\exists',
        5 => 'app\\services\\expire',
        6 => 'app\\services\\ttl',
        7 => 'app\\services\\setmultiple',
        8 => 'app\\services\\getmultiple',
        9 => 'app\\services\\deletemultiple',
        10 => 'app\\services\\flush',
        11 => 'app\\services\\keys',
        12 => 'app\\services\\increment',
        13 => 'app\\services\\decrement',
        14 => 'app\\services\\getconnectioninfo',
        15 => 'app\\services\\ping',
        16 => 'app\\services\\getconnection',
        17 => 'app\\services\\serialize',
        18 => 'app\\services\\unserialize',
        19 => 'app\\services\\logerror',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/GlobalErrorHandlerService.php' => 
    array (
      0 => '5649a8d59c234a79cace05235f31cafeca02e450',
      1 => 
      array (
        0 => 'app\\services\\globalerrorhandlerservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\register',
        1 => 'app\\services\\seterrorcontext',
        2 => 'app\\services\\handleerror',
        3 => 'app\\services\\handlefatalerror',
        4 => 'app\\services\\handleexception',
        5 => 'app\\services\\logerror',
        6 => 'app\\services\\logfatalerror',
        7 => 'app\\services\\sendfatalerrorresponse',
        8 => 'app\\services\\senderrorresponse',
        9 => 'app\\services\\isfatalerror',
        10 => 'app\\services\\getseverityname',
        11 => 'app\\services\\isapirequest',
        12 => 'app\\services\\geterrorcontext',
        13 => 'app\\services\\clearerrorcontext',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/ResilientLoggerService.php' => 
    array (
      0 => '730ea13f7c94a5f3b132fc3d7b83fdb30adcc1f3',
      1 => 
      array (
        0 => 'app\\services\\resilientloggerservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\logapirequest',
        2 => 'app\\services\\logsyncwithtimeout',
        3 => 'app\\services\\logasyncwithtimeout',
        4 => 'app\\services\\executewithtimeout',
        5 => 'app\\services\\handleloggingfailure',
        6 => 'app\\services\\tryfallbackstrategies',
        7 => 'app\\services\\backuptocache',
        8 => 'app\\services\\queueforlaterprocessing',
        9 => 'app\\services\\backuptofile',
        10 => 'app\\services\\logfailuretosystem',
        11 => 'app\\services\\iscircuitbreakeropen',
        12 => 'app\\services\\handlecircuitbreakeropen',
        13 => 'app\\services\\recordsuccess',
        14 => 'app\\services\\recordfailure',
        15 => 'app\\services\\gethealthstatus',
        16 => 'app\\services\\getbackuplogscount',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Services/ContactService.php' => 
    array (
      0 => '03ea41a898edbab3f9ec6d47c6e19400d66bc6fa',
      1 => 
      array (
        0 => 'app\\services\\contactservice',
      ),
      2 => 
      array (
        0 => 'app\\services\\__construct',
        1 => 'app\\services\\getcontactslist',
        2 => 'app\\services\\getcontactbyid',
        3 => 'app\\services\\createcontact',
        4 => 'app\\services\\updatecontact',
        5 => 'app\\services\\deletecontact',
        6 => 'app\\services\\createbatchcontacts',
        7 => 'app\\services\\getcontactsbyleadid',
        8 => 'app\\services\\searchcontacts',
        9 => 'app\\services\\getorcreatecontactbymobile',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/LogHealthCheckCommand.php' => 
    array (
      0 => 'e39d84d0a89ad4455529bf35af8496a567999c91',
      1 => 
      array (
        0 => 'app\\console\\commands\\loghealthcheckcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\__construct',
        1 => 'app\\console\\commands\\handle',
        2 => 'app\\console\\commands\\displayhealthstatus',
        3 => 'app\\console\\commands\\sendalerts',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/TestResourceBugFixCommand.php' => 
    array (
      0 => '91ecdf8e18ccbb73c64f5715d22ea28ae75d73b9',
      1 => 
      array (
        0 => 'app\\console\\commands\\testresourcebugfixcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\handle',
        1 => 'app\\console\\commands\\testnormaltimestamps',
        2 => 'app\\console\\commands\\testnulltimestamps',
        3 => 'app\\console\\commands\\testinvalidtimestamps',
        4 => 'app\\console\\commands\\testsafeformat',
        5 => 'app\\console\\commands\\toarray',
        6 => 'app\\console\\commands\\displayresults',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/TestRedisServiceCommand.php' => 
    array (
      0 => 'cd9e8febf7b59080ab2fa2d2ad57b1bfc7043922',
      1 => 
      array (
        0 => 'app\\console\\commands\\testredisservicecommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\__construct',
        1 => 'app\\console\\commands\\handle',
        2 => 'app\\console\\commands\\testconnection',
        3 => 'app\\console\\commands\\testbasicoperations',
        4 => 'app\\console\\commands\\testbatchoperations',
        5 => 'app\\console\\commands\\testcounteroperations',
        6 => 'app\\console\\commands\\testpatternmatching',
        7 => 'app\\console\\commands\\displaysummary',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/TestLeadModuleRefactoringCommand.php' => 
    array (
      0 => '86901bce67eb4e079fb4206b31a594fffedfb3d9',
      1 => 
      array (
        0 => 'app\\console\\commands\\testleadmodulerefactoringcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\__construct',
        1 => 'app\\console\\commands\\handle',
        2 => 'app\\console\\commands\\testrepositorylayer',
        3 => 'app\\console\\commands\\testleadquery',
        4 => 'app\\console\\commands\\testleadcrud',
        5 => 'app\\console\\commands\\testbatchoperations',
        6 => 'app\\console\\commands\\testcontactoperations',
        7 => 'app\\console\\commands\\testcachefunctionality',
        8 => 'app\\console\\commands\\testservicelayer',
        9 => 'app\\console\\commands\\testbusinesslogic',
        10 => 'app\\console\\commands\\testtransactionmanagement',
        11 => 'app\\console\\commands\\testexceptionhandling',
        12 => 'app\\console\\commands\\testperformance',
        13 => 'app\\console\\commands\\testqueryperformance',
        14 => 'app\\console\\commands\\testcacheperformance',
        15 => 'app\\console\\commands\\testbatchperformance',
        16 => 'app\\console\\commands\\displaysummary',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/CleanBusinessLogsCommand.php' => 
    array (
      0 => 'befd078a8e4840e29ca5e88752068e8b9fe5f97e',
      1 => 
      array (
        0 => 'app\\console\\commands\\cleanbusinesslogscommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\handle',
        1 => 'app\\console\\commands\\getbusinesslogfiles',
        2 => 'app\\console\\commands\\extractdatefromfilename',
        3 => 'app\\console\\commands\\formatbytes',
        4 => 'app\\console\\commands\\showlogstatistics',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/ValidateErrorResponseFormatCommand.php' => 
    array (
      0 => 'c1589e0ffcc57bbee612b42dbe68514394cda580',
      1 => 
      array (
        0 => 'app\\console\\commands\\validateerrorresponseformatcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\handle',
        1 => 'app\\console\\commands\\validateerrorresponse',
        2 => 'app\\console\\commands\\showvalidationsummary',
        3 => 'app\\console\\commands\\showformatfeatures',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/TestDatabaseOptimizationCommand.php' => 
    array (
      0 => '4eb33a0ef97f6db53e45dba5fa29a7bf53290113',
      1 => 
      array (
        0 => 'app\\console\\commands\\testdatabaseoptimizationcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\__construct',
        1 => 'app\\console\\commands\\handle',
        2 => 'app\\console\\commands\\testquerybuilder',
        3 => 'app\\console\\commands\\testtransactionmanager',
        4 => 'app\\console\\commands\\testcomplexquerybuilding',
        5 => 'app\\console\\commands\\testdynamicsorting',
        6 => 'app\\console\\commands\\testrangeconditions',
        7 => 'app\\console\\commands\\testsearchconditions',
        8 => 'app\\console\\commands\\testqueryoptimization',
        9 => 'app\\console\\commands\\testaggregatequery',
        10 => 'app\\console\\commands\\testbasictransaction',
        11 => 'app\\console\\commands\\testnestedtransaction',
        12 => 'app\\console\\commands\\testtransactionrollback',
        13 => 'app\\console\\commands\\testdeadlockretry',
        14 => 'app\\console\\commands\\testbatchtransactions',
        15 => 'app\\console\\commands\\testtransactionstatistics',
        16 => 'app\\console\\commands\\displaysummary',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/work/crm-api/app/Console/Commands/TestErrorHandlingCommand.php' => 
    array (
      0 => 'c2286ad5c9ae3794bf408f69541d4aa8c7d6abe3',
      1 => 
      array (
        0 => 'app\\console\\commands\\testerrorhandlingcommand',
      ),
      2 => 
      array (
        0 => 'app\\console\\commands\\handle',
        1 => 'app\\console\\commands\\testallerrortypes',
        2 => 'app\\console\\commands\\testspecificerror',
        3 => 'app\\console\\commands\\performtest',
        4 => 'app\\console\\commands\\getexpectedresult',
        5 => 'app\\console\\commands\\showavailabletests',
        6 => 'app\\console\\commands\\showtestsummary',
      ),
      3 => 
      array (
      ),
    ),
  ),
));
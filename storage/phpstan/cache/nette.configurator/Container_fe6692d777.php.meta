a:6:{i:0;i:1;i:1;a:23:{s:83:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.neon";i:**********;s:93:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/parametersSchema.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level8.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level7.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level6.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level5.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level4.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level3.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level2.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level1.neon";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/conf/config.level0.neon";i:**********;s:36:"/Users/<USER>/work/crm-api/phpstan.neon";i:1754110838;s:67:"/Users/<USER>/work/crm-api/./vendor/nunomaduro/larastan/extension.neon";i:**********;s:45:"/Users/<USER>/work/crm-api/phpstan-baseline.neon";i:1754107514;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/di/src/DI/Extensions/ServicesExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/di/src/DI/Extensions/ParametersExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/bootstrap/src/Bootstrap/Extensions/PhpExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/di/src/DI/Extensions/ExtensionsExtension.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/RulesExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/ConditionalTagsExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/ParametersSchemaExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/ValidateIgnoredErrorsExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/ValidateExcludePathsExtension.php";i:**********;}i:2;a:855:{s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Debug/DebugScopeRule.php";i:**********;s:85:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Rule.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Debug/DumpPhpDocTypeRule.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Debug/DumpTypeRule.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Debug/FileAssertRule.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiInstantiationRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiClassExtendsRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiClassImplementsRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiInterfaceExtendsRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiMethodCallRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiStaticCallRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiTraitUseRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/GetTemplateTypeRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/PhpStanNamespaceIn3rdPartyPackageRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/DuplicateKeysInLiteralArraysRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/EmptyArrayItemRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/OffsetAccessWithoutDimForReadingRule.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Cast/UnsetCastRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/AllowedSubTypesRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ClassAttributesRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ClassConstantAttributesRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ClassConstantRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/DuplicateDeclarationRule.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/EnumSanityRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassesInClassImplementsRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassesInEnumImplementsRule.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassesInInterfaceExtendsRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassInTraitUseRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/InstantiationRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/InstantiationCallableRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/InvalidPromotedPropertiesRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/LocalTypeAliasesRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/LocalTypeTraitAliasesRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/NewStaticRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/NonClassAttributeClassRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ReadOnlyClassRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/TraitAttributeClassRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/ClassAsClassConstantRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/DynamicClassConstantFetchRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/FinalConstantRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/NativeTypedClassConstantRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/EnumCases/EnumCaseAttributesRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/NoncapturingCatchRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/ThrowExpressionRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ArrowFunctionAttributesRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ArrowFunctionReturnNullsafeByRefRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ClosureAttributesRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/DefineParametersRule.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ExistingClassesInArrowFunctionTypehintsRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/CallToFunctionParametersRule.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ExistingClassesInClosureTypehintsRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ExistingClassesInTypehintsRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/FunctionAttributesRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/InnerFunctionRule.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/InvalidLexicalVariablesInClosureUseRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ParamAttributesRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/PrintfParametersRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/RedefinedParametersRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ReturnNullsafeByRefRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Ignore/IgnoreParseErrorRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/VariadicParametersDeclarationRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Keywords/ContinueBreakInLoopRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Keywords/DeclareStrictTypesRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/AbstractMethodInNonAbstractClassRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/AbstractPrivateMethodRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallMethodsRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallStaticMethodsRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/ConstructorReturnTypeRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/ExistingClassesInTypehintsRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/FinalPrivateMethodRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodCallableRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodVisibilityInInterfaceRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MissingMethodImplementationRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodAttributesRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/StaticMethodCallableRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Names/UsedNamesRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Operators/InvalidAssignVarRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/AccessPropertiesInAssignRule.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/AccessStaticPropertiesInAssignRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/InvalidCallablePropertyTypeRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/MissingReadOnlyPropertyAssignRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/PropertiesInInterfaceRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/PropertyAttributesRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyPropertyRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Traits/ConflictingTraitConstantsRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Traits/ConstantsInTraitsRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Types/InvalidTypesInUnionRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/UnsetRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Whitespace/FileWhitespaceRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/UnusedConstructorParametersRule.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/ConstantRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/UnusedClosureUsesRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/EmptyRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/IssetRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/NullCoalesceRule.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Cast/EchoRule.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Cast/InvalidCastRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Cast/InvalidPartOfEncapsedStringRule.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Cast/PrintRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/AccessPrivateConstantThroughStaticRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/UsageOfVoidMatchExpressionRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/ValueAssignedToClassConstantRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/IncompatibleDefaultParameterTypeRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/ClassAncestorsRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/ClassTemplateTypeRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/EnumAncestorsRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/EnumTemplateTypeRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/FunctionTemplateTypeRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/FunctionSignatureVarianceRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/InterfaceAncestorsRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/InterfaceTemplateTypeRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/MethodTemplateTypeRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/MethodTagTemplateTypeRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/MethodSignatureVarianceRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/TraitTemplateTypeRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/UsedTraitsRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallPrivateMethodThroughStaticRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/IncompatibleDefaultParameterTypeRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Operators/InvalidComparisonOperationRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/FunctionConditionalReturnTypeRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/MethodConditionalReturnTypeRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/FunctionAssertRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/MethodAssertRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/IncompatibleSelfOutTypeRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/IncompatibleClassConstantPhpDocTypeRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/IncompatiblePhpDocTypeRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/IncompatiblePropertyPhpDocTypeRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/InvalidThrowsPhpDocValueRule.php";i:**********;s:135:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/IncompatibleParamImmediatelyInvokedCallableRule.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/AccessPrivatePropertyThroughStaticRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/RequireImplementsRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/RequireExtendsRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/RequireImplementsDefinitionClassRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/RequireExtendsDefinitionClassRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/RequireExtendsDefinitionTraitRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/ArrayDestructuringRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/IterableInForeachRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/OffsetAccessAssignmentRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/OffsetAccessAssignOpRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/OffsetAccessValueAssignmentRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/UnpackIterableInArrayRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/ThrowExprTypeRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ArrowFunctionReturnTypeRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ClosureReturnTypeRule.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ReturnTypeRule.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generators/YieldTypeRule.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/ReturnTypeRule.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/DefaultValueTypesAssignedToPropertiesRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyPropertyAssignRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyPropertyAssignRefRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/TypesAssignedToPropertiesRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/ThrowTypeRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/VariableCloningRule.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/DeadForeachRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/UnreachableStatementRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/UnusedPrivateConstantRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/UnusedPrivateMethodRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/OverwrittenExitPointByFinallyRule.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/CallToFunctionStatementWithoutSideEffectsRule.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallToMethodStatementWithoutSideEffectsRule.php";i:**********;s:138:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallToStaticMethodStatementWithoutSideEffectsRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/NullsafeMethodCallRule.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideArrowFunctionReturnTypehintRule.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideClosureReturnTypehintRule.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideFunctionReturnTypehintRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DateTimeInstantiationRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/MissingClassConstantTypehintRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/MissingFunctionReturnTypehintRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MissingMethodReturnTypehintRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/MissingPropertyTypehintRule.php";i:**********;s:113:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/UselessConstructs/NoUselessWithFunctionCallsRule.php";i:**********;s:114:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/UselessConstructs/NoUselessValueFunctionCallsRule.php";i:**********;s:109:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/DeferrableServiceProviderMissingProvidesRule.php";i:**********;s:109:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/ConsoleCommand/UndefinedArgumentOrOptionRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/BuilderFactory.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/LexerFactory.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/NodeVisitor/NameResolver.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/NodeVisitorAbstract.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/NodeVisitor.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/AnonymousClassVisitor.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ArrayFilterArgVisitor.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ArrayFindArgVisitor.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ArrayMapArgVisitor.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ArrayWalkArgVisitor.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ClosureArgVisitor.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ClosureBindToVarVisitor.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ClosureBindArgVisitor.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/CurlSetOptArgVisitor.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/TypeTraverserInstanceofVisitor.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ArrowFunctionArgVisitor.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/MagicConstantParamDefaultVisitor.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/NewAssignedToPropertyVisitor.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ParentStmtTypesVisitor.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/TryCatchTypeVisitor.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/LastConditionVisitor.php";i:**********;s:142:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/NodeVisitor/NodeConnectingVisitor.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Node/Printer/ExprPrinter.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Node/Printer/Printer.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/PrettyPrinter/Standard.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/PrettyPrinterAbstract.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Broker/AnonymousClassNameHelper.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Php/PhpVersionFactoryFactory.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/phpstan/phpdoc-parser/src/Lexer/Lexer.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/phpstan/phpdoc-parser/src/Parser/TypeParser.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/phpstan/phpdoc-parser/src/Parser/PhpDocParser.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/phpstan/phpdoc-parser/src/Printer/Printer.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/ConstExprParserFactory.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/PhpDocInheritanceResolver.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/PhpDocNodeResolver.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/PhpDocStringResolver.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/ConstExprNodeResolver.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/TypeNodeResolver.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/TypeStringResolver.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/StubValidator.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/CountableStubFilesExtension.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/StubFilesExtension.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/SocketSelectStubFilesExtension.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/DefaultStubFilesProvider.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/StubFilesProvider.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/JsonValidateStubFilesExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/ReflectionEnumStubFilesExtension.php";i:**********;s:92:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/Analyser.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/AnalyserResultFinalizer.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/FileAnalyser.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/LocalIgnoresProcessor.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/RuleErrorTransformer.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/Ignore/IgnoredErrorHelper.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/Ignore/IgnoreLexer.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/LazyInternalScopeFactory.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/InternalScopeFactory.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ScopeFactory.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/NodeScopeResolver.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ConstantResolverFactory.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ResultCache/ResultCacheClearer.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/RicherScopeGetTypeHelper.php";i:**********;s:86:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Cache/Cache.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Collectors/RegistryFactory.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/AnalyseApplication.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/AnalyserRunner.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/FixerApplication.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Dependency/DependencyResolver.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Dependency/ExportedNodeFetcher.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Dependency/ExportedNodeResolver.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Dependency/ExportedNodeVisitor.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Nette/NetteContainer.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Container.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/DerivativeContainerFactory.php";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileHelper.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileExcluderFactory.php";i:**********;s:90:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileFinder.php";i:**********;s:91:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileMonitor.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/DeclarePositionVisitor.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/ImmediatelyInvokedClosureVisitor.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parallel/ParallelAnalyser.php";i:**********;s:93:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parallel/Scheduler.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Diagnose/DiagnoseExtension.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/FunctionCallStatementFinder.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Process/CpuCoreCounter.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/InitializerExprTypeResolver.php";i:**********;s:140:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Annotations/AnnotationsMethodsClassReflectionExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/MethodsClassReflectionExtension.php";i:**********;s:143:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Annotations/AnnotationsPropertiesClassReflectionExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/PropertiesClassReflectionExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/CachingVisitor.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/FileNodesFetcher.php";i:**********;s:163:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/ComposerJsonAndInstalledJsonSourceLocatorMaker.php";i:**********;s:155:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedDirectorySourceLocatorFactory.php";i:**********;s:158:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedDirectorySourceLocatorRepository.php";i:**********;s:159:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedSingleFileSourceLocatorRepository.php";i:**********;s:148:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/RequireExtension/RequireExtendsMethodsClassReflectionExtension.php";i:**********;s:151:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/RequireExtension/RequireExtendsPropertiesClassReflectionExtension.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Mixin/MixinMethodsClassReflectionExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Mixin/MixinPropertiesClassReflectionExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/PhpClassReflectionExtension.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/Soap/SoapClientMethodsClassReflectionExtension.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/EnumAllowedSubTypesClassReflectionExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/AllowedSubTypesClassReflectionExtension.php";i:**********;s:135:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/UniversalObjectCratesClassReflectionExtension.php";i:**********;s:146:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/PHPStan/NativeReflectionEnumReturnDynamicReturnTypeExtension.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicMethodReturnTypeExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/NativeFunctionReflectionProvider.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/SignatureMapParser.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/FunctionSignatureMapProvider.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/SignatureMapProvider.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/Php8SignatureMapProvider.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/SignatureMap/SignatureMapProviderFactory.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiRuleHelper.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/AttributesCheck.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/NonexistentOffsetInArrayDimFetchCheck.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/ClassNameCheck.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/ClassCaseSensitivityCheck.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/ClassForbiddenNameCheck.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/LocalTypeAliasesCheck.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MethodTagCheck.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MixinCheck.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/PropertyTagCheck.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ConstantConditionRuleHelper.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ImpossibleCheckTypeHelper.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/DefaultExceptionTypeResolver.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/ExceptionTypeResolver.php";i:**********;s:135:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/MissingCheckedExceptionInFunctionThrowsRule.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/MissingCheckedExceptionInMethodThrowsRule.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/MissingCheckedExceptionInThrowsCheck.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/TooWideFunctionThrowTypeRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/TooWideMethodThrowTypeRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/TooWideThrowTypeCheck.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/FunctionCallParametersCheck.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/FunctionDefinitionCheck.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/FunctionReturnTypeCheck.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/ParameterCastableToStringCheck.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/CrossCheckInterfacesHelper.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/GenericAncestorsCheck.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/GenericObjectTypeCheck.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/MethodTagTemplateTypeCheck.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/TemplateTypeCheck.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/VarianceCheck.php";i:**********;s:91:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/IssetCheck.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodCallCheck.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/StaticMethodCallCheck.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodSignatureRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MethodParameterComparisonHelper.php";i:**********;s:101:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/MissingTypehintCheck.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/NullsafeCheck.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/LazyAlwaysUsedClassConstantsExtensionProvider.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/AlwaysUsedClassConstantsExtensionProvider.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/LazyAlwaysUsedMethodExtensionProvider.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/AlwaysUsedMethodExtensionProvider.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/ConditionalReturnTypeRuleHelper.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/AssertRuleHelper.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/UnresolvableTypeHelper.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/GenericCallableRuleHelper.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/VarTagTypeRuleHelper.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Playground/NeverRuleHelper.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/LazyReadWritePropertiesExtensionProvider.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadWritePropertiesExtensionProvider.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/PropertyDescriptor.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/PropertyReflectionFinder.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Pure/FunctionPurityCheck.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/RuleLevelHelper.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/UnusedFunctionParametersCheck.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideParameterOutTypeCheck.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/FileTypeMapper.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/BitwiseFlagHelper.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/AbsFunctionDynamicReturnTypeExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicFunctionReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArgumentBasedFunctionReturnTypeExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayChangeKeyCaseFunctionReturnTypeExtension.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayIntersectKeyFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayChunkFunctionReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayColumnFunctionReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayCombineFunctionReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayCurrentDynamicReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFillFunctionReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFillKeysFunctionReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFilterFunctionReturnTypeHelper.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFilterFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFlipFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFindFunctionReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayFindKeyFunctionReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayKeyDynamicReturnTypeExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayKeyExistsFunctionTypeSpecifyingExtension.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/FunctionTypeSpecifyingExtension.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/TypeSpecifierAwareExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayKeyFirstDynamicReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayKeyLastDynamicReturnTypeExtension.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayKeysFunctionDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayMapFunctionReturnTypeExtension.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayMergeFunctionDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayNextDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayPopFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayRandFunctionReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayReduceFunctionReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayReplaceFunctionReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayReverseFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayShiftFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArraySliceFunctionReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArraySpliceFunctionReturnTypeExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArraySearchFunctionDynamicReturnTypeExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArraySearchFunctionTypeSpecifyingExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayValuesFunctionDynamicReturnTypeExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArraySumFunctionDynamicReturnTypeExtension.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/AssertThrowTypeExtension.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicFunctionThrowTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/BackedEnumFromMethodDynamicReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicStaticMethodReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/Base64DecodeDynamicFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/BcMathStringOrNullReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ClosureBindDynamicReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ClosureBindToDynamicReturnTypeExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ClosureFromCallableDynamicReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/CompactFunctionReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ConstantFunctionReturnTypeExtension.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ConstantHelper.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/CountFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/CountFunctionTypeSpecifyingExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/CurlGetinfoFunctionDynamicReturnTypeExtension.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateFunctionReturnTypeHelper.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateFormatFunctionReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateFormatMethodReturnTypeExtension.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateFunctionReturnTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateIntervalConstructorThrowTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicStaticMethodThrowTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateIntervalDynamicReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeCreateDynamicReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeDynamicReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeModifyReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeConstructorThrowTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeModifyMethodThrowTypeExtension.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/DynamicMethodThrowTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeSubMethodThrowTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DateTimeZoneConstructorThrowTypeExtension.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DsMapDynamicReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DsMapDynamicMethodThrowTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DioStatDynamicFunctionReturnTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ExplodeFunctionDynamicReturnTypeExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/FilterFunctionReturnTypeHelper.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/FilterInputDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/FilterVarDynamicReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/FilterVarArrayDynamicReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GetCalledClassDynamicReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GetClassDynamicReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GetDebugTypeFunctionReturnTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GetDefinedVarsFunctionReturnTypeExtension.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GetParentClassDynamicFunctionReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GettypeFunctionReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/GettimeofdayDynamicFunctionReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/HashFunctionsReturnTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/HighlightStringDynamicReturnTypeExtension.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IntdivThrowTypeExtension.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IniGetReturnTypeExtension.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/JsonThrowTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/OpenSslEncryptParameterOutTypeExtension.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/FunctionParameterOutTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ParseStrParameterOutTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PregMatchTypeSpecifyingExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PregMatchParameterOutTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PregReplaceCallbackClosureTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/FunctionParameterClosureTypeExtension.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/RegexArrayShapeMatcher.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Regex/RegexGroupParser.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Regex/RegexExpressionHelper.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionClassConstructorThrowTypeExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionFunctionConstructorThrowTypeExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionMethodConstructorThrowTypeExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionPropertyConstructorThrowTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrContainingTypeSpecifyingExtension.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SimpleXMLElementClassPropertyReflectionExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SimpleXMLElementConstructorThrowTypeExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StatDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MethodExistsTypeSpecifyingExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PropertyExistsTypeSpecifyingExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MinMaxFunctionReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/NumberFormatFunctionDynamicReturnTypeExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PathinfoFunctionDynamicReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PregFilterFunctionReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PregSplitDynamicReturnTypeExtension.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionClassIsSubclassOfTypeSpecifyingExtension.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/MethodTypeSpecifyingExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReplaceFunctionsDynamicReturnTypeExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ArrayPointerFunctionsDynamicReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/LtrimFunctionReturnTypeExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MbFunctionsReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MbFunctionsReturnTypeExtensionTrait.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MbConvertEncodingFunctionReturnTypeExtension.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MbSubstituteCharacterDynamicReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MbStrlenFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/MicrotimeFunctionReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/HrtimeFunctionReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ImplodeFunctionReturnTypeExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/NonEmptyStringFunctionsReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SetTypeFunctionTypeSpecifyingExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrCaseFunctionsReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrlenFunctionReturnTypeExtension.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrIncrementDecrementFunctionReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrPadFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrRepeatFunctionReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrrevFunctionReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SubstrDynamicReturnTypeExtension.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ThrowableReturnTypeExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ParseUrlFunctionDynamicReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/TriggerErrorDynamicReturnTypeExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/TrimFunctionDynamicReturnTypeExtension.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/VersionCompareFunctionDynamicReturnTypeExtension.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/PowFunctionReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/RoundFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrtotimeFunctionReturnTypeExtension.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/RandomIntFunctionReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/RangeFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/AssertFunctionTypeSpecifyingExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ClassExistsFunctionTypeSpecifyingExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ClassImplementsFunctionReturnTypeExtension.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DefineConstantTypeSpecifyingExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DefinedConstantTypeSpecifyingExtension.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/FunctionExistsFunctionTypeSpecifyingExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/InArrayFunctionTypeSpecifyingExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsArrayFunctionTypeSpecifyingExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsCallableFunctionTypeSpecifyingExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsIterableFunctionTypeSpecifyingExtension.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsSubclassOfFunctionTypeSpecifyingExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IteratorToArrayFunctionReturnTypeExtension.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsAFunctionTypeSpecifyingExtension.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/IsAFunctionTypeSpecifyingHelper.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/CtypeDigitFunctionTypeSpecifyingExtension.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/JsonThrowOnErrorDynamicReturnTypeExtension.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/TypeSpecifyingFunctionsDynamicReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SimpleXMLElementAsXMLMethodReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SimpleXMLElementXpathMethodReturnTypeExtension.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrSplitFunctionReturnTypeExtension.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrTokFunctionReturnTypeExtension.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SprintfFunctionDynamicReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/SscanfFunctionDynamicReturnTypeExtension.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrvalFamilyFunctionReturnTypeExtension.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/StrWordCountFunctionDynamicReturnTypeExtension.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/XMLReaderOpenReturnTypeExtension.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/ReflectionGetAttributesMethodReturnTypeExtension.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Php/DatePeriodConstructorReturnTypeExtension.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/ClosureTypeFactory.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/Constant/OversizedArrayBuilder.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/PrintfHelper.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/TypeSpecifierFactory.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/ParentDirectoryRelativePathHelper.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/RelativePathHelper.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Broker/BrokerFactory.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Cache/FileCacheStorage.php";i:**********;s:93:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Cache/CacheStorage.php";i:**********;s:92:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/RichParser.php";i:**********;s:88:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/Parser.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/CleaningParser.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/SimpleParser.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/CachedParser.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/PhpParserDecorator.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Parser.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Parser/Php7.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/ParserAbstract.php";i:**********;s:93:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/LazyRegistry.php";i:**********;s:89:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Registry.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/StubPhpDocProvider.php";i:**********;s:130:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ReflectionProvider/ReflectionProviderFactory.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ReflectionProvider.php";i:**********;s:139:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/Reflector/DefaultReflector.php";i:**********;s:132:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/Reflector/Reflector.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/Reflector/MemoizingReflector.php";i:**********;s:137:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/Reflector/ClassReflector.php";i:**********;s:140:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/Reflector/FunctionReflector.php";i:**********;s:140:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/Reflector/ConstantReflector.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/BetterReflectionProvider.php";i:**********;s:139:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/BetterReflectionSourceLocatorFactory.php";i:**********;s:150:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceStubber/PhpStormStubsSourceStubberFactory.php";i:**********;s:167:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/SourceLocator/SourceStubber/PhpStormStubsSourceStubber.php";i:**********;s:154:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/SourceLocator/SourceStubber/SourceStubber.php";i:**********;s:147:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceStubber/ReflectionSourceStubberFactory.php";i:**********;s:164:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/SourceLocator/SourceStubber/ReflectionSourceStubber.php";i:**********;s:99:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Parser/PathRoutingParser.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Diagnose/PHPStanDiagnoseExtension.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/CiDetectedErrorFormatter.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/ErrorFormatter.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/RawErrorFormatter.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/TableErrorFormatter.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/CheckstyleErrorFormatter.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/JsonErrorFormatter.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/JunitErrorFormatter.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/GitlabErrorFormatter.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/GithubErrorFormatter.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Command/ErrorFormatter/TeamcityErrorFormatter.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiClassConstFetchRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiInstanceofRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/ApiInstanceofTypeRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/NodeConnectingVisitorAttributesRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/RuntimeReflectionFunctionRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Api/RuntimeReflectionInstantiationRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassInClassExtendsRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ExistingClassInInstanceOfRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/LocalTypeTraitUseAliasesRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/CaughtExceptionExistenceRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/CallToNonExistentFunctionRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/OverridingConstantRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/OverridingMethodRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/ConsistentConstructorRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Missing/MissingReturnRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Namespaces/ExistingNamesInGroupUseRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Namespaces/ExistingNamesInUseRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Operators/InvalidIncDecOperationRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/AccessPropertiesRule.php";i:**********;s:118:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/AccessStaticPropertiesRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ExistingClassesInPropertiesRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/FunctionCallableRule.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/MissingReadOnlyByPhpDocPropertyAssignRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/OverridingPropertyRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyByPhpDocPropertyRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/UninitializedPropertyRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/WritingToReadOnlyPropertiesRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadingWriteOnlyPropertiesRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/CompactVariablesRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/DefinedVariableRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Regexp/RegularExpressionPatternRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ConstructorsHelper.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MissingMagicSerializationMethodsRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Constants/MagicConstantContextRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/UselessFunctionReturnValueRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/PrintfArrayParametersRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Regexp/RegularExpressionQuotingRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Keywords/RequireFileExistsRule.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MixinRule.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MixinTraitRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MixinTraitUseRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MethodTagRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MethodTagTraitRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/MethodTagTraitUseRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/PropertyTagRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/PropertyTagTraitRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/PropertyTagTraitUseRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/RequireExtendsCheck.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/RequireImplementsDefinitionTraitRule.php";i:**********;s:140:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/IncompatibleArrowFunctionDefaultParameterTypeRule.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/IncompatibleClosureDefaultParameterTypeRule.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/CallCallablesRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/MethodTagTemplateTypeTraitRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/IllegalConstructorMethodCallRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/IllegalConstructorStaticCallRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/InvalidPhpDocTagValueRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/InvalidPhpDocVarTagTypeRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/InvalidPHPStanDocTagRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/VarTagChangedExpressionTypeRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/PhpDoc/WrongVariableNameInVarTagRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generics/PropertyVarianceRule.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Pure/PureFunctionRule.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Pure/PureMethodRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Operators/InvalidBinaryOperationRule.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Operators/InvalidUnaryOperationRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/InvalidKeyInArrayDimFetchRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/InvalidKeyInArrayItemRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/NonexistentOffsetInArrayDimFetchRule.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/ThrowsVoidFunctionWithExplicitThrowPointRule.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/ThrowsVoidMethodWithExplicitThrowPointRule.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generators/YieldFromTypeRule.php";i:**********;s:112:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Generators/YieldInGeneratorRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Arrays/ArrayUnpackingRule.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyByPhpDocPropertyAssignRefRule.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/ReadOnlyByPhpDocPropertyAssignRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/ParameterOutAssignedTypeRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Variables/ParameterOutExecutionEndTypeRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Classes/ImpossibleInstanceOfRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/BooleanAndConstantConditionRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/BooleanOrConstantConditionRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/BooleanNotConstantConditionRule.php";i:**********;s:98:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/NoopRule.php";i:**********;s:139:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/CallToConstructorStatementWithoutImpurePointsRule.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/ConstructorWithoutImpurePointsCollector.php";i:**********;s:95:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Collectors/Collector.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/PossiblyPureNewCollector.php";i:**********;s:136:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/CallToFunctionStatementWithoutImpurePointsRule.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/FunctionWithoutImpurePointsCollector.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/PossiblyPureFuncCallCollector.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/CallToMethodStatementWithoutImpurePointsRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/MethodWithoutImpurePointsCollector.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/PossiblyPureMethodCallCollector.php";i:**********;s:140:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/CallToStaticMethodStatementWithoutImpurePointsRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/PossiblyPureStaticCallCollector.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/UnusedPrivatePropertyRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/DoWhileLoopConstantConditionRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ElseIfConstantConditionRule.php";i:**********;s:115:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/IfConstantConditionRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ImpossibleCheckTypeFunctionCallRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ImpossibleCheckTypeMethodCallRule.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ImpossibleCheckTypeStaticMethodCallRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/LogicalXorConstantConditionRule.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/DeadCode/BetterNoopRule.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/MatchExpressionRule.php";i:**********;s:138:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/NumberComparisonOperatorsConstantConditionRule.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/StrictComparisonOfDifferentTypesRule.php";i:**********;s:119:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/ConstantLooseComparisonRule.php";i:**********;s:128:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/TernaryOperatorConstantConditionRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/UnreachableIfBranchesRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/UnreachableTernaryElseBranchRule.php";i:**********;s:125:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/WhileLoopAlwaysFalseConditionRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Comparison/WhileLoopAlwaysTrueConditionRule.php";i:**********;s:137:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/CallToConstructorStatementWithoutSideEffectsRule.php";i:**********;s:129:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideMethodReturnTypehintRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Properties/NullsafePropertyFetchRule.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Traits/TraitDeclarationCollector.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Traits/TraitUseCollector.php";i:**********;s:108:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Traits/NotAnalysedTraitRule.php";i:**********;s:122:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Exceptions/CatchWithUnthrownExceptionRule.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideFunctionParameterOutTypeRule.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWideMethodParameterOutTypeRule.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/TooWideTypehints/TooWidePropertyTypeRule.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/RandomIntParametersRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ArrayFilterRule.php";i:**********;s:106:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ArrayValuesRule.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/CallUserFuncRule.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ImplodeFunctionRule.php";i:**********;s:120:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ParameterCastableToStringRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/ImplodeParameterCastableToStringRule.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/SortParameterCastableToStringRule.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Functions/MissingFunctionParameterTypehintRule.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MissingMethodParameterTypehintRule.php";i:**********;s:117:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Rules/Methods/MissingMethodSelfOutTypeRule.php";i:**********;s:97:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/RelationForwardsCallsExtension.php";i:**********;s:94:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/ModelForwardsCallsExtension.php";i:**********;s:104:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/EloquentBuilderForwardsCallsExtension.php";i:**********;s:95:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/HigherOrderTapProxyExtension.php";i:**********;s:102:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/HigherOrderCollectionProxyExtension.php";i:**********;s:105:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/StorageMethodsClassReflectionExtension.php";i:**********;s:76:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/Extension.php";i:**********;s:110:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/ModelFactoryMethodsClassReflectionExtension.php";i:**********;s:114:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/RedirectResponseMethodsClassReflectionExtension.php";i:**********;s:103:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/MacroMethodsClassReflectionExtension.php";i:**********;s:80:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Concerns/HasContainer.php";i:**********;s:106:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/ViewWithMethodsClassReflectionExtension.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/ModelAccessorExtension.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/ModelPropertyExtension.php";i:**********;s:113:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/HigherOrderCollectionProxyPropertyExtension.php";i:**********;s:105:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/RelationDynamicMethodReturnTypeExtension.php";i:**********;s:111:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/ModelRelationsDynamicMethodReturnTypeExtension.php";i:**********;s:99:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/HigherOrderTapProxyExtension.php";i:**********;s:123:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ContainerArrayAccessDynamicMethodReturnTypeExtension.php";i:**********;s:93:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/ModelRelationsExtension.php";i:**********;s:112:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ModelOnlyDynamicMethodReturnTypeExtension.php";i:**********;s:121:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ModelFactoryDynamicStaticMethodReturnTypeExtension.php";i:**********;s:114:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ModelDynamicStaticMethodReturnTypeExtension.php";i:**********;s:104:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AppMakeDynamicReturnTypeExtension.php";i:**********;s:84:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AuthExtension.php";i:**********;s:82:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Concerns/LoadsAuthModel.php";i:**********;s:114:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/GuardDynamicStaticMethodReturnTypeExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AuthManagerExtension.php";i:**********;s:84:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/DateExtension.php";i:**********;s:85:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/GuardExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/RequestFileExtension.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/RequestRouteExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/RequestUserExtension.php";i:**********;s:95:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/EloquentBuilderExtension.php";i:**********;s:98:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/RelationCollectionExtension.php";i:**********;s:89:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ModelFindExtension.php";i:**********;s:96:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/BuilderModelFindExtension.php";i:**********;s:88:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/TestCaseExtension.php";i:**********;s:83:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Support/CollectionHelper.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/AuthExtension.php";i:**********;s:95:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/CollectExtension.php";i:**********;s:99:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/NowAndTodayExtension.php";i:**********;s:96:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/ResponseExtension.php";i:**********;s:97:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/ValidatorExtension.php";i:**********;s:119:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/CollectionFilterRejectDynamicReturnTypeExtension.php";i:**********;s:119:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/CollectionWhereNotNullDynamicReturnTypeExtension.php";i:**********;s:116:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/NewModelQueryDynamicMethodReturnTypeExtension.php";i:**********;s:110:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/FactoryDynamicMethodReturnTypeExtension.php";i:**********;s:103:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/AbortIfFunctionTypeSpecifyingExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/AppExtension.php";i:**********;s:93:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/ValueExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/StrExtension.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/TapExtension.php";i:**********;s:116:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/StorageDynamicStaticMethodReturnTypeExtension.php";i:**********;s:115:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/GenericEloquentCollectionTypeNodeResolverExtension.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/TypeNodeResolverExtension.php";i:**********;s:100:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/ViewStringTypeNodeResolverExtension.php";i:**********;s:88:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/OctaneCompatibilityRule.php";i:**********;s:94:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/NoEnvCallsOutsideOfConfigRule.php";i:**********;s:80:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/NoModelMakeRule.php";i:**********;s:96:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/NoUnnecessaryCollectionCallRule.php";i:**********;s:81:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/ModelAppendsRule.php";i:**********;s:112:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/GenericEloquentBuilderTypeNodeResolverExtension.php";i:**********;s:104:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AppEnvironmentReturnTypeExtension.php";i:**********;s:110:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AppFacadeEnvironmentReturnTypeExtension.php";i:**********;s:117:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/ModelProperty/ModelPropertyTypeNodeResolverExtension.php";i:**********;s:85:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Types/RelationParserHelper.php";i:**********;s:85:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/MigrationHelper.php";i:**********;s:93:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/SquashedMigrationHelper.php";i:**********;s:85:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/ModelCastHelper.php";i:**********;s:89:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/ModelPropertyHelper.php";i:**********;s:80:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/ModelRuleHelper.php";i:**********;s:80:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Methods/BuilderHelper.php";i:**********;s:86:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/RelationExistenceRule.php";i:**********;s:125:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/CheckDispatchArgumentTypesCompatibleWithClassConstructorRule.php";i:**********;s:108:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Properties/Schema/MySqlDataTypeToPhpTypeConverter.php";i:**********;s:85:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/LarastanStubFilesExtension.php";i:**********;s:80:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Rules/UnusedViewsRule.php";i:**********;s:95:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedViewFunctionCollector.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedEmailViewCollector.php";i:**********;s:91:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedViewMakeCollector.php";i:**********;s:97:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedViewFacadeMakeCollector.php";i:**********;s:98:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedRouteFacadeViewCollector.php";i:**********;s:100:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Collectors/UsedViewInAnotherViewCollector.php";i:**********;s:81:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Support/ViewFileHelper.php";i:**********;s:112:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ApplicationMakeDynamicReturnTypeExtension.php";i:**********;s:110:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ContainerMakeDynamicReturnTypeExtension.php";i:**********;s:120:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ConsoleCommand/ArgumentDynamicReturnTypeExtension.php";i:**********;s:123:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ConsoleCommand/HasArgumentDynamicReturnTypeExtension.php";i:**********;s:118:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ConsoleCommand/OptionDynamicReturnTypeExtension.php";i:**********;s:121:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ConsoleCommand/HasOptionDynamicReturnTypeExtension.php";i:**********;s:103:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/TranslatorGetReturnTypeExtension.php";i:**********;s:97:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/LangGetReturnTypeExtension.php";i:**********;s:101:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/TransHelperReturnTypeExtension.php";i:**********;s:112:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/DoubleUnderscoreHelperReturnTypeExtension.php";i:**********;s:84:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/AppMakeHelper.php";i:**********;s:94:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Internal/ConsoleApplicationResolver.php";i:**********;s:92:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Internal/ConsoleApplicationHelper.php";i:**********;s:99:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Support/HigherOrderCollectionProxyHelper.php";i:**********;s:127:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/ConfigFunctionDynamicFunctionReturnTypeExtension.php";i:**********;s:112:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/ConfigGetDynamicMethodReturnTypeExtension.php";i:**********;s:79:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/Support/ConfigParser.php";i:**********;s:124:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/Helpers/EnvFunctionDynamicFunctionReturnTypeExtension.php";i:**********;s:118:"/Users/<USER>/work/crm-api/vendor/nunomaduro/larastan/src/ReturnTypes/FormRequestSafeDynamicMethodReturnTypeExtension.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/di/src/DI/Container.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nette/utils/src/SmartObject.php";i:**********;s:96:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Php/PhpVersionFactory.php";i:**********;s:89:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Php/PhpVersion.php";i:**********;s:126:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/phpstan/phpdoc-parser/src/Parser/ConstExprParser.php";i:**********;s:127:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/LazyTypeNodeResolverExtensionRegistryProvider.php";i:**********;s:123:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/PhpDoc/TypeNodeResolverExtensionRegistryProvider.php";i:**********;s:100:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ConstantResolver.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ResultCache/ResultCacheManager.php";i:**********;s:121:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/ResultCache/ResultCacheManagerFactory.php";i:**********;s:94:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Collectors/Registry.php";i:**********;s:113:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/MemoizingContainer.php";i:**********;s:150:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Reflection/LazyClassReflectionExtensionRegistryProvider.php";i:**********;s:146:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Reflection/ClassReflectionExtensionRegistryProvider.php";i:**********;s:146:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyDynamicReturnTypeExtensionRegistryProvider.php";i:**********;s:142:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/DynamicReturnTypeExtensionRegistryProvider.php";i:**********;s:137:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyParameterOutTypeExtensionProvider.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/ParameterOutTypeExtensionProvider.php";i:**********;s:151:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyExpressionTypeResolverExtensionRegistryProvider.php";i:**********;s:147:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/ExpressionTypeResolverExtensionRegistryProvider.php";i:**********;s:151:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyOperatorTypeSpecifyingExtensionRegistryProvider.php";i:**********;s:147:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/OperatorTypeSpecifyingExtensionRegistryProvider.php";i:**********;s:137:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyDynamicThrowTypeExtensionProvider.php";i:**********;s:133:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/DynamicThrowTypeExtensionProvider.php";i:**********;s:141:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/LazyParameterClosureTypeExtensionProvider.php";i:**********;s:137:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/DependencyInjection/Type/ParameterClosureTypeExtensionProvider.php";i:**********;s:92:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileExcluder.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FileExcluderRawFactory.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/PhpFunctionReflection.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/FunctionReflection.php";i:**********;s:111:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/FunctionReflectionFactory.php";i:**********;s:146:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedPsrAutoloaderLocator.php";i:**********;s:145:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/ondrejmirtes/better-reflection/src/SourceLocator/Type/SourceLocator.php";i:**********;s:153:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedPsrAutoloaderLocatorFactory.php";i:**********;s:149:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedSingleFileSourceLocator.php";i:**********;s:156:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/SourceLocator/OptimizedSingleFileSourceLocatorFactory.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/PhpMethodReflection.php";i:**********;s:110:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ExtendedMethodReflection.php";i:**********;s:107:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ClassMemberReflection.php";i:**********;s:102:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/MethodReflection.php";i:**********;s:116:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/Php/PhpMethodReflectionFactory.php";i:**********;s:135:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ReflectionProvider/LazyReflectionProviderProvider.php";i:**********;s:131:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/ReflectionProvider/ReflectionProviderProvider.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/UsefulTypeAliasResolver.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/TypeAliasResolver.php";i:**********;s:109:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/LazyTypeAliasResolverProvider.php";i:**********;s:105:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Type/TypeAliasResolverProvider.php";i:**********;s:97:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Analyser/TypeSpecifier.php";i:**********;s:103:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/FuzzyRelativePathHelper.php";i:**********;s:104:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/File/SimpleRelativePathHelper.php";i:**********;s:88:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Broker/Broker.php";i:**********;s:114:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Lexer.php";i:**********;s:134:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/src/Reflection/BetterReflection/BetterReflectionProviderFactory.php";i:**********;s:124:"phar:///Users/<USER>/work/crm-api/vendor/phpstan/phpstan/phpstan.phar/vendor/nikic/php-parser/lib/PhpParser/Lexer/Emulative.php";i:**********;}i:3;a:841:{i:0;s:34:"PHPStan\Rules\Debug\DebugScopeRule";i:1;s:18:"PHPStan\Rules\Rule";i:2;s:38:"PHPStan\Rules\Debug\DumpPhpDocTypeRule";i:3;s:32:"PHPStan\Rules\Debug\DumpTypeRule";i:4;s:34:"PHPStan\Rules\Debug\FileAssertRule";i:5;s:38:"PHPStan\Rules\Api\ApiInstantiationRule";i:6;s:37:"PHPStan\Rules\Api\ApiClassExtendsRule";i:7;s:40:"PHPStan\Rules\Api\ApiClassImplementsRule";i:8;s:41:"PHPStan\Rules\Api\ApiInterfaceExtendsRule";i:9;s:35:"PHPStan\Rules\Api\ApiMethodCallRule";i:10;s:35:"PHPStan\Rules\Api\ApiStaticCallRule";i:11;s:33:"PHPStan\Rules\Api\ApiTraitUseRule";i:12;s:37:"PHPStan\Rules\Api\GetTemplateTypeRule";i:13;s:55:"PHPStan\Rules\Api\PhpStanNamespaceIn3rdPartyPackageRule";i:14;s:53:"PHPStan\Rules\Arrays\DuplicateKeysInLiteralArraysRule";i:15;s:39:"PHPStan\Rules\Arrays\EmptyArrayItemRule";i:16;s:57:"PHPStan\Rules\Arrays\OffsetAccessWithoutDimForReadingRule";i:17;s:32:"PHPStan\Rules\Cast\UnsetCastRule";i:18;s:41:"PHPStan\Rules\Classes\AllowedSubTypesRule";i:19;s:41:"PHPStan\Rules\Classes\ClassAttributesRule";i:20;s:49:"PHPStan\Rules\Classes\ClassConstantAttributesRule";i:21;s:39:"PHPStan\Rules\Classes\ClassConstantRule";i:22;s:46:"PHPStan\Rules\Classes\DuplicateDeclarationRule";i:23;s:36:"PHPStan\Rules\Classes\EnumSanityRule";i:24;s:58:"PHPStan\Rules\Classes\ExistingClassesInClassImplementsRule";i:25;s:57:"PHPStan\Rules\Classes\ExistingClassesInEnumImplementsRule";i:26;s:59:"PHPStan\Rules\Classes\ExistingClassesInInterfaceExtendsRule";i:27;s:49:"PHPStan\Rules\Classes\ExistingClassInTraitUseRule";i:28;s:39:"PHPStan\Rules\Classes\InstantiationRule";i:29;s:47:"PHPStan\Rules\Classes\InstantiationCallableRule";i:30;s:51:"PHPStan\Rules\Classes\InvalidPromotedPropertiesRule";i:31;s:42:"PHPStan\Rules\Classes\LocalTypeAliasesRule";i:32;s:47:"PHPStan\Rules\Classes\LocalTypeTraitAliasesRule";i:33;s:35:"PHPStan\Rules\Classes\NewStaticRule";i:34;s:48:"PHPStan\Rules\Classes\NonClassAttributeClassRule";i:35;s:39:"PHPStan\Rules\Classes\ReadOnlyClassRule";i:36;s:45:"PHPStan\Rules\Classes\TraitAttributeClassRule";i:37;s:48:"PHPStan\Rules\Constants\ClassAsClassConstantRule";i:38;s:53:"PHPStan\Rules\Constants\DynamicClassConstantFetchRule";i:39;s:41:"PHPStan\Rules\Constants\FinalConstantRule";i:40;s:52:"PHPStan\Rules\Constants\NativeTypedClassConstantRule";i:41;s:46:"PHPStan\Rules\EnumCases\EnumCaseAttributesRule";i:42;s:46:"PHPStan\Rules\Exceptions\NoncapturingCatchRule";i:43;s:44:"PHPStan\Rules\Exceptions\ThrowExpressionRule";i:44;s:51:"PHPStan\Rules\Functions\ArrowFunctionAttributesRule";i:45;s:60:"PHPStan\Rules\Functions\ArrowFunctionReturnNullsafeByRefRule";i:46;s:45:"PHPStan\Rules\Functions\ClosureAttributesRule";i:47;s:44:"PHPStan\Rules\Functions\DefineParametersRule";i:48;s:67:"PHPStan\Rules\Functions\ExistingClassesInArrowFunctionTypehintsRule";i:49;s:52:"PHPStan\Rules\Functions\CallToFunctionParametersRule";i:50;s:61:"PHPStan\Rules\Functions\ExistingClassesInClosureTypehintsRule";i:51;s:54:"PHPStan\Rules\Functions\ExistingClassesInTypehintsRule";i:52;s:46:"PHPStan\Rules\Functions\FunctionAttributesRule";i:53;s:41:"PHPStan\Rules\Functions\InnerFunctionRule";i:54;s:63:"PHPStan\Rules\Functions\InvalidLexicalVariablesInClosureUseRule";i:55;s:43:"PHPStan\Rules\Functions\ParamAttributesRule";i:56;s:44:"PHPStan\Rules\Functions\PrintfParametersRule";i:57;s:47:"PHPStan\Rules\Functions\RedefinedParametersRule";i:58;s:47:"PHPStan\Rules\Functions\ReturnNullsafeByRefRule";i:59;s:41:"PHPStan\Rules\Ignore\IgnoreParseErrorRule";i:60;s:57:"PHPStan\Rules\Functions\VariadicParametersDeclarationRule";i:61;s:46:"PHPStan\Rules\Keywords\ContinueBreakInLoopRule";i:62;s:45:"PHPStan\Rules\Keywords\DeclareStrictTypesRule";i:63;s:58:"PHPStan\Rules\Methods\AbstractMethodInNonAbstractClassRule";i:64;s:47:"PHPStan\Rules\Methods\AbstractPrivateMethodRule";i:65;s:37:"PHPStan\Rules\Methods\CallMethodsRule";i:66;s:43:"PHPStan\Rules\Methods\CallStaticMethodsRule";i:67;s:47:"PHPStan\Rules\Methods\ConstructorReturnTypeRule";i:68;s:52:"PHPStan\Rules\Methods\ExistingClassesInTypehintsRule";i:69;s:44:"PHPStan\Rules\Methods\FinalPrivateMethodRule";i:70;s:40:"PHPStan\Rules\Methods\MethodCallableRule";i:71;s:53:"PHPStan\Rules\Methods\MethodVisibilityInInterfaceRule";i:72;s:53:"PHPStan\Rules\Methods\MissingMethodImplementationRule";i:73;s:42:"PHPStan\Rules\Methods\MethodAttributesRule";i:74;s:46:"PHPStan\Rules\Methods\StaticMethodCallableRule";i:75;s:33:"PHPStan\Rules\Names\UsedNamesRule";i:76;s:44:"PHPStan\Rules\Operators\InvalidAssignVarRule";i:77;s:53:"PHPStan\Rules\Properties\AccessPropertiesInAssignRule";i:78;s:59:"PHPStan\Rules\Properties\AccessStaticPropertiesInAssignRule";i:79;s:56:"PHPStan\Rules\Properties\InvalidCallablePropertyTypeRule";i:80;s:58:"PHPStan\Rules\Properties\MissingReadOnlyPropertyAssignRule";i:81;s:50:"PHPStan\Rules\Properties\PropertiesInInterfaceRule";i:82;s:47:"PHPStan\Rules\Properties\PropertyAttributesRule";i:83;s:45:"PHPStan\Rules\Properties\ReadOnlyPropertyRule";i:84;s:50:"PHPStan\Rules\Traits\ConflictingTraitConstantsRule";i:85;s:42:"PHPStan\Rules\Traits\ConstantsInTraitsRule";i:86;s:43:"PHPStan\Rules\Types\InvalidTypesInUnionRule";i:87;s:33:"PHPStan\Rules\Variables\UnsetRule";i:88;s:43:"PHPStan\Rules\Whitespace\FileWhitespaceRule";i:89;s:53:"PHPStan\Rules\Classes\UnusedConstructorParametersRule";i:90;s:36:"PHPStan\Rules\Constants\ConstantRule";i:91;s:45:"PHPStan\Rules\Functions\UnusedClosureUsesRule";i:92;s:33:"PHPStan\Rules\Variables\EmptyRule";i:93;s:33:"PHPStan\Rules\Variables\IssetRule";i:94;s:40:"PHPStan\Rules\Variables\NullCoalesceRule";i:95;s:27:"PHPStan\Rules\Cast\EchoRule";i:96;s:34:"PHPStan\Rules\Cast\InvalidCastRule";i:97;s:50:"PHPStan\Rules\Cast\InvalidPartOfEncapsedStringRule";i:98;s:28:"PHPStan\Rules\Cast\PrintRule";i:99;s:60:"PHPStan\Rules\Classes\AccessPrivateConstantThroughStaticRule";i:100;s:55:"PHPStan\Rules\Comparison\UsageOfVoidMatchExpressionRule";i:101;s:56:"PHPStan\Rules\Constants\ValueAssignedToClassConstantRule";i:102;s:60:"PHPStan\Rules\Functions\IncompatibleDefaultParameterTypeRule";i:103;s:41:"PHPStan\Rules\Generics\ClassAncestorsRule";i:104;s:44:"PHPStan\Rules\Generics\ClassTemplateTypeRule";i:105;s:40:"PHPStan\Rules\Generics\EnumAncestorsRule";i:106;s:43:"PHPStan\Rules\Generics\EnumTemplateTypeRule";i:107;s:47:"PHPStan\Rules\Generics\FunctionTemplateTypeRule";i:108;s:52:"PHPStan\Rules\Generics\FunctionSignatureVarianceRule";i:109;s:45:"PHPStan\Rules\Generics\InterfaceAncestorsRule";i:110;s:48:"PHPStan\Rules\Generics\InterfaceTemplateTypeRule";i:111;s:45:"PHPStan\Rules\Generics\MethodTemplateTypeRule";i:112;s:48:"PHPStan\Rules\Generics\MethodTagTemplateTypeRule";i:113;s:50:"PHPStan\Rules\Generics\MethodSignatureVarianceRule";i:114;s:44:"PHPStan\Rules\Generics\TraitTemplateTypeRule";i:115;s:37:"PHPStan\Rules\Generics\UsedTraitsRule";i:116;s:56:"PHPStan\Rules\Methods\CallPrivateMethodThroughStaticRule";i:117;s:58:"PHPStan\Rules\Methods\IncompatibleDefaultParameterTypeRule";i:118;s:54:"PHPStan\Rules\Operators\InvalidComparisonOperationRule";i:119;s:54:"PHPStan\Rules\PhpDoc\FunctionConditionalReturnTypeRule";i:120;s:52:"PHPStan\Rules\PhpDoc\MethodConditionalReturnTypeRule";i:121;s:39:"PHPStan\Rules\PhpDoc\FunctionAssertRule";i:122;s:37:"PHPStan\Rules\PhpDoc\MethodAssertRule";i:123;s:48:"PHPStan\Rules\PhpDoc\IncompatibleSelfOutTypeRule";i:124;s:60:"PHPStan\Rules\PhpDoc\IncompatibleClassConstantPhpDocTypeRule";i:125;s:47:"PHPStan\Rules\PhpDoc\IncompatiblePhpDocTypeRule";i:126;s:55:"PHPStan\Rules\PhpDoc\IncompatiblePropertyPhpDocTypeRule";i:127;s:49:"PHPStan\Rules\PhpDoc\InvalidThrowsPhpDocValueRule";i:128;s:68:"PHPStan\Rules\PhpDoc\IncompatibleParamImmediatelyInvokedCallableRule";i:129;s:63:"PHPStan\Rules\Properties\AccessPrivatePropertyThroughStaticRule";i:130;s:43:"PHPStan\Rules\Classes\RequireImplementsRule";i:131;s:40:"PHPStan\Rules\Classes\RequireExtendsRule";i:132;s:57:"PHPStan\Rules\PhpDoc\RequireImplementsDefinitionClassRule";i:133;s:54:"PHPStan\Rules\PhpDoc\RequireExtendsDefinitionClassRule";i:134;s:54:"PHPStan\Rules\PhpDoc\RequireExtendsDefinitionTraitRule";i:135;s:43:"PHPStan\Rules\Arrays\ArrayDestructuringRule";i:136;s:42:"PHPStan\Rules\Arrays\IterableInForeachRule";i:137;s:47:"PHPStan\Rules\Arrays\OffsetAccessAssignmentRule";i:138;s:45:"PHPStan\Rules\Arrays\OffsetAccessAssignOpRule";i:139;s:52:"PHPStan\Rules\Arrays\OffsetAccessValueAssignmentRule";i:140;s:46:"PHPStan\Rules\Arrays\UnpackIterableInArrayRule";i:141;s:42:"PHPStan\Rules\Exceptions\ThrowExprTypeRule";i:142;s:51:"PHPStan\Rules\Functions\ArrowFunctionReturnTypeRule";i:143;s:45:"PHPStan\Rules\Functions\ClosureReturnTypeRule";i:144;s:38:"PHPStan\Rules\Functions\ReturnTypeRule";i:145;s:38:"PHPStan\Rules\Generators\YieldTypeRule";i:146;s:36:"PHPStan\Rules\Methods\ReturnTypeRule";i:147;s:66:"PHPStan\Rules\Properties\DefaultValueTypesAssignedToPropertiesRule";i:148;s:51:"PHPStan\Rules\Properties\ReadOnlyPropertyAssignRule";i:149;s:54:"PHPStan\Rules\Properties\ReadOnlyPropertyAssignRefRule";i:150;s:54:"PHPStan\Rules\Properties\TypesAssignedToPropertiesRule";i:151;s:37:"PHPStan\Rules\Variables\ThrowTypeRule";i:152;s:43:"PHPStan\Rules\Variables\VariableCloningRule";i:153;s:36:"PHPStan\Rules\Arrays\DeadForeachRule";i:154;s:47:"PHPStan\Rules\DeadCode\UnreachableStatementRule";i:155;s:48:"PHPStan\Rules\DeadCode\UnusedPrivateConstantRule";i:156;s:46:"PHPStan\Rules\DeadCode\UnusedPrivateMethodRule";i:157;s:58:"PHPStan\Rules\Exceptions\OverwrittenExitPointByFinallyRule";i:158;s:69:"PHPStan\Rules\Functions\CallToFunctionStatementWithoutSideEffectsRule";i:159;s:65:"PHPStan\Rules\Methods\CallToMethodStatementWithoutSideEffectsRule";i:160;s:71:"PHPStan\Rules\Methods\CallToStaticMethodStatementWithoutSideEffectsRule";i:161;s:44:"PHPStan\Rules\Methods\NullsafeMethodCallRule";i:162;s:69:"PHPStan\Rules\TooWideTypehints\TooWideArrowFunctionReturnTypehintRule";i:163;s:63:"PHPStan\Rules\TooWideTypehints\TooWideClosureReturnTypehintRule";i:164;s:64:"PHPStan\Rules\TooWideTypehints\TooWideFunctionReturnTypehintRule";i:165;s:39:"PHPStan\Rules\DateTimeInstantiationRule";i:166;s:56:"PHPStan\Rules\Constants\MissingClassConstantTypehintRule";i:167;s:57:"PHPStan\Rules\Functions\MissingFunctionReturnTypehintRule";i:168;s:53:"PHPStan\Rules\Methods\MissingMethodReturnTypehintRule";i:169;s:52:"PHPStan\Rules\Properties\MissingPropertyTypehintRule";i:170;s:72:"Larastan\Larastan\Rules\UselessConstructs\NoUselessWithFunctionCallsRule";i:171;s:73:"Larastan\Larastan\Rules\UselessConstructs\NoUselessValueFunctionCallsRule";i:172;s:68:"Larastan\Larastan\Rules\DeferrableServiceProviderMissingProvidesRule";i:173;s:68:"Larastan\Larastan\Rules\ConsoleCommand\UndefinedArgumentOrOptionRule";i:174;s:24:"PhpParser\BuilderFactory";i:175;s:27:"PHPStan\Parser\LexerFactory";i:176;s:34:"PhpParser\NodeVisitor\NameResolver";i:177;s:29:"PhpParser\NodeVisitorAbstract";i:178;s:21:"PhpParser\NodeVisitor";i:179;s:36:"PHPStan\Parser\AnonymousClassVisitor";i:180;s:36:"PHPStan\Parser\ArrayFilterArgVisitor";i:181;s:34:"PHPStan\Parser\ArrayFindArgVisitor";i:182;s:33:"PHPStan\Parser\ArrayMapArgVisitor";i:183;s:34:"PHPStan\Parser\ArrayWalkArgVisitor";i:184;s:32:"PHPStan\Parser\ClosureArgVisitor";i:185;s:38:"PHPStan\Parser\ClosureBindToVarVisitor";i:186;s:36:"PHPStan\Parser\ClosureBindArgVisitor";i:187;s:35:"PHPStan\Parser\CurlSetOptArgVisitor";i:188;s:45:"PHPStan\Parser\TypeTraverserInstanceofVisitor";i:189;s:38:"PHPStan\Parser\ArrowFunctionArgVisitor";i:190;s:47:"PHPStan\Parser\MagicConstantParamDefaultVisitor";i:191;s:43:"PHPStan\Parser\NewAssignedToPropertyVisitor";i:192;s:37:"PHPStan\Parser\ParentStmtTypesVisitor";i:193;s:34:"PHPStan\Parser\TryCatchTypeVisitor";i:194;s:35:"PHPStan\Parser\LastConditionVisitor";i:195;s:43:"PhpParser\NodeVisitor\NodeConnectingVisitor";i:196;s:32:"PHPStan\Node\Printer\ExprPrinter";i:197;s:28:"PHPStan\Node\Printer\Printer";i:198;s:32:"PhpParser\PrettyPrinter\Standard";i:199;s:31:"PhpParser\PrettyPrinterAbstract";i:200;s:39:"PHPStan\Broker\AnonymousClassNameHelper";i:201;s:36:"PHPStan\Php\PhpVersionFactoryFactory";i:202;s:32:"PHPStan\PhpDocParser\Lexer\Lexer";i:203;s:38:"PHPStan\PhpDocParser\Parser\TypeParser";i:204;s:40:"PHPStan\PhpDocParser\Parser\PhpDocParser";i:205;s:36:"PHPStan\PhpDocParser\Printer\Printer";i:206;s:37:"PHPStan\PhpDoc\ConstExprParserFactory";i:207;s:40:"PHPStan\PhpDoc\PhpDocInheritanceResolver";i:208;s:33:"PHPStan\PhpDoc\PhpDocNodeResolver";i:209;s:35:"PHPStan\PhpDoc\PhpDocStringResolver";i:210;s:36:"PHPStan\PhpDoc\ConstExprNodeResolver";i:211;s:31:"PHPStan\PhpDoc\TypeNodeResolver";i:212;s:33:"PHPStan\PhpDoc\TypeStringResolver";i:213;s:28:"PHPStan\PhpDoc\StubValidator";i:214;s:42:"PHPStan\PhpDoc\CountableStubFilesExtension";i:215;s:33:"PHPStan\PhpDoc\StubFilesExtension";i:216;s:45:"PHPStan\PhpDoc\SocketSelectStubFilesExtension";i:217;s:39:"PHPStan\PhpDoc\DefaultStubFilesProvider";i:218;s:32:"PHPStan\PhpDoc\StubFilesProvider";i:219;s:45:"PHPStan\PhpDoc\JsonValidateStubFilesExtension";i:220;s:47:"PHPStan\PhpDoc\ReflectionEnumStubFilesExtension";i:221;s:25:"PHPStan\Analyser\Analyser";i:222;s:40:"PHPStan\Analyser\AnalyserResultFinalizer";i:223;s:29:"PHPStan\Analyser\FileAnalyser";i:224;s:38:"PHPStan\Analyser\LocalIgnoresProcessor";i:225;s:37:"PHPStan\Analyser\RuleErrorTransformer";i:226;s:42:"PHPStan\Analyser\Ignore\IgnoredErrorHelper";i:227;s:35:"PHPStan\Analyser\Ignore\IgnoreLexer";i:228;s:41:"PHPStan\Analyser\LazyInternalScopeFactory";i:229;s:37:"PHPStan\Analyser\InternalScopeFactory";i:230;s:29:"PHPStan\Analyser\ScopeFactory";i:231;s:34:"PHPStan\Analyser\NodeScopeResolver";i:232;s:40:"PHPStan\Analyser\ConstantResolverFactory";i:233;s:47:"PHPStan\Analyser\ResultCache\ResultCacheClearer";i:234;s:41:"PHPStan\Analyser\RicherScopeGetTypeHelper";i:235;s:19:"PHPStan\Cache\Cache";i:236;s:34:"PHPStan\Collectors\RegistryFactory";i:237;s:34:"PHPStan\Command\AnalyseApplication";i:238;s:30:"PHPStan\Command\AnalyserRunner";i:239;s:32:"PHPStan\Command\FixerApplication";i:240;s:37:"PHPStan\Dependency\DependencyResolver";i:241;s:38:"PHPStan\Dependency\ExportedNodeFetcher";i:242;s:39:"PHPStan\Dependency\ExportedNodeResolver";i:243;s:38:"PHPStan\Dependency\ExportedNodeVisitor";i:244;s:48:"PHPStan\DependencyInjection\Nette\NetteContainer";i:245;s:37:"PHPStan\DependencyInjection\Container";i:246;s:54:"PHPStan\DependencyInjection\DerivativeContainerFactory";i:247;s:23:"PHPStan\File\FileHelper";i:248;s:32:"PHPStan\File\FileExcluderFactory";i:249;s:23:"PHPStan\File\FileFinder";i:250;s:24:"PHPStan\File\FileMonitor";i:251;s:37:"PHPStan\Parser\DeclarePositionVisitor";i:252;s:47:"PHPStan\Parser\ImmediatelyInvokedClosureVisitor";i:253;s:33:"PHPStan\Parallel\ParallelAnalyser";i:254;s:26:"PHPStan\Parallel\Scheduler";i:255;s:34:"PHPStan\Diagnose\DiagnoseExtension";i:256;s:42:"PHPStan\Parser\FunctionCallStatementFinder";i:257;s:30:"PHPStan\Process\CpuCoreCounter";i:258;s:46:"PHPStan\Reflection\InitializerExprTypeResolver";i:259;s:73:"PHPStan\Reflection\Annotations\AnnotationsMethodsClassReflectionExtension";i:260;s:50:"PHPStan\Reflection\MethodsClassReflectionExtension";i:261;s:76:"PHPStan\Reflection\Annotations\AnnotationsPropertiesClassReflectionExtension";i:262;s:53:"PHPStan\Reflection\PropertiesClassReflectionExtension";i:263;s:64:"PHPStan\Reflection\BetterReflection\SourceLocator\CachingVisitor";i:264;s:66:"PHPStan\Reflection\BetterReflection\SourceLocator\FileNodesFetcher";i:265;s:96:"PHPStan\Reflection\BetterReflection\SourceLocator\ComposerJsonAndInstalledJsonSourceLocatorMaker";i:266;s:88:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedDirectorySourceLocatorFactory";i:267;s:91:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedDirectorySourceLocatorRepository";i:268;s:92:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedSingleFileSourceLocatorRepository";i:269;s:81:"PHPStan\Reflection\RequireExtension\RequireExtendsMethodsClassReflectionExtension";i:270;s:84:"PHPStan\Reflection\RequireExtension\RequireExtendsPropertiesClassReflectionExtension";i:271;s:61:"PHPStan\Reflection\Mixin\MixinMethodsClassReflectionExtension";i:272;s:64:"PHPStan\Reflection\Mixin\MixinPropertiesClassReflectionExtension";i:273;s:50:"PHPStan\Reflection\Php\PhpClassReflectionExtension";i:274;s:69:"PHPStan\Reflection\Php\Soap\SoapClientMethodsClassReflectionExtension";i:275;s:66:"PHPStan\Reflection\Php\EnumAllowedSubTypesClassReflectionExtension";i:276;s:58:"PHPStan\Reflection\AllowedSubTypesClassReflectionExtension";i:277;s:68:"PHPStan\Reflection\Php\UniversalObjectCratesClassReflectionExtension";i:278;s:79:"PHPStan\Reflection\PHPStan\NativeReflectionEnumReturnDynamicReturnTypeExtension";i:279;s:45:"PHPStan\Type\DynamicMethodReturnTypeExtension";i:280;s:64:"PHPStan\Reflection\SignatureMap\NativeFunctionReflectionProvider";i:281;s:50:"PHPStan\Reflection\SignatureMap\SignatureMapParser";i:282;s:60:"PHPStan\Reflection\SignatureMap\FunctionSignatureMapProvider";i:283;s:52:"PHPStan\Reflection\SignatureMap\SignatureMapProvider";i:284;s:56:"PHPStan\Reflection\SignatureMap\Php8SignatureMapProvider";i:285;s:59:"PHPStan\Reflection\SignatureMap\SignatureMapProviderFactory";i:286;s:31:"PHPStan\Rules\Api\ApiRuleHelper";i:287;s:29:"PHPStan\Rules\AttributesCheck";i:288;s:58:"PHPStan\Rules\Arrays\NonexistentOffsetInArrayDimFetchCheck";i:289;s:28:"PHPStan\Rules\ClassNameCheck";i:290;s:39:"PHPStan\Rules\ClassCaseSensitivityCheck";i:291;s:37:"PHPStan\Rules\ClassForbiddenNameCheck";i:292;s:43:"PHPStan\Rules\Classes\LocalTypeAliasesCheck";i:293;s:36:"PHPStan\Rules\Classes\MethodTagCheck";i:294;s:32:"PHPStan\Rules\Classes\MixinCheck";i:295;s:38:"PHPStan\Rules\Classes\PropertyTagCheck";i:296;s:52:"PHPStan\Rules\Comparison\ConstantConditionRuleHelper";i:297;s:50:"PHPStan\Rules\Comparison\ImpossibleCheckTypeHelper";i:298;s:53:"PHPStan\Rules\Exceptions\DefaultExceptionTypeResolver";i:299;s:46:"PHPStan\Rules\Exceptions\ExceptionTypeResolver";i:300;s:68:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInFunctionThrowsRule";i:301;s:66:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInMethodThrowsRule";i:302;s:61:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInThrowsCheck";i:303;s:53:"PHPStan\Rules\Exceptions\TooWideFunctionThrowTypeRule";i:304;s:51:"PHPStan\Rules\Exceptions\TooWideMethodThrowTypeRule";i:305;s:46:"PHPStan\Rules\Exceptions\TooWideThrowTypeCheck";i:306;s:41:"PHPStan\Rules\FunctionCallParametersCheck";i:307;s:37:"PHPStan\Rules\FunctionDefinitionCheck";i:308;s:37:"PHPStan\Rules\FunctionReturnTypeCheck";i:309;s:44:"PHPStan\Rules\ParameterCastableToStringCheck";i:310;s:49:"PHPStan\Rules\Generics\CrossCheckInterfacesHelper";i:311;s:44:"PHPStan\Rules\Generics\GenericAncestorsCheck";i:312;s:45:"PHPStan\Rules\Generics\GenericObjectTypeCheck";i:313;s:49:"PHPStan\Rules\Generics\MethodTagTemplateTypeCheck";i:314;s:40:"PHPStan\Rules\Generics\TemplateTypeCheck";i:315;s:36:"PHPStan\Rules\Generics\VarianceCheck";i:316;s:24:"PHPStan\Rules\IssetCheck";i:317;s:37:"PHPStan\Rules\Methods\MethodCallCheck";i:318;s:43:"PHPStan\Rules\Methods\StaticMethodCallCheck";i:319;s:41:"PHPStan\Rules\Methods\MethodSignatureRule";i:320;s:53:"PHPStan\Rules\Methods\MethodParameterComparisonHelper";i:321;s:34:"PHPStan\Rules\MissingTypehintCheck";i:322;s:27:"PHPStan\Rules\NullsafeCheck";i:323;s:69:"PHPStan\Rules\Constants\LazyAlwaysUsedClassConstantsExtensionProvider";i:324;s:65:"PHPStan\Rules\Constants\AlwaysUsedClassConstantsExtensionProvider";i:325;s:59:"PHPStan\Rules\Methods\LazyAlwaysUsedMethodExtensionProvider";i:326;s:55:"PHPStan\Rules\Methods\AlwaysUsedMethodExtensionProvider";i:327;s:52:"PHPStan\Rules\PhpDoc\ConditionalReturnTypeRuleHelper";i:328;s:37:"PHPStan\Rules\PhpDoc\AssertRuleHelper";i:329;s:43:"PHPStan\Rules\PhpDoc\UnresolvableTypeHelper";i:330;s:46:"PHPStan\Rules\PhpDoc\GenericCallableRuleHelper";i:331;s:41:"PHPStan\Rules\PhpDoc\VarTagTypeRuleHelper";i:332;s:40:"PHPStan\Rules\Playground\NeverRuleHelper";i:333;s:65:"PHPStan\Rules\Properties\LazyReadWritePropertiesExtensionProvider";i:334;s:61:"PHPStan\Rules\Properties\ReadWritePropertiesExtensionProvider";i:335;s:43:"PHPStan\Rules\Properties\PropertyDescriptor";i:336;s:49:"PHPStan\Rules\Properties\PropertyReflectionFinder";i:337;s:38:"PHPStan\Rules\Pure\FunctionPurityCheck";i:338;s:29:"PHPStan\Rules\RuleLevelHelper";i:339;s:43:"PHPStan\Rules\UnusedFunctionParametersCheck";i:340;s:59:"PHPStan\Rules\TooWideTypehints\TooWideParameterOutTypeCheck";i:341;s:27:"PHPStan\Type\FileTypeMapper";i:342;s:30:"PHPStan\Type\BitwiseFlagHelper";i:343;s:54:"PHPStan\Type\Php\AbsFunctionDynamicReturnTypeExtension";i:344;s:47:"PHPStan\Type\DynamicFunctionReturnTypeExtension";i:345;s:57:"PHPStan\Type\Php\ArgumentBasedFunctionReturnTypeExtension";i:346;s:62:"PHPStan\Type\Php\ArrayChangeKeyCaseFunctionReturnTypeExtension";i:347;s:61:"PHPStan\Type\Php\ArrayIntersectKeyFunctionReturnTypeExtension";i:348;s:54:"PHPStan\Type\Php\ArrayChunkFunctionReturnTypeExtension";i:349;s:55:"PHPStan\Type\Php\ArrayColumnFunctionReturnTypeExtension";i:350;s:56:"PHPStan\Type\Php\ArrayCombineFunctionReturnTypeExtension";i:351;s:55:"PHPStan\Type\Php\ArrayCurrentDynamicReturnTypeExtension";i:352;s:53:"PHPStan\Type\Php\ArrayFillFunctionReturnTypeExtension";i:353;s:57:"PHPStan\Type\Php\ArrayFillKeysFunctionReturnTypeExtension";i:354;s:52:"PHPStan\Type\Php\ArrayFilterFunctionReturnTypeHelper";i:355;s:55:"PHPStan\Type\Php\ArrayFilterFunctionReturnTypeExtension";i:356;s:53:"PHPStan\Type\Php\ArrayFlipFunctionReturnTypeExtension";i:357;s:53:"PHPStan\Type\Php\ArrayFindFunctionReturnTypeExtension";i:358;s:56:"PHPStan\Type\Php\ArrayFindKeyFunctionReturnTypeExtension";i:359;s:51:"PHPStan\Type\Php\ArrayKeyDynamicReturnTypeExtension";i:360;s:62:"PHPStan\Type\Php\ArrayKeyExistsFunctionTypeSpecifyingExtension";i:361;s:44:"PHPStan\Type\FunctionTypeSpecifyingExtension";i:362;s:44:"PHPStan\Analyser\TypeSpecifierAwareExtension";i:363;s:56:"PHPStan\Type\Php\ArrayKeyFirstDynamicReturnTypeExtension";i:364;s:55:"PHPStan\Type\Php\ArrayKeyLastDynamicReturnTypeExtension";i:365;s:60:"PHPStan\Type\Php\ArrayKeysFunctionDynamicReturnTypeExtension";i:366;s:52:"PHPStan\Type\Php\ArrayMapFunctionReturnTypeExtension";i:367;s:61:"PHPStan\Type\Php\ArrayMergeFunctionDynamicReturnTypeExtension";i:368;s:52:"PHPStan\Type\Php\ArrayNextDynamicReturnTypeExtension";i:369;s:52:"PHPStan\Type\Php\ArrayPopFunctionReturnTypeExtension";i:370;s:53:"PHPStan\Type\Php\ArrayRandFunctionReturnTypeExtension";i:371;s:55:"PHPStan\Type\Php\ArrayReduceFunctionReturnTypeExtension";i:372;s:56:"PHPStan\Type\Php\ArrayReplaceFunctionReturnTypeExtension";i:373;s:56:"PHPStan\Type\Php\ArrayReverseFunctionReturnTypeExtension";i:374;s:54:"PHPStan\Type\Php\ArrayShiftFunctionReturnTypeExtension";i:375;s:54:"PHPStan\Type\Php\ArraySliceFunctionReturnTypeExtension";i:376;s:55:"PHPStan\Type\Php\ArraySpliceFunctionReturnTypeExtension";i:377;s:62:"PHPStan\Type\Php\ArraySearchFunctionDynamicReturnTypeExtension";i:378;s:59:"PHPStan\Type\Php\ArraySearchFunctionTypeSpecifyingExtension";i:379;s:62:"PHPStan\Type\Php\ArrayValuesFunctionDynamicReturnTypeExtension";i:380;s:59:"PHPStan\Type\Php\ArraySumFunctionDynamicReturnTypeExtension";i:381;s:41:"PHPStan\Type\Php\AssertThrowTypeExtension";i:382;s:46:"PHPStan\Type\DynamicFunctionThrowTypeExtension";i:383;s:63:"PHPStan\Type\Php\BackedEnumFromMethodDynamicReturnTypeExtension";i:384;s:51:"PHPStan\Type\DynamicStaticMethodReturnTypeExtension";i:385;s:63:"PHPStan\Type\Php\Base64DecodeDynamicFunctionReturnTypeExtension";i:386;s:54:"PHPStan\Type\Php\BcMathStringOrNullReturnTypeExtension";i:387;s:54:"PHPStan\Type\Php\ClosureBindDynamicReturnTypeExtension";i:388;s:56:"PHPStan\Type\Php\ClosureBindToDynamicReturnTypeExtension";i:389;s:62:"PHPStan\Type\Php\ClosureFromCallableDynamicReturnTypeExtension";i:390;s:51:"PHPStan\Type\Php\CompactFunctionReturnTypeExtension";i:391;s:52:"PHPStan\Type\Php\ConstantFunctionReturnTypeExtension";i:392;s:31:"PHPStan\Type\Php\ConstantHelper";i:393;s:49:"PHPStan\Type\Php\CountFunctionReturnTypeExtension";i:394;s:53:"PHPStan\Type\Php\CountFunctionTypeSpecifyingExtension";i:395;s:62:"PHPStan\Type\Php\CurlGetinfoFunctionDynamicReturnTypeExtension";i:396;s:45:"PHPStan\Type\Php\DateFunctionReturnTypeHelper";i:397;s:54:"PHPStan\Type\Php\DateFormatFunctionReturnTypeExtension";i:398;s:52:"PHPStan\Type\Php\DateFormatMethodReturnTypeExtension";i:399;s:48:"PHPStan\Type\Php\DateFunctionReturnTypeExtension";i:400;s:58:"PHPStan\Type\Php\DateIntervalConstructorThrowTypeExtension";i:401;s:50:"PHPStan\Type\DynamicStaticMethodThrowTypeExtension";i:402;s:55:"PHPStan\Type\Php\DateIntervalDynamicReturnTypeExtension";i:403;s:57:"PHPStan\Type\Php\DateTimeCreateDynamicReturnTypeExtension";i:404;s:51:"PHPStan\Type\Php\DateTimeDynamicReturnTypeExtension";i:405;s:50:"PHPStan\Type\Php\DateTimeModifyReturnTypeExtension";i:406;s:54:"PHPStan\Type\Php\DateTimeConstructorThrowTypeExtension";i:407;s:55:"PHPStan\Type\Php\DateTimeModifyMethodThrowTypeExtension";i:408;s:44:"PHPStan\Type\DynamicMethodThrowTypeExtension";i:409;s:52:"PHPStan\Type\Php\DateTimeSubMethodThrowTypeExtension";i:410;s:58:"PHPStan\Type\Php\DateTimeZoneConstructorThrowTypeExtension";i:411;s:48:"PHPStan\Type\Php\DsMapDynamicReturnTypeExtension";i:412;s:53:"PHPStan\Type\Php\DsMapDynamicMethodThrowTypeExtension";i:413;s:58:"PHPStan\Type\Php\DioStatDynamicFunctionReturnTypeExtension";i:414;s:58:"PHPStan\Type\Php\ExplodeFunctionDynamicReturnTypeExtension";i:415;s:47:"PHPStan\Type\Php\FilterFunctionReturnTypeHelper";i:416;s:54:"PHPStan\Type\Php\FilterInputDynamicReturnTypeExtension";i:417;s:52:"PHPStan\Type\Php\FilterVarDynamicReturnTypeExtension";i:418;s:57:"PHPStan\Type\Php\FilterVarArrayDynamicReturnTypeExtension";i:419;s:57:"PHPStan\Type\Php\GetCalledClassDynamicReturnTypeExtension";i:420;s:51:"PHPStan\Type\Php\GetClassDynamicReturnTypeExtension";i:421;s:56:"PHPStan\Type\Php\GetDebugTypeFunctionReturnTypeExtension";i:422;s:58:"PHPStan\Type\Php\GetDefinedVarsFunctionReturnTypeExtension";i:423;s:65:"PHPStan\Type\Php\GetParentClassDynamicFunctionReturnTypeExtension";i:424;s:51:"PHPStan\Type\Php\GettypeFunctionReturnTypeExtension";i:425;s:63:"PHPStan\Type\Php\GettimeofdayDynamicFunctionReturnTypeExtension";i:426;s:49:"PHPStan\Type\Php\HashFunctionsReturnTypeExtension";i:427;s:58:"PHPStan\Type\Php\HighlightStringDynamicReturnTypeExtension";i:428;s:41:"PHPStan\Type\Php\IntdivThrowTypeExtension";i:429;s:42:"PHPStan\Type\Php\IniGetReturnTypeExtension";i:430;s:39:"PHPStan\Type\Php\JsonThrowTypeExtension";i:431;s:56:"PHPStan\Type\Php\OpenSslEncryptParameterOutTypeExtension";i:432;s:46:"PHPStan\Type\FunctionParameterOutTypeExtension";i:433;s:50:"PHPStan\Type\Php\ParseStrParameterOutTypeExtension";i:434;s:49:"PHPStan\Type\Php\PregMatchTypeSpecifyingExtension";i:435;s:51:"PHPStan\Type\Php\PregMatchParameterOutTypeExtension";i:436;s:56:"PHPStan\Type\Php\PregReplaceCallbackClosureTypeExtension";i:437;s:50:"PHPStan\Type\FunctionParameterClosureTypeExtension";i:438;s:39:"PHPStan\Type\Php\RegexArrayShapeMatcher";i:439;s:35:"PHPStan\Type\Regex\RegexGroupParser";i:440;s:40:"PHPStan\Type\Regex\RegexExpressionHelper";i:441;s:61:"PHPStan\Type\Php\ReflectionClassConstructorThrowTypeExtension";i:442;s:64:"PHPStan\Type\Php\ReflectionFunctionConstructorThrowTypeExtension";i:443;s:62:"PHPStan\Type\Php\ReflectionMethodConstructorThrowTypeExtension";i:444;s:64:"PHPStan\Type\Php\ReflectionPropertyConstructorThrowTypeExtension";i:445;s:53:"PHPStan\Type\Php\StrContainingTypeSpecifyingExtension";i:446;s:65:"PHPStan\Type\Php\SimpleXMLElementClassPropertyReflectionExtension";i:447;s:62:"PHPStan\Type\Php\SimpleXMLElementConstructorThrowTypeExtension";i:448;s:47:"PHPStan\Type\Php\StatDynamicReturnTypeExtension";i:449;s:52:"PHPStan\Type\Php\MethodExistsTypeSpecifyingExtension";i:450;s:54:"PHPStan\Type\Php\PropertyExistsTypeSpecifyingExtension";i:451;s:50:"PHPStan\Type\Php\MinMaxFunctionReturnTypeExtension";i:452;s:63:"PHPStan\Type\Php\NumberFormatFunctionDynamicReturnTypeExtension";i:453;s:59:"PHPStan\Type\Php\PathinfoFunctionDynamicReturnTypeExtension";i:454;s:54:"PHPStan\Type\Php\PregFilterFunctionReturnTypeExtension";i:455;s:52:"PHPStan\Type\Php\PregSplitDynamicReturnTypeExtension";i:456;s:67:"PHPStan\Type\Php\ReflectionClassIsSubclassOfTypeSpecifyingExtension";i:457;s:42:"PHPStan\Type\MethodTypeSpecifyingExtension";i:458;s:59:"PHPStan\Type\Php\ReplaceFunctionsDynamicReturnTypeExtension";i:459;s:64:"PHPStan\Type\Php\ArrayPointerFunctionsDynamicReturnTypeExtension";i:460;s:49:"PHPStan\Type\Php\LtrimFunctionReturnTypeExtension";i:461;s:47:"PHPStan\Type\Php\MbFunctionsReturnTypeExtension";i:462;s:52:"PHPStan\Type\Php\MbFunctionsReturnTypeExtensionTrait";i:463;s:61:"PHPStan\Type\Php\MbConvertEncodingFunctionReturnTypeExtension";i:464;s:64:"PHPStan\Type\Php\MbSubstituteCharacterDynamicReturnTypeExtension";i:465;s:52:"PHPStan\Type\Php\MbStrlenFunctionReturnTypeExtension";i:466;s:53:"PHPStan\Type\Php\MicrotimeFunctionReturnTypeExtension";i:467;s:50:"PHPStan\Type\Php\HrtimeFunctionReturnTypeExtension";i:468;s:51:"PHPStan\Type\Php\ImplodeFunctionReturnTypeExtension";i:469;s:59:"PHPStan\Type\Php\NonEmptyStringFunctionsReturnTypeExtension";i:470;s:55:"PHPStan\Type\Php\SetTypeFunctionTypeSpecifyingExtension";i:471;s:52:"PHPStan\Type\Php\StrCaseFunctionsReturnTypeExtension";i:472;s:50:"PHPStan\Type\Php\StrlenFunctionReturnTypeExtension";i:473;s:65:"PHPStan\Type\Php\StrIncrementDecrementFunctionReturnTypeExtension";i:474;s:50:"PHPStan\Type\Php\StrPadFunctionReturnTypeExtension";i:475;s:53:"PHPStan\Type\Php\StrRepeatFunctionReturnTypeExtension";i:476;s:50:"PHPStan\Type\Php\StrrevFunctionReturnTypeExtension";i:477;s:49:"PHPStan\Type\Php\SubstrDynamicReturnTypeExtension";i:478;s:45:"PHPStan\Type\Php\ThrowableReturnTypeExtension";i:479;s:59:"PHPStan\Type\Php\ParseUrlFunctionDynamicReturnTypeExtension";i:480;s:55:"PHPStan\Type\Php\TriggerErrorDynamicReturnTypeExtension";i:481;s:55:"PHPStan\Type\Php\TrimFunctionDynamicReturnTypeExtension";i:482;s:65:"PHPStan\Type\Php\VersionCompareFunctionDynamicReturnTypeExtension";i:483;s:47:"PHPStan\Type\Php\PowFunctionReturnTypeExtension";i:484;s:49:"PHPStan\Type\Php\RoundFunctionReturnTypeExtension";i:485;s:53:"PHPStan\Type\Php\StrtotimeFunctionReturnTypeExtension";i:486;s:53:"PHPStan\Type\Php\RandomIntFunctionReturnTypeExtension";i:487;s:49:"PHPStan\Type\Php\RangeFunctionReturnTypeExtension";i:488;s:54:"PHPStan\Type\Php\AssertFunctionTypeSpecifyingExtension";i:489;s:59:"PHPStan\Type\Php\ClassExistsFunctionTypeSpecifyingExtension";i:490;s:59:"PHPStan\Type\Php\ClassImplementsFunctionReturnTypeExtension";i:491;s:54:"PHPStan\Type\Php\DefineConstantTypeSpecifyingExtension";i:492;s:55:"PHPStan\Type\Php\DefinedConstantTypeSpecifyingExtension";i:493;s:62:"PHPStan\Type\Php\FunctionExistsFunctionTypeSpecifyingExtension";i:494;s:55:"PHPStan\Type\Php\InArrayFunctionTypeSpecifyingExtension";i:495;s:55:"PHPStan\Type\Php\IsArrayFunctionTypeSpecifyingExtension";i:496;s:58:"PHPStan\Type\Php\IsCallableFunctionTypeSpecifyingExtension";i:497;s:58:"PHPStan\Type\Php\IsIterableFunctionTypeSpecifyingExtension";i:498;s:60:"PHPStan\Type\Php\IsSubclassOfFunctionTypeSpecifyingExtension";i:499;s:59:"PHPStan\Type\Php\IteratorToArrayFunctionReturnTypeExtension";i:500;s:51:"PHPStan\Type\Php\IsAFunctionTypeSpecifyingExtension";i:501;s:48:"PHPStan\Type\Php\IsAFunctionTypeSpecifyingHelper";i:502;s:58:"PHPStan\Type\Php\CtypeDigitFunctionTypeSpecifyingExtension";i:503;s:59:"PHPStan\Type\Php\JsonThrowOnErrorDynamicReturnTypeExtension";i:504;s:66:"PHPStan\Type\Php\TypeSpecifyingFunctionsDynamicReturnTypeExtension";i:505;s:63:"PHPStan\Type\Php\SimpleXMLElementAsXMLMethodReturnTypeExtension";i:506;s:63:"PHPStan\Type\Php\SimpleXMLElementXpathMethodReturnTypeExtension";i:507;s:52:"PHPStan\Type\Php\StrSplitFunctionReturnTypeExtension";i:508;s:50:"PHPStan\Type\Php\StrTokFunctionReturnTypeExtension";i:509;s:58:"PHPStan\Type\Php\SprintfFunctionDynamicReturnTypeExtension";i:510;s:57:"PHPStan\Type\Php\SscanfFunctionDynamicReturnTypeExtension";i:511;s:56:"PHPStan\Type\Php\StrvalFamilyFunctionReturnTypeExtension";i:512;s:63:"PHPStan\Type\Php\StrWordCountFunctionDynamicReturnTypeExtension";i:513;s:49:"PHPStan\Type\Php\XMLReaderOpenReturnTypeExtension";i:514;s:65:"PHPStan\Type\Php\ReflectionGetAttributesMethodReturnTypeExtension";i:515;s:57:"PHPStan\Type\Php\DatePeriodConstructorReturnTypeExtension";i:516;s:31:"PHPStan\Type\ClosureTypeFactory";i:517;s:43:"PHPStan\Type\Constant\OversizedArrayBuilder";i:518;s:36:"PHPStan\Rules\Functions\PrintfHelper";i:519;s:37:"PHPStan\Analyser\TypeSpecifierFactory";i:520;s:46:"PHPStan\File\ParentDirectoryRelativePathHelper";i:521;s:31:"PHPStan\File\RelativePathHelper";i:522;s:28:"PHPStan\Broker\BrokerFactory";i:523;s:30:"PHPStan\Cache\FileCacheStorage";i:524;s:26:"PHPStan\Cache\CacheStorage";i:525;s:25:"PHPStan\Parser\RichParser";i:526;s:21:"PHPStan\Parser\Parser";i:527;s:29:"PHPStan\Parser\CleaningParser";i:528;s:27:"PHPStan\Parser\SimpleParser";i:529;s:27:"PHPStan\Parser\CachedParser";i:530;s:33:"PHPStan\Parser\PhpParserDecorator";i:531;s:16:"PhpParser\Parser";i:532;s:21:"PhpParser\Parser\Php7";i:533;s:24:"PhpParser\ParserAbstract";i:534;s:26:"PHPStan\Rules\LazyRegistry";i:535;s:22:"PHPStan\Rules\Registry";i:536;s:33:"PHPStan\PhpDoc\StubPhpDocProvider";i:537;s:63:"PHPStan\Reflection\ReflectionProvider\ReflectionProviderFactory";i:538;s:37:"PHPStan\Reflection\ReflectionProvider";i:539;s:51:"PHPStan\BetterReflection\Reflector\DefaultReflector";i:540;s:44:"PHPStan\BetterReflection\Reflector\Reflector";i:541;s:64:"PHPStan\Reflection\BetterReflection\Reflector\MemoizingReflector";i:542;s:49:"PHPStan\BetterReflection\Reflector\ClassReflector";i:543;s:52:"PHPStan\BetterReflection\Reflector\FunctionReflector";i:544;s:52:"PHPStan\BetterReflection\Reflector\ConstantReflector";i:545;s:60:"PHPStan\Reflection\BetterReflection\BetterReflectionProvider";i:546;s:72:"PHPStan\Reflection\BetterReflection\BetterReflectionSourceLocatorFactory";i:547;s:83:"PHPStan\Reflection\BetterReflection\SourceStubber\PhpStormStubsSourceStubberFactory";i:548;s:79:"PHPStan\BetterReflection\SourceLocator\SourceStubber\PhpStormStubsSourceStubber";i:549;s:66:"PHPStan\BetterReflection\SourceLocator\SourceStubber\SourceStubber";i:550;s:76:"PHPStan\BetterReflection\SourceLocator\SourceStubber\ReflectionSourceStubber";i:551;s:80:"PHPStan\Reflection\BetterReflection\SourceStubber\ReflectionSourceStubberFactory";i:552;s:32:"PHPStan\Parser\PathRoutingParser";i:553;s:41:"PHPStan\Diagnose\PHPStanDiagnoseExtension";i:554;s:55:"PHPStan\Command\ErrorFormatter\CiDetectedErrorFormatter";i:555;s:45:"PHPStan\Command\ErrorFormatter\ErrorFormatter";i:556;s:48:"PHPStan\Command\ErrorFormatter\RawErrorFormatter";i:557;s:50:"PHPStan\Command\ErrorFormatter\TableErrorFormatter";i:558;s:55:"PHPStan\Command\ErrorFormatter\CheckstyleErrorFormatter";i:559;s:49:"PHPStan\Command\ErrorFormatter\JsonErrorFormatter";i:560;s:50:"PHPStan\Command\ErrorFormatter\JunitErrorFormatter";i:561;s:51:"PHPStan\Command\ErrorFormatter\GitlabErrorFormatter";i:562;s:51:"PHPStan\Command\ErrorFormatter\GithubErrorFormatter";i:563;s:53:"PHPStan\Command\ErrorFormatter\TeamcityErrorFormatter";i:564;s:40:"PHPStan\Rules\Api\ApiClassConstFetchRule";i:565;s:35:"PHPStan\Rules\Api\ApiInstanceofRule";i:566;s:39:"PHPStan\Rules\Api\ApiInstanceofTypeRule";i:567;s:53:"PHPStan\Rules\Api\NodeConnectingVisitorAttributesRule";i:568;s:47:"PHPStan\Rules\Api\RuntimeReflectionFunctionRule";i:569;s:52:"PHPStan\Rules\Api\RuntimeReflectionInstantiationRule";i:570;s:53:"PHPStan\Rules\Classes\ExistingClassInClassExtendsRule";i:571;s:51:"PHPStan\Rules\Classes\ExistingClassInInstanceOfRule";i:572;s:50:"PHPStan\Rules\Classes\LocalTypeTraitUseAliasesRule";i:573;s:53:"PHPStan\Rules\Exceptions\CaughtExceptionExistenceRule";i:574;s:53:"PHPStan\Rules\Functions\CallToNonExistentFunctionRule";i:575;s:46:"PHPStan\Rules\Constants\OverridingConstantRule";i:576;s:42:"PHPStan\Rules\Methods\OverridingMethodRule";i:577;s:47:"PHPStan\Rules\Methods\ConsistentConstructorRule";i:578;s:39:"PHPStan\Rules\Missing\MissingReturnRule";i:579;s:52:"PHPStan\Rules\Namespaces\ExistingNamesInGroupUseRule";i:580;s:47:"PHPStan\Rules\Namespaces\ExistingNamesInUseRule";i:581;s:50:"PHPStan\Rules\Operators\InvalidIncDecOperationRule";i:582;s:45:"PHPStan\Rules\Properties\AccessPropertiesRule";i:583;s:51:"PHPStan\Rules\Properties\AccessStaticPropertiesRule";i:584;s:56:"PHPStan\Rules\Properties\ExistingClassesInPropertiesRule";i:585;s:44:"PHPStan\Rules\Functions\FunctionCallableRule";i:586;s:66:"PHPStan\Rules\Properties\MissingReadOnlyByPhpDocPropertyAssignRule";i:587;s:47:"PHPStan\Rules\Properties\OverridingPropertyRule";i:588;s:53:"PHPStan\Rules\Properties\ReadOnlyByPhpDocPropertyRule";i:589;s:50:"PHPStan\Rules\Properties\UninitializedPropertyRule";i:590;s:56:"PHPStan\Rules\Properties\WritingToReadOnlyPropertiesRule";i:591;s:55:"PHPStan\Rules\Properties\ReadingWriteOnlyPropertiesRule";i:592;s:44:"PHPStan\Rules\Variables\CompactVariablesRule";i:593;s:43:"PHPStan\Rules\Variables\DefinedVariableRule";i:594;s:49:"PHPStan\Rules\Regexp\RegularExpressionPatternRule";i:595;s:37:"PHPStan\Reflection\ConstructorsHelper";i:596;s:58:"PHPStan\Rules\Methods\MissingMagicSerializationMethodsRule";i:597;s:48:"PHPStan\Rules\Constants\MagicConstantContextRule";i:598;s:54:"PHPStan\Rules\Functions\UselessFunctionReturnValueRule";i:599;s:49:"PHPStan\Rules\Functions\PrintfArrayParametersRule";i:600;s:49:"PHPStan\Rules\Regexp\RegularExpressionQuotingRule";i:601;s:44:"PHPStan\Rules\Keywords\RequireFileExistsRule";i:602;s:31:"PHPStan\Rules\Classes\MixinRule";i:603;s:36:"PHPStan\Rules\Classes\MixinTraitRule";i:604;s:39:"PHPStan\Rules\Classes\MixinTraitUseRule";i:605;s:35:"PHPStan\Rules\Classes\MethodTagRule";i:606;s:40:"PHPStan\Rules\Classes\MethodTagTraitRule";i:607;s:43:"PHPStan\Rules\Classes\MethodTagTraitUseRule";i:608;s:37:"PHPStan\Rules\Classes\PropertyTagRule";i:609;s:42:"PHPStan\Rules\Classes\PropertyTagTraitRule";i:610;s:45:"PHPStan\Rules\Classes\PropertyTagTraitUseRule";i:611;s:40:"PHPStan\Rules\PhpDoc\RequireExtendsCheck";i:612;s:57:"PHPStan\Rules\PhpDoc\RequireImplementsDefinitionTraitRule";i:613;s:73:"PHPStan\Rules\Functions\IncompatibleArrowFunctionDefaultParameterTypeRule";i:614;s:67:"PHPStan\Rules\Functions\IncompatibleClosureDefaultParameterTypeRule";i:615;s:41:"PHPStan\Rules\Functions\CallCallablesRule";i:616;s:53:"PHPStan\Rules\Generics\MethodTagTemplateTypeTraitRule";i:617;s:54:"PHPStan\Rules\Methods\IllegalConstructorMethodCallRule";i:618;s:54:"PHPStan\Rules\Methods\IllegalConstructorStaticCallRule";i:619;s:46:"PHPStan\Rules\PhpDoc\InvalidPhpDocTagValueRule";i:620;s:48:"PHPStan\Rules\PhpDoc\InvalidPhpDocVarTagTypeRule";i:621;s:45:"PHPStan\Rules\PhpDoc\InvalidPHPStanDocTagRule";i:622;s:52:"PHPStan\Rules\PhpDoc\VarTagChangedExpressionTypeRule";i:623;s:50:"PHPStan\Rules\PhpDoc\WrongVariableNameInVarTagRule";i:624;s:43:"PHPStan\Rules\Generics\PropertyVarianceRule";i:625;s:35:"PHPStan\Rules\Pure\PureFunctionRule";i:626;s:33:"PHPStan\Rules\Pure\PureMethodRule";i:627;s:50:"PHPStan\Rules\Operators\InvalidBinaryOperationRule";i:628;s:49:"PHPStan\Rules\Operators\InvalidUnaryOperationRule";i:629;s:50:"PHPStan\Rules\Arrays\InvalidKeyInArrayDimFetchRule";i:630;s:46:"PHPStan\Rules\Arrays\InvalidKeyInArrayItemRule";i:631;s:57:"PHPStan\Rules\Arrays\NonexistentOffsetInArrayDimFetchRule";i:632;s:69:"PHPStan\Rules\Exceptions\ThrowsVoidFunctionWithExplicitThrowPointRule";i:633;s:67:"PHPStan\Rules\Exceptions\ThrowsVoidMethodWithExplicitThrowPointRule";i:634;s:42:"PHPStan\Rules\Generators\YieldFromTypeRule";i:635;s:45:"PHPStan\Rules\Generators\YieldInGeneratorRule";i:636;s:39:"PHPStan\Rules\Arrays\ArrayUnpackingRule";i:637;s:62:"PHPStan\Rules\Properties\ReadOnlyByPhpDocPropertyAssignRefRule";i:638;s:59:"PHPStan\Rules\Properties\ReadOnlyByPhpDocPropertyAssignRule";i:639;s:52:"PHPStan\Rules\Variables\ParameterOutAssignedTypeRule";i:640;s:56:"PHPStan\Rules\Variables\ParameterOutExecutionEndTypeRule";i:641;s:46:"PHPStan\Rules\Classes\ImpossibleInstanceOfRule";i:642;s:56:"PHPStan\Rules\Comparison\BooleanAndConstantConditionRule";i:643;s:55:"PHPStan\Rules\Comparison\BooleanOrConstantConditionRule";i:644;s:56:"PHPStan\Rules\Comparison\BooleanNotConstantConditionRule";i:645;s:31:"PHPStan\Rules\DeadCode\NoopRule";i:646;s:72:"PHPStan\Rules\DeadCode\CallToConstructorStatementWithoutImpurePointsRule";i:647;s:62:"PHPStan\Rules\DeadCode\ConstructorWithoutImpurePointsCollector";i:648;s:28:"PHPStan\Collectors\Collector";i:649;s:47:"PHPStan\Rules\DeadCode\PossiblyPureNewCollector";i:650;s:69:"PHPStan\Rules\DeadCode\CallToFunctionStatementWithoutImpurePointsRule";i:651;s:59:"PHPStan\Rules\DeadCode\FunctionWithoutImpurePointsCollector";i:652;s:52:"PHPStan\Rules\DeadCode\PossiblyPureFuncCallCollector";i:653;s:67:"PHPStan\Rules\DeadCode\CallToMethodStatementWithoutImpurePointsRule";i:654;s:57:"PHPStan\Rules\DeadCode\MethodWithoutImpurePointsCollector";i:655;s:54:"PHPStan\Rules\DeadCode\PossiblyPureMethodCallCollector";i:656;s:73:"PHPStan\Rules\DeadCode\CallToStaticMethodStatementWithoutImpurePointsRule";i:657;s:54:"PHPStan\Rules\DeadCode\PossiblyPureStaticCallCollector";i:658;s:48:"PHPStan\Rules\DeadCode\UnusedPrivatePropertyRule";i:659;s:57:"PHPStan\Rules\Comparison\DoWhileLoopConstantConditionRule";i:660;s:52:"PHPStan\Rules\Comparison\ElseIfConstantConditionRule";i:661;s:48:"PHPStan\Rules\Comparison\IfConstantConditionRule";i:662;s:60:"PHPStan\Rules\Comparison\ImpossibleCheckTypeFunctionCallRule";i:663;s:58:"PHPStan\Rules\Comparison\ImpossibleCheckTypeMethodCallRule";i:664;s:64:"PHPStan\Rules\Comparison\ImpossibleCheckTypeStaticMethodCallRule";i:665;s:56:"PHPStan\Rules\Comparison\LogicalXorConstantConditionRule";i:666;s:37:"PHPStan\Rules\DeadCode\BetterNoopRule";i:667;s:44:"PHPStan\Rules\Comparison\MatchExpressionRule";i:668;s:71:"PHPStan\Rules\Comparison\NumberComparisonOperatorsConstantConditionRule";i:669;s:61:"PHPStan\Rules\Comparison\StrictComparisonOfDifferentTypesRule";i:670;s:52:"PHPStan\Rules\Comparison\ConstantLooseComparisonRule";i:671;s:61:"PHPStan\Rules\Comparison\TernaryOperatorConstantConditionRule";i:672;s:50:"PHPStan\Rules\Comparison\UnreachableIfBranchesRule";i:673;s:57:"PHPStan\Rules\Comparison\UnreachableTernaryElseBranchRule";i:674;s:58:"PHPStan\Rules\Comparison\WhileLoopAlwaysFalseConditionRule";i:675;s:57:"PHPStan\Rules\Comparison\WhileLoopAlwaysTrueConditionRule";i:676;s:70:"PHPStan\Rules\Methods\CallToConstructorStatementWithoutSideEffectsRule";i:677;s:62:"PHPStan\Rules\TooWideTypehints\TooWideMethodReturnTypehintRule";i:678;s:50:"PHPStan\Rules\Properties\NullsafePropertyFetchRule";i:679;s:46:"PHPStan\Rules\Traits\TraitDeclarationCollector";i:680;s:38:"PHPStan\Rules\Traits\TraitUseCollector";i:681;s:41:"PHPStan\Rules\Traits\NotAnalysedTraitRule";i:682;s:55:"PHPStan\Rules\Exceptions\CatchWithUnthrownExceptionRule";i:683;s:66:"PHPStan\Rules\TooWideTypehints\TooWideFunctionParameterOutTypeRule";i:684;s:64:"PHPStan\Rules\TooWideTypehints\TooWideMethodParameterOutTypeRule";i:685;s:54:"PHPStan\Rules\TooWideTypehints\TooWidePropertyTypeRule";i:686;s:47:"PHPStan\Rules\Functions\RandomIntParametersRule";i:687;s:39:"PHPStan\Rules\Functions\ArrayFilterRule";i:688;s:39:"PHPStan\Rules\Functions\ArrayValuesRule";i:689;s:40:"PHPStan\Rules\Functions\CallUserFuncRule";i:690;s:43:"PHPStan\Rules\Functions\ImplodeFunctionRule";i:691;s:53:"PHPStan\Rules\Functions\ParameterCastableToStringRule";i:692;s:60:"PHPStan\Rules\Functions\ImplodeParameterCastableToStringRule";i:693;s:57:"PHPStan\Rules\Functions\SortParameterCastableToStringRule";i:694;s:60:"PHPStan\Rules\Functions\MissingFunctionParameterTypehintRule";i:695;s:56:"PHPStan\Rules\Methods\MissingMethodParameterTypehintRule";i:696;s:50:"PHPStan\Rules\Methods\MissingMethodSelfOutTypeRule";i:697;s:56:"Larastan\Larastan\Methods\RelationForwardsCallsExtension";i:698;s:53:"Larastan\Larastan\Methods\ModelForwardsCallsExtension";i:699;s:63:"Larastan\Larastan\Methods\EloquentBuilderForwardsCallsExtension";i:700;s:54:"Larastan\Larastan\Methods\HigherOrderTapProxyExtension";i:701;s:61:"Larastan\Larastan\Methods\HigherOrderCollectionProxyExtension";i:702;s:64:"Larastan\Larastan\Methods\StorageMethodsClassReflectionExtension";i:703;s:35:"Larastan\Larastan\Methods\Extension";i:704;s:69:"Larastan\Larastan\Methods\ModelFactoryMethodsClassReflectionExtension";i:705;s:73:"Larastan\Larastan\Methods\RedirectResponseMethodsClassReflectionExtension";i:706;s:62:"Larastan\Larastan\Methods\MacroMethodsClassReflectionExtension";i:707;s:39:"Larastan\Larastan\Concerns\HasContainer";i:708;s:65:"Larastan\Larastan\Methods\ViewWithMethodsClassReflectionExtension";i:709;s:51:"Larastan\Larastan\Properties\ModelAccessorExtension";i:710;s:51:"Larastan\Larastan\Properties\ModelPropertyExtension";i:711;s:72:"Larastan\Larastan\Properties\HigherOrderCollectionProxyPropertyExtension";i:712;s:64:"Larastan\Larastan\Types\RelationDynamicMethodReturnTypeExtension";i:713;s:70:"Larastan\Larastan\Types\ModelRelationsDynamicMethodReturnTypeExtension";i:714;s:58:"Larastan\Larastan\ReturnTypes\HigherOrderTapProxyExtension";i:715;s:82:"Larastan\Larastan\ReturnTypes\ContainerArrayAccessDynamicMethodReturnTypeExtension";i:716;s:52:"Larastan\Larastan\Properties\ModelRelationsExtension";i:717;s:71:"Larastan\Larastan\ReturnTypes\ModelOnlyDynamicMethodReturnTypeExtension";i:718;s:80:"Larastan\Larastan\ReturnTypes\ModelFactoryDynamicStaticMethodReturnTypeExtension";i:719;s:73:"Larastan\Larastan\ReturnTypes\ModelDynamicStaticMethodReturnTypeExtension";i:720;s:63:"Larastan\Larastan\ReturnTypes\AppMakeDynamicReturnTypeExtension";i:721;s:43:"Larastan\Larastan\ReturnTypes\AuthExtension";i:722;s:41:"Larastan\Larastan\Concerns\LoadsAuthModel";i:723;s:73:"Larastan\Larastan\ReturnTypes\GuardDynamicStaticMethodReturnTypeExtension";i:724;s:50:"Larastan\Larastan\ReturnTypes\AuthManagerExtension";i:725;s:43:"Larastan\Larastan\ReturnTypes\DateExtension";i:726;s:44:"Larastan\Larastan\ReturnTypes\GuardExtension";i:727;s:50:"Larastan\Larastan\ReturnTypes\RequestFileExtension";i:728;s:51:"Larastan\Larastan\ReturnTypes\RequestRouteExtension";i:729;s:50:"Larastan\Larastan\ReturnTypes\RequestUserExtension";i:730;s:54:"Larastan\Larastan\ReturnTypes\EloquentBuilderExtension";i:731;s:57:"Larastan\Larastan\ReturnTypes\RelationCollectionExtension";i:732;s:48:"Larastan\Larastan\ReturnTypes\ModelFindExtension";i:733;s:55:"Larastan\Larastan\ReturnTypes\BuilderModelFindExtension";i:734;s:47:"Larastan\Larastan\ReturnTypes\TestCaseExtension";i:735;s:42:"Larastan\Larastan\Support\CollectionHelper";i:736;s:51:"Larastan\Larastan\ReturnTypes\Helpers\AuthExtension";i:737;s:54:"Larastan\Larastan\ReturnTypes\Helpers\CollectExtension";i:738;s:58:"Larastan\Larastan\ReturnTypes\Helpers\NowAndTodayExtension";i:739;s:55:"Larastan\Larastan\ReturnTypes\Helpers\ResponseExtension";i:740;s:56:"Larastan\Larastan\ReturnTypes\Helpers\ValidatorExtension";i:741;s:78:"Larastan\Larastan\ReturnTypes\CollectionFilterRejectDynamicReturnTypeExtension";i:742;s:78:"Larastan\Larastan\ReturnTypes\CollectionWhereNotNullDynamicReturnTypeExtension";i:743;s:75:"Larastan\Larastan\ReturnTypes\NewModelQueryDynamicMethodReturnTypeExtension";i:744;s:69:"Larastan\Larastan\ReturnTypes\FactoryDynamicMethodReturnTypeExtension";i:745;s:62:"Larastan\Larastan\Types\AbortIfFunctionTypeSpecifyingExtension";i:746;s:50:"Larastan\Larastan\ReturnTypes\Helpers\AppExtension";i:747;s:52:"Larastan\Larastan\ReturnTypes\Helpers\ValueExtension";i:748;s:50:"Larastan\Larastan\ReturnTypes\Helpers\StrExtension";i:749;s:50:"Larastan\Larastan\ReturnTypes\Helpers\TapExtension";i:750;s:75:"Larastan\Larastan\ReturnTypes\StorageDynamicStaticMethodReturnTypeExtension";i:751;s:74:"Larastan\Larastan\Types\GenericEloquentCollectionTypeNodeResolverExtension";i:752;s:40:"PHPStan\PhpDoc\TypeNodeResolverExtension";i:753;s:59:"Larastan\Larastan\Types\ViewStringTypeNodeResolverExtension";i:754;s:47:"Larastan\Larastan\Rules\OctaneCompatibilityRule";i:755;s:53:"Larastan\Larastan\Rules\NoEnvCallsOutsideOfConfigRule";i:756;s:39:"Larastan\Larastan\Rules\NoModelMakeRule";i:757;s:55:"Larastan\Larastan\Rules\NoUnnecessaryCollectionCallRule";i:758;s:40:"Larastan\Larastan\Rules\ModelAppendsRule";i:759;s:71:"Larastan\Larastan\Types\GenericEloquentBuilderTypeNodeResolverExtension";i:760;s:63:"Larastan\Larastan\ReturnTypes\AppEnvironmentReturnTypeExtension";i:761;s:69:"Larastan\Larastan\ReturnTypes\AppFacadeEnvironmentReturnTypeExtension";i:762;s:76:"Larastan\Larastan\Types\ModelProperty\ModelPropertyTypeNodeResolverExtension";i:763;s:44:"Larastan\Larastan\Types\RelationParserHelper";i:764;s:44:"Larastan\Larastan\Properties\MigrationHelper";i:765;s:52:"Larastan\Larastan\Properties\SquashedMigrationHelper";i:766;s:44:"Larastan\Larastan\Properties\ModelCastHelper";i:767;s:48:"Larastan\Larastan\Properties\ModelPropertyHelper";i:768;s:39:"Larastan\Larastan\Rules\ModelRuleHelper";i:769;s:39:"Larastan\Larastan\Methods\BuilderHelper";i:770;s:45:"Larastan\Larastan\Rules\RelationExistenceRule";i:771;s:84:"Larastan\Larastan\Rules\CheckDispatchArgumentTypesCompatibleWithClassConstructorRule";i:772;s:67:"Larastan\Larastan\Properties\Schema\MySqlDataTypeToPhpTypeConverter";i:773;s:44:"Larastan\Larastan\LarastanStubFilesExtension";i:774;s:39:"Larastan\Larastan\Rules\UnusedViewsRule";i:775;s:54:"Larastan\Larastan\Collectors\UsedViewFunctionCollector";i:776;s:51:"Larastan\Larastan\Collectors\UsedEmailViewCollector";i:777;s:50:"Larastan\Larastan\Collectors\UsedViewMakeCollector";i:778;s:56:"Larastan\Larastan\Collectors\UsedViewFacadeMakeCollector";i:779;s:57:"Larastan\Larastan\Collectors\UsedRouteFacadeViewCollector";i:780;s:59:"Larastan\Larastan\Collectors\UsedViewInAnotherViewCollector";i:781;s:40:"Larastan\Larastan\Support\ViewFileHelper";i:782;s:71:"Larastan\Larastan\ReturnTypes\ApplicationMakeDynamicReturnTypeExtension";i:783;s:69:"Larastan\Larastan\ReturnTypes\ContainerMakeDynamicReturnTypeExtension";i:784;s:79:"Larastan\Larastan\ReturnTypes\ConsoleCommand\ArgumentDynamicReturnTypeExtension";i:785;s:82:"Larastan\Larastan\ReturnTypes\ConsoleCommand\HasArgumentDynamicReturnTypeExtension";i:786;s:77:"Larastan\Larastan\ReturnTypes\ConsoleCommand\OptionDynamicReturnTypeExtension";i:787;s:80:"Larastan\Larastan\ReturnTypes\ConsoleCommand\HasOptionDynamicReturnTypeExtension";i:788;s:62:"Larastan\Larastan\ReturnTypes\TranslatorGetReturnTypeExtension";i:789;s:56:"Larastan\Larastan\ReturnTypes\LangGetReturnTypeExtension";i:790;s:60:"Larastan\Larastan\ReturnTypes\TransHelperReturnTypeExtension";i:791;s:71:"Larastan\Larastan\ReturnTypes\DoubleUnderscoreHelperReturnTypeExtension";i:792;s:43:"Larastan\Larastan\ReturnTypes\AppMakeHelper";i:793;s:53:"Larastan\Larastan\Internal\ConsoleApplicationResolver";i:794;s:51:"Larastan\Larastan\Internal\ConsoleApplicationHelper";i:795;s:58:"Larastan\Larastan\Support\HigherOrderCollectionProxyHelper";i:796;s:86:"Larastan\Larastan\ReturnTypes\Helpers\ConfigFunctionDynamicFunctionReturnTypeExtension";i:797;s:71:"Larastan\Larastan\ReturnTypes\ConfigGetDynamicMethodReturnTypeExtension";i:798;s:38:"Larastan\Larastan\Support\ConfigParser";i:799;s:83:"Larastan\Larastan\ReturnTypes\Helpers\EnvFunctionDynamicFunctionReturnTypeExtension";i:800;s:77:"Larastan\Larastan\ReturnTypes\FormRequestSafeDynamicMethodReturnTypeExtension";i:801;s:37:"_PHPStan_2d0955352\Nette\DI\Container";i:802;s:36:"_PHPStan_2d0955352\Nette\SmartObject";i:803;s:22:"PHPStan\Php\PhpVersion";i:804;s:29:"PHPStan\Php\PhpVersionFactory";i:805;s:43:"PHPStan\PhpDocParser\Parser\ConstExprParser";i:806;s:56:"PHPStan\PhpDoc\TypeNodeResolverExtensionRegistryProvider";i:807;s:33:"PHPStan\Analyser\ConstantResolver";i:808;s:47:"PHPStan\Analyser\ResultCache\ResultCacheManager";i:809;s:54:"PHPStan\Analyser\ResultCache\ResultCacheManagerFactory";i:810;s:27:"PHPStan\Collectors\Registry";i:811;s:79:"PHPStan\DependencyInjection\Reflection\ClassReflectionExtensionRegistryProvider";i:812;s:75:"PHPStan\DependencyInjection\Type\DynamicReturnTypeExtensionRegistryProvider";i:813;s:66:"PHPStan\DependencyInjection\Type\ParameterOutTypeExtensionProvider";i:814;s:80:"PHPStan\DependencyInjection\Type\ExpressionTypeResolverExtensionRegistryProvider";i:815;s:80:"PHPStan\DependencyInjection\Type\OperatorTypeSpecifyingExtensionRegistryProvider";i:816;s:66:"PHPStan\DependencyInjection\Type\DynamicThrowTypeExtensionProvider";i:817;s:70:"PHPStan\DependencyInjection\Type\ParameterClosureTypeExtensionProvider";i:818;s:25:"PHPStan\File\FileExcluder";i:819;s:35:"PHPStan\File\FileExcluderRawFactory";i:820;s:44:"PHPStan\Reflection\Php\PhpFunctionReflection";i:821;s:37:"PHPStan\Reflection\FunctionReflection";i:822;s:44:"PHPStan\Reflection\FunctionReflectionFactory";i:823;s:79:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedPsrAutoloaderLocator";i:824;s:57:"PHPStan\BetterReflection\SourceLocator\Type\SourceLocator";i:825;s:86:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedPsrAutoloaderLocatorFactory";i:826;s:82:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedSingleFileSourceLocator";i:827;s:89:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedSingleFileSourceLocatorFactory";i:828;s:42:"PHPStan\Reflection\Php\PhpMethodReflection";i:829;s:43:"PHPStan\Reflection\ExtendedMethodReflection";i:830;s:40:"PHPStan\Reflection\ClassMemberReflection";i:831;s:35:"PHPStan\Reflection\MethodReflection";i:832;s:49:"PHPStan\Reflection\Php\PhpMethodReflectionFactory";i:833;s:64:"PHPStan\Reflection\ReflectionProvider\ReflectionProviderProvider";i:834;s:30:"PHPStan\Type\TypeAliasResolver";i:835;s:38:"PHPStan\Type\TypeAliasResolverProvider";i:836;s:30:"PHPStan\Analyser\TypeSpecifier";i:837;s:21:"PHPStan\Broker\Broker";i:838;s:15:"PhpParser\Lexer";i:839;s:67:"PHPStan\Reflection\BetterReflection\BetterReflectionProviderFactory";i:840;s:25:"PhpParser\Lexer\Emulative";}i:4;a:539:{i:0;s:71:"PHPStan\Reflection\ReflectionProvider\ReflectionProviderFactory::create";i:1;s:91:"PHPStan\Reflection\BetterReflection\SourceStubber\PhpStormStubsSourceStubberFactory::create";i:2;s:88:"PHPStan\Reflection\BetterReflection\SourceStubber\ReflectionSourceStubberFactory::create";i:3;s:47:"PHPStan\Rules\Debug\DebugScopeRule::__construct";i:4;s:51:"PHPStan\Rules\Debug\DumpPhpDocTypeRule::__construct";i:5;s:45:"PHPStan\Rules\Debug\DumpTypeRule::__construct";i:6;s:47:"PHPStan\Rules\Debug\FileAssertRule::__construct";i:7;s:51:"PHPStan\Rules\Api\ApiInstantiationRule::__construct";i:8;s:50:"PHPStan\Rules\Api\ApiClassExtendsRule::__construct";i:9;s:53:"PHPStan\Rules\Api\ApiClassImplementsRule::__construct";i:10;s:54:"PHPStan\Rules\Api\ApiInterfaceExtendsRule::__construct";i:11;s:48:"PHPStan\Rules\Api\ApiMethodCallRule::__construct";i:12;s:48:"PHPStan\Rules\Api\ApiStaticCallRule::__construct";i:13;s:46:"PHPStan\Rules\Api\ApiTraitUseRule::__construct";i:14;s:50:"PHPStan\Rules\Api\GetTemplateTypeRule::__construct";i:15;s:68:"PHPStan\Rules\Api\PhpStanNamespaceIn3rdPartyPackageRule::__construct";i:16;s:66:"PHPStan\Rules\Arrays\DuplicateKeysInLiteralArraysRule::__construct";i:17;s:45:"PHPStan\Rules\Cast\UnsetCastRule::__construct";i:18;s:54:"PHPStan\Rules\Classes\ClassAttributesRule::__construct";i:19;s:62:"PHPStan\Rules\Classes\ClassConstantAttributesRule::__construct";i:20;s:52:"PHPStan\Rules\Classes\ClassConstantRule::__construct";i:21;s:71:"PHPStan\Rules\Classes\ExistingClassesInClassImplementsRule::__construct";i:22;s:70:"PHPStan\Rules\Classes\ExistingClassesInEnumImplementsRule::__construct";i:23;s:72:"PHPStan\Rules\Classes\ExistingClassesInInterfaceExtendsRule::__construct";i:24;s:62:"PHPStan\Rules\Classes\ExistingClassInTraitUseRule::__construct";i:25;s:52:"PHPStan\Rules\Classes\InstantiationRule::__construct";i:26;s:64:"PHPStan\Rules\Classes\InvalidPromotedPropertiesRule::__construct";i:27;s:55:"PHPStan\Rules\Classes\LocalTypeAliasesRule::__construct";i:28;s:60:"PHPStan\Rules\Classes\LocalTypeTraitAliasesRule::__construct";i:29;s:52:"PHPStan\Rules\Classes\ReadOnlyClassRule::__construct";i:30;s:66:"PHPStan\Rules\Constants\DynamicClassConstantFetchRule::__construct";i:31;s:54:"PHPStan\Rules\Constants\FinalConstantRule::__construct";i:32;s:65:"PHPStan\Rules\Constants\NativeTypedClassConstantRule::__construct";i:33;s:59:"PHPStan\Rules\EnumCases\EnumCaseAttributesRule::__construct";i:34;s:59:"PHPStan\Rules\Exceptions\NoncapturingCatchRule::__construct";i:35;s:57:"PHPStan\Rules\Exceptions\ThrowExpressionRule::__construct";i:36;s:64:"PHPStan\Rules\Functions\ArrowFunctionAttributesRule::__construct";i:37;s:73:"PHPStan\Rules\Functions\ArrowFunctionReturnNullsafeByRefRule::__construct";i:38;s:58:"PHPStan\Rules\Functions\ClosureAttributesRule::__construct";i:39;s:57:"PHPStan\Rules\Functions\DefineParametersRule::__construct";i:40;s:80:"PHPStan\Rules\Functions\ExistingClassesInArrowFunctionTypehintsRule::__construct";i:41;s:65:"PHPStan\Rules\Functions\CallToFunctionParametersRule::__construct";i:42;s:74:"PHPStan\Rules\Functions\ExistingClassesInClosureTypehintsRule::__construct";i:43;s:67:"PHPStan\Rules\Functions\ExistingClassesInTypehintsRule::__construct";i:44;s:59:"PHPStan\Rules\Functions\FunctionAttributesRule::__construct";i:45;s:56:"PHPStan\Rules\Functions\ParamAttributesRule::__construct";i:46;s:57:"PHPStan\Rules\Functions\PrintfParametersRule::__construct";i:47;s:60:"PHPStan\Rules\Functions\ReturnNullsafeByRefRule::__construct";i:48;s:58:"PHPStan\Rules\Keywords\DeclareStrictTypesRule::__construct";i:49;s:50:"PHPStan\Rules\Methods\CallMethodsRule::__construct";i:50;s:56:"PHPStan\Rules\Methods\CallStaticMethodsRule::__construct";i:51;s:65:"PHPStan\Rules\Methods\ExistingClassesInTypehintsRule::__construct";i:52;s:57:"PHPStan\Rules\Methods\FinalPrivateMethodRule::__construct";i:53;s:53:"PHPStan\Rules\Methods\MethodCallableRule::__construct";i:54;s:55:"PHPStan\Rules\Methods\MethodAttributesRule::__construct";i:55;s:59:"PHPStan\Rules\Methods\StaticMethodCallableRule::__construct";i:56;s:57:"PHPStan\Rules\Operators\InvalidAssignVarRule::__construct";i:57;s:66:"PHPStan\Rules\Properties\AccessPropertiesInAssignRule::__construct";i:58;s:72:"PHPStan\Rules\Properties\AccessStaticPropertiesInAssignRule::__construct";i:59;s:71:"PHPStan\Rules\Properties\MissingReadOnlyPropertyAssignRule::__construct";i:60;s:60:"PHPStan\Rules\Properties\PropertyAttributesRule::__construct";i:61;s:58:"PHPStan\Rules\Properties\ReadOnlyPropertyRule::__construct";i:62;s:63:"PHPStan\Rules\Traits\ConflictingTraitConstantsRule::__construct";i:63;s:55:"PHPStan\Rules\Traits\ConstantsInTraitsRule::__construct";i:64;s:66:"PHPStan\Rules\Classes\UnusedConstructorParametersRule::__construct";i:65;s:58:"PHPStan\Rules\Functions\UnusedClosureUsesRule::__construct";i:66;s:46:"PHPStan\Rules\Variables\EmptyRule::__construct";i:67;s:46:"PHPStan\Rules\Variables\IssetRule::__construct";i:68;s:53:"PHPStan\Rules\Variables\NullCoalesceRule::__construct";i:69;s:40:"PHPStan\Rules\Cast\EchoRule::__construct";i:70;s:47:"PHPStan\Rules\Cast\InvalidCastRule::__construct";i:71;s:63:"PHPStan\Rules\Cast\InvalidPartOfEncapsedStringRule::__construct";i:72;s:41:"PHPStan\Rules\Cast\PrintRule::__construct";i:73;s:54:"PHPStan\Rules\Generics\ClassAncestorsRule::__construct";i:74;s:57:"PHPStan\Rules\Generics\ClassTemplateTypeRule::__construct";i:75;s:53:"PHPStan\Rules\Generics\EnumAncestorsRule::__construct";i:76;s:60:"PHPStan\Rules\Generics\FunctionTemplateTypeRule::__construct";i:77;s:65:"PHPStan\Rules\Generics\FunctionSignatureVarianceRule::__construct";i:78;s:58:"PHPStan\Rules\Generics\InterfaceAncestorsRule::__construct";i:79;s:61:"PHPStan\Rules\Generics\InterfaceTemplateTypeRule::__construct";i:80;s:58:"PHPStan\Rules\Generics\MethodTemplateTypeRule::__construct";i:81;s:61:"PHPStan\Rules\Generics\MethodTagTemplateTypeRule::__construct";i:82;s:63:"PHPStan\Rules\Generics\MethodSignatureVarianceRule::__construct";i:83;s:57:"PHPStan\Rules\Generics\TraitTemplateTypeRule::__construct";i:84;s:50:"PHPStan\Rules\Generics\UsedTraitsRule::__construct";i:85;s:67:"PHPStan\Rules\Operators\InvalidComparisonOperationRule::__construct";i:86;s:67:"PHPStan\Rules\PhpDoc\FunctionConditionalReturnTypeRule::__construct";i:87;s:65:"PHPStan\Rules\PhpDoc\MethodConditionalReturnTypeRule::__construct";i:88;s:52:"PHPStan\Rules\PhpDoc\FunctionAssertRule::__construct";i:89;s:50:"PHPStan\Rules\PhpDoc\MethodAssertRule::__construct";i:90;s:61:"PHPStan\Rules\PhpDoc\IncompatibleSelfOutTypeRule::__construct";i:91;s:73:"PHPStan\Rules\PhpDoc\IncompatibleClassConstantPhpDocTypeRule::__construct";i:92;s:60:"PHPStan\Rules\PhpDoc\IncompatiblePhpDocTypeRule::__construct";i:93;s:68:"PHPStan\Rules\PhpDoc\IncompatiblePropertyPhpDocTypeRule::__construct";i:94;s:62:"PHPStan\Rules\PhpDoc\InvalidThrowsPhpDocValueRule::__construct";i:95;s:81:"PHPStan\Rules\PhpDoc\IncompatibleParamImmediatelyInvokedCallableRule::__construct";i:96;s:67:"PHPStan\Rules\PhpDoc\RequireExtendsDefinitionClassRule::__construct";i:97;s:67:"PHPStan\Rules\PhpDoc\RequireExtendsDefinitionTraitRule::__construct";i:98;s:56:"PHPStan\Rules\Arrays\ArrayDestructuringRule::__construct";i:99;s:55:"PHPStan\Rules\Arrays\IterableInForeachRule::__construct";i:100;s:60:"PHPStan\Rules\Arrays\OffsetAccessAssignmentRule::__construct";i:101;s:58:"PHPStan\Rules\Arrays\OffsetAccessAssignOpRule::__construct";i:102;s:65:"PHPStan\Rules\Arrays\OffsetAccessValueAssignmentRule::__construct";i:103;s:59:"PHPStan\Rules\Arrays\UnpackIterableInArrayRule::__construct";i:104;s:55:"PHPStan\Rules\Exceptions\ThrowExprTypeRule::__construct";i:105;s:64:"PHPStan\Rules\Functions\ArrowFunctionReturnTypeRule::__construct";i:106;s:58:"PHPStan\Rules\Functions\ClosureReturnTypeRule::__construct";i:107;s:51:"PHPStan\Rules\Functions\ReturnTypeRule::__construct";i:108;s:51:"PHPStan\Rules\Generators\YieldTypeRule::__construct";i:109;s:49:"PHPStan\Rules\Methods\ReturnTypeRule::__construct";i:110;s:79:"PHPStan\Rules\Properties\DefaultValueTypesAssignedToPropertiesRule::__construct";i:111;s:64:"PHPStan\Rules\Properties\ReadOnlyPropertyAssignRule::__construct";i:112;s:67:"PHPStan\Rules\Properties\ReadOnlyPropertyAssignRefRule::__construct";i:113;s:67:"PHPStan\Rules\Properties\TypesAssignedToPropertiesRule::__construct";i:114;s:50:"PHPStan\Rules\Variables\ThrowTypeRule::__construct";i:115;s:56:"PHPStan\Rules\Variables\VariableCloningRule::__construct";i:116;s:61:"PHPStan\Rules\DeadCode\UnusedPrivateConstantRule::__construct";i:117;s:59:"PHPStan\Rules\DeadCode\UnusedPrivateMethodRule::__construct";i:118;s:82:"PHPStan\Rules\Functions\CallToFunctionStatementWithoutSideEffectsRule::__construct";i:119;s:78:"PHPStan\Rules\Methods\CallToMethodStatementWithoutSideEffectsRule::__construct";i:120;s:84:"PHPStan\Rules\Methods\CallToStaticMethodStatementWithoutSideEffectsRule::__construct";i:121;s:69:"PHPStan\Rules\Constants\MissingClassConstantTypehintRule::__construct";i:122;s:70:"PHPStan\Rules\Functions\MissingFunctionReturnTypehintRule::__construct";i:123;s:66:"PHPStan\Rules\Methods\MissingMethodReturnTypehintRule::__construct";i:124;s:65:"PHPStan\Rules\Properties\MissingPropertyTypehintRule::__construct";i:125;s:81:"Larastan\Larastan\Rules\ConsoleCommand\UndefinedArgumentOrOptionRule::__construct";i:126;s:40:"PHPStan\Parser\LexerFactory::__construct";i:127;s:47:"PhpParser\NodeVisitor\NameResolver::__construct";i:128;s:45:"PHPStan\Node\Printer\ExprPrinter::__construct";i:129;s:41:"PHPStan\Node\Printer\Printer::__construct";i:130;s:52:"PHPStan\Broker\AnonymousClassNameHelper::__construct";i:131;s:37:"PHPStan\Php\PhpVersionFactory::create";i:132;s:44:"PHPStan\Php\PhpVersionFactoryFactory::create";i:133;s:49:"PHPStan\Php\PhpVersionFactoryFactory::__construct";i:134;s:45:"PHPStan\PhpDocParser\Lexer\Lexer::__construct";i:135;s:51:"PHPStan\PhpDocParser\Parser\TypeParser::__construct";i:136;s:45:"PHPStan\PhpDoc\ConstExprParserFactory::create";i:137;s:53:"PHPStan\PhpDocParser\Parser\PhpDocParser::__construct";i:138;s:50:"PHPStan\PhpDoc\ConstExprParserFactory::__construct";i:139;s:53:"PHPStan\PhpDoc\PhpDocInheritanceResolver::__construct";i:140;s:46:"PHPStan\PhpDoc\PhpDocNodeResolver::__construct";i:141;s:48:"PHPStan\PhpDoc\PhpDocStringResolver::__construct";i:142;s:49:"PHPStan\PhpDoc\ConstExprNodeResolver::__construct";i:143;s:44:"PHPStan\PhpDoc\TypeNodeResolver::__construct";i:144;s:73:"PHPStan\PhpDoc\LazyTypeNodeResolverExtensionRegistryProvider::__construct";i:145;s:46:"PHPStan\PhpDoc\TypeStringResolver::__construct";i:146;s:41:"PHPStan\PhpDoc\StubValidator::__construct";i:147;s:55:"PHPStan\PhpDoc\CountableStubFilesExtension::__construct";i:148;s:58:"PHPStan\PhpDoc\SocketSelectStubFilesExtension::__construct";i:149;s:52:"PHPStan\PhpDoc\DefaultStubFilesProvider::__construct";i:150;s:58:"PHPStan\PhpDoc\JsonValidateStubFilesExtension::__construct";i:151;s:60:"PHPStan\PhpDoc\ReflectionEnumStubFilesExtension::__construct";i:152;s:38:"PHPStan\Analyser\Analyser::__construct";i:153;s:53:"PHPStan\Analyser\AnalyserResultFinalizer::__construct";i:154;s:42:"PHPStan\Analyser\FileAnalyser::__construct";i:155;s:55:"PHPStan\Analyser\Ignore\IgnoredErrorHelper::__construct";i:156;s:54:"PHPStan\Analyser\LazyInternalScopeFactory::__construct";i:157;s:42:"PHPStan\Analyser\ScopeFactory::__construct";i:158;s:47:"PHPStan\Analyser\NodeScopeResolver::__construct";i:159;s:48:"PHPStan\Analyser\ConstantResolverFactory::create";i:160;s:53:"PHPStan\Analyser\ConstantResolverFactory::__construct";i:161;s:60:"PHPStan\Analyser\ResultCache\ResultCacheManager::__construct";i:162;s:60:"PHPStan\Analyser\ResultCache\ResultCacheClearer::__construct";i:163;s:54:"PHPStan\Analyser\RicherScopeGetTypeHelper::__construct";i:164;s:32:"PHPStan\Cache\Cache::__construct";i:165;s:42:"PHPStan\Collectors\RegistryFactory::create";i:166;s:47:"PHPStan\Collectors\RegistryFactory::__construct";i:167;s:47:"PHPStan\Command\AnalyseApplication::__construct";i:168;s:43:"PHPStan\Command\AnalyserRunner::__construct";i:169;s:45:"PHPStan\Command\FixerApplication::__construct";i:170;s:50:"PHPStan\Dependency\DependencyResolver::__construct";i:171;s:51:"PHPStan\Dependency\ExportedNodeFetcher::__construct";i:172;s:52:"PHPStan\Dependency\ExportedNodeResolver::__construct";i:173;s:51:"PHPStan\Dependency\ExportedNodeVisitor::__construct";i:174;s:59:"PHPStan\DependencyInjection\MemoizingContainer::__construct";i:175;s:61:"PHPStan\DependencyInjection\Nette\NetteContainer::__construct";i:176;s:67:"PHPStan\DependencyInjection\DerivativeContainerFactory::__construct";i:177;s:96:"PHPStan\DependencyInjection\Reflection\LazyClassReflectionExtensionRegistryProvider::__construct";i:178;s:92:"PHPStan\DependencyInjection\Type\LazyDynamicReturnTypeExtensionRegistryProvider::__construct";i:179;s:83:"PHPStan\DependencyInjection\Type\LazyParameterOutTypeExtensionProvider::__construct";i:180;s:97:"PHPStan\DependencyInjection\Type\LazyExpressionTypeResolverExtensionRegistryProvider::__construct";i:181;s:97:"PHPStan\DependencyInjection\Type\LazyOperatorTypeSpecifyingExtensionRegistryProvider::__construct";i:182;s:83:"PHPStan\DependencyInjection\Type\LazyDynamicThrowTypeExtensionProvider::__construct";i:183;s:87:"PHPStan\DependencyInjection\Type\LazyParameterClosureTypeExtensionProvider::__construct";i:184;s:36:"PHPStan\File\FileHelper::__construct";i:185;s:45:"PHPStan\File\FileExcluderFactory::__construct";i:186;s:38:"PHPStan\File\FileExcluder::__construct";i:187;s:59:"PHPStan\File\FileExcluderFactory::createAnalyseFileExcluder";i:188;s:56:"PHPStan\File\FileExcluderFactory::createScanFileExcluder";i:189;s:36:"PHPStan\File\FileFinder::__construct";i:191;s:37:"PHPStan\File\FileMonitor::__construct";i:192;s:46:"PHPStan\Parallel\ParallelAnalyser::__construct";i:193;s:39:"PHPStan\Parallel\Scheduler::__construct";i:194;s:57:"PHPStan\Reflection\Php\PhpFunctionReflection::__construct";i:195;s:59:"PHPStan\Reflection\InitializerExprTypeResolver::__construct";i:196;s:79:"PHPStan\Reflection\BetterReflection\SourceLocator\FileNodesFetcher::__construct";i:197;s:109:"PHPStan\Reflection\BetterReflection\SourceLocator\ComposerJsonAndInstalledJsonSourceLocatorMaker::__construct";i:198;s:101:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedDirectorySourceLocatorFactory::__construct";i:199;s:104:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedDirectorySourceLocatorRepository::__construct";i:200;s:92:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedPsrAutoloaderLocator::__construct";i:201;s:95:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedSingleFileSourceLocator::__construct";i:202;s:105:"PHPStan\Reflection\BetterReflection\SourceLocator\OptimizedSingleFileSourceLocatorRepository::__construct";i:203;s:74:"PHPStan\Reflection\Mixin\MixinMethodsClassReflectionExtension::__construct";i:204;s:77:"PHPStan\Reflection\Mixin\MixinPropertiesClassReflectionExtension::__construct";i:205;s:63:"PHPStan\Reflection\Php\PhpClassReflectionExtension::__construct";i:206;s:55:"PHPStan\Reflection\Php\PhpMethodReflection::__construct";i:207;s:81:"PHPStan\Reflection\Php\UniversalObjectCratesClassReflectionExtension::__construct";i:208;s:92:"PHPStan\Reflection\PHPStan\NativeReflectionEnumReturnDynamicReturnTypeExtension::__construct";i:210;s:81:"PHPStan\Reflection\ReflectionProvider\LazyReflectionProviderProvider::__construct";i:211;s:77:"PHPStan\Reflection\SignatureMap\NativeFunctionReflectionProvider::__construct";i:212;s:63:"PHPStan\Reflection\SignatureMap\SignatureMapParser::__construct";i:213;s:73:"PHPStan\Reflection\SignatureMap\FunctionSignatureMapProvider::__construct";i:214;s:69:"PHPStan\Reflection\SignatureMap\Php8SignatureMapProvider::__construct";i:215;s:72:"PHPStan\Reflection\SignatureMap\SignatureMapProviderFactory::__construct";i:216;s:67:"PHPStan\Reflection\SignatureMap\SignatureMapProviderFactory::create";i:217;s:42:"PHPStan\Rules\AttributesCheck::__construct";i:218;s:71:"PHPStan\Rules\Arrays\NonexistentOffsetInArrayDimFetchCheck::__construct";i:219;s:41:"PHPStan\Rules\ClassNameCheck::__construct";i:220;s:52:"PHPStan\Rules\ClassCaseSensitivityCheck::__construct";i:221;s:50:"PHPStan\Rules\ClassForbiddenNameCheck::__construct";i:222;s:56:"PHPStan\Rules\Classes\LocalTypeAliasesCheck::__construct";i:223;s:49:"PHPStan\Rules\Classes\MethodTagCheck::__construct";i:224;s:45:"PHPStan\Rules\Classes\MixinCheck::__construct";i:225;s:51:"PHPStan\Rules\Classes\PropertyTagCheck::__construct";i:226;s:65:"PHPStan\Rules\Comparison\ConstantConditionRuleHelper::__construct";i:227;s:63:"PHPStan\Rules\Comparison\ImpossibleCheckTypeHelper::__construct";i:228;s:66:"PHPStan\Rules\Exceptions\DefaultExceptionTypeResolver::__construct";i:229;s:81:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInFunctionThrowsRule::__construct";i:230;s:79:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInMethodThrowsRule::__construct";i:231;s:74:"PHPStan\Rules\Exceptions\MissingCheckedExceptionInThrowsCheck::__construct";i:232;s:66:"PHPStan\Rules\Exceptions\TooWideFunctionThrowTypeRule::__construct";i:233;s:64:"PHPStan\Rules\Exceptions\TooWideMethodThrowTypeRule::__construct";i:234;s:54:"PHPStan\Rules\FunctionCallParametersCheck::__construct";i:235;s:50:"PHPStan\Rules\FunctionDefinitionCheck::__construct";i:236;s:50:"PHPStan\Rules\FunctionReturnTypeCheck::__construct";i:237;s:57:"PHPStan\Rules\ParameterCastableToStringCheck::__construct";i:238;s:57:"PHPStan\Rules\Generics\GenericAncestorsCheck::__construct";i:239;s:62:"PHPStan\Rules\Generics\MethodTagTemplateTypeCheck::__construct";i:240;s:53:"PHPStan\Rules\Generics\TemplateTypeCheck::__construct";i:241;s:49:"PHPStan\Rules\Generics\VarianceCheck::__construct";i:242;s:37:"PHPStan\Rules\IssetCheck::__construct";i:243;s:50:"PHPStan\Rules\Methods\MethodCallCheck::__construct";i:244;s:56:"PHPStan\Rules\Methods\StaticMethodCallCheck::__construct";i:245;s:54:"PHPStan\Rules\Methods\MethodSignatureRule::__construct";i:246;s:66:"PHPStan\Rules\Methods\MethodParameterComparisonHelper::__construct";i:247;s:47:"PHPStan\Rules\MissingTypehintCheck::__construct";i:248;s:82:"PHPStan\Rules\Constants\LazyAlwaysUsedClassConstantsExtensionProvider::__construct";i:249;s:72:"PHPStan\Rules\Methods\LazyAlwaysUsedMethodExtensionProvider::__construct";i:250;s:50:"PHPStan\Rules\PhpDoc\AssertRuleHelper::__construct";i:251;s:59:"PHPStan\Rules\PhpDoc\GenericCallableRuleHelper::__construct";i:252;s:54:"PHPStan\Rules\PhpDoc\VarTagTypeRuleHelper::__construct";i:253;s:78:"PHPStan\Rules\Properties\LazyReadWritePropertiesExtensionProvider::__construct";i:254;s:42:"PHPStan\Rules\RuleLevelHelper::__construct";i:255;s:56:"PHPStan\Rules\UnusedFunctionParametersCheck::__construct";i:256;s:40:"PHPStan\Type\FileTypeMapper::__construct";i:257;s:49:"PHPStan\Type\UsefulTypeAliasResolver::__construct";i:258;s:55:"PHPStan\Type\LazyTypeAliasResolverProvider::__construct";i:259;s:43:"PHPStan\Type\BitwiseFlagHelper::__construct";i:260;s:74:"PHPStan\Type\Php\ArrayIntersectKeyFunctionReturnTypeExtension::__construct";i:261;s:67:"PHPStan\Type\Php\ArrayChunkFunctionReturnTypeExtension::__construct";i:262;s:68:"PHPStan\Type\Php\ArrayColumnFunctionReturnTypeExtension::__construct";i:263;s:69:"PHPStan\Type\Php\ArrayCombineFunctionReturnTypeExtension::__construct";i:264;s:66:"PHPStan\Type\Php\ArrayFillFunctionReturnTypeExtension::__construct";i:265;s:70:"PHPStan\Type\Php\ArrayFillKeysFunctionReturnTypeExtension::__construct";i:266;s:65:"PHPStan\Type\Php\ArrayFilterFunctionReturnTypeHelper::__construct";i:267;s:68:"PHPStan\Type\Php\ArrayFilterFunctionReturnTypeExtension::__construct";i:268;s:66:"PHPStan\Type\Php\ArrayFlipFunctionReturnTypeExtension::__construct";i:269;s:66:"PHPStan\Type\Php\ArrayFindFunctionReturnTypeExtension::__construct";i:270;s:73:"PHPStan\Type\Php\ArrayKeysFunctionDynamicReturnTypeExtension::__construct";i:271;s:69:"PHPStan\Type\Php\ArrayReverseFunctionReturnTypeExtension::__construct";i:272;s:67:"PHPStan\Type\Php\ArraySliceFunctionReturnTypeExtension::__construct";i:273;s:75:"PHPStan\Type\Php\ArraySearchFunctionDynamicReturnTypeExtension::__construct";i:274;s:75:"PHPStan\Type\Php\ArrayValuesFunctionDynamicReturnTypeExtension::__construct";i:275;s:67:"PHPStan\Type\Php\BcMathStringOrNullReturnTypeExtension::__construct";i:276;s:64:"PHPStan\Type\Php\CompactFunctionReturnTypeExtension::__construct";i:277;s:65:"PHPStan\Type\Php\ConstantFunctionReturnTypeExtension::__construct";i:278;s:75:"PHPStan\Type\Php\CurlGetinfoFunctionDynamicReturnTypeExtension::__construct";i:279;s:67:"PHPStan\Type\Php\DateFormatFunctionReturnTypeExtension::__construct";i:280;s:65:"PHPStan\Type\Php\DateFormatMethodReturnTypeExtension::__construct";i:281;s:61:"PHPStan\Type\Php\DateFunctionReturnTypeExtension::__construct";i:282;s:71:"PHPStan\Type\Php\DateIntervalConstructorThrowTypeExtension::__construct";i:283;s:63:"PHPStan\Type\Php\DateTimeModifyReturnTypeExtension::__construct";i:285;s:67:"PHPStan\Type\Php\DateTimeConstructorThrowTypeExtension::__construct";i:286;s:68:"PHPStan\Type\Php\DateTimeModifyMethodThrowTypeExtension::__construct";i:287;s:65:"PHPStan\Type\Php\DateTimeSubMethodThrowTypeExtension::__construct";i:288;s:71:"PHPStan\Type\Php\DateTimeZoneConstructorThrowTypeExtension::__construct";i:289;s:71:"PHPStan\Type\Php\ExplodeFunctionDynamicReturnTypeExtension::__construct";i:290;s:60:"PHPStan\Type\Php\FilterFunctionReturnTypeHelper::__construct";i:291;s:67:"PHPStan\Type\Php\FilterInputDynamicReturnTypeExtension::__construct";i:292;s:65:"PHPStan\Type\Php\FilterVarDynamicReturnTypeExtension::__construct";i:293;s:70:"PHPStan\Type\Php\FilterVarArrayDynamicReturnTypeExtension::__construct";i:294;s:78:"PHPStan\Type\Php\GetParentClassDynamicFunctionReturnTypeExtension::__construct";i:295;s:62:"PHPStan\Type\Php\HashFunctionsReturnTypeExtension::__construct";i:296;s:71:"PHPStan\Type\Php\HighlightStringDynamicReturnTypeExtension::__construct";i:297;s:52:"PHPStan\Type\Php\JsonThrowTypeExtension::__construct";i:298;s:62:"PHPStan\Type\Php\PregMatchTypeSpecifyingExtension::__construct";i:299;s:64:"PHPStan\Type\Php\PregMatchParameterOutTypeExtension::__construct";i:300;s:69:"PHPStan\Type\Php\PregReplaceCallbackClosureTypeExtension::__construct";i:301;s:52:"PHPStan\Type\Php\RegexArrayShapeMatcher::__construct";i:302;s:48:"PHPStan\Type\Regex\RegexGroupParser::__construct";i:303;s:53:"PHPStan\Type\Regex\RegexExpressionHelper::__construct";i:304;s:77:"PHPStan\Type\Php\ReflectionFunctionConstructorThrowTypeExtension::__construct";i:305;s:75:"PHPStan\Type\Php\ReflectionMethodConstructorThrowTypeExtension::__construct";i:306;s:77:"PHPStan\Type\Php\ReflectionPropertyConstructorThrowTypeExtension::__construct";i:307;s:67:"PHPStan\Type\Php\PropertyExistsTypeSpecifyingExtension::__construct";i:308;s:63:"PHPStan\Type\Php\MinMaxFunctionReturnTypeExtension::__construct";i:309;s:72:"PHPStan\Type\Php\PathinfoFunctionDynamicReturnTypeExtension::__construct";i:310;s:65:"PHPStan\Type\Php\PregSplitDynamicReturnTypeExtension::__construct";i:311;s:60:"PHPStan\Type\Php\MbFunctionsReturnTypeExtension::__construct";i:312;s:74:"PHPStan\Type\Php\MbConvertEncodingFunctionReturnTypeExtension::__construct";i:313;s:77:"PHPStan\Type\Php\MbSubstituteCharacterDynamicReturnTypeExtension::__construct";i:314;s:65:"PHPStan\Type\Php\MbStrlenFunctionReturnTypeExtension::__construct";i:315;s:62:"PHPStan\Type\Php\SubstrDynamicReturnTypeExtension::__construct";i:316;s:68:"PHPStan\Type\Php\TriggerErrorDynamicReturnTypeExtension::__construct";i:317;s:62:"PHPStan\Type\Php\RoundFunctionReturnTypeExtension::__construct";i:318;s:68:"PHPStan\Type\Php\DefinedConstantTypeSpecifyingExtension::__construct";i:319;s:68:"PHPStan\Type\Php\IsArrayFunctionTypeSpecifyingExtension::__construct";i:320;s:71:"PHPStan\Type\Php\IsCallableFunctionTypeSpecifyingExtension::__construct";i:321;s:73:"PHPStan\Type\Php\IsSubclassOfFunctionTypeSpecifyingExtension::__construct";i:322;s:64:"PHPStan\Type\Php\IsAFunctionTypeSpecifyingExtension::__construct";i:323;s:72:"PHPStan\Type\Php\JsonThrowOnErrorDynamicReturnTypeExtension::__construct";i:324;s:79:"PHPStan\Type\Php\TypeSpecifyingFunctionsDynamicReturnTypeExtension::__construct";i:325;s:65:"PHPStan\Type\Php\StrSplitFunctionReturnTypeExtension::__construct";i:326;s:78:"PHPStan\Type\Php\ReflectionGetAttributesMethodReturnTypeExtension::__construct";i:331;s:44:"PHPStan\Type\ClosureTypeFactory::__construct";i:332;s:49:"PHPStan\Rules\Functions\PrintfHelper::__construct";i:333;s:49:"_PHPStan_2d0955352\Nette\DI\Container::getService";i:334;s:45:"PHPStan\Analyser\TypeSpecifierFactory::create";i:335;s:50:"PHPStan\Analyser\TypeSpecifierFactory::__construct";i:336;s:49:"PHPStan\File\FuzzyRelativePathHelper::__construct";i:337;s:50:"PHPStan\File\SimpleRelativePathHelper::__construct";i:338;s:59:"PHPStan\File\ParentDirectoryRelativePathHelper::__construct";i:339;s:36:"PHPStan\Broker\BrokerFactory::create";i:340;s:41:"PHPStan\Broker\BrokerFactory::__construct";i:341;s:43:"PHPStan\Cache\FileCacheStorage::__construct";i:342;s:38:"PHPStan\Parser\RichParser::__construct";i:343;s:42:"PHPStan\Parser\CleaningParser::__construct";i:344;s:40:"PHPStan\Parser\SimpleParser::__construct";i:345;s:40:"PHPStan\Parser\CachedParser::__construct";i:346;s:46:"PHPStan\Parser\PhpParserDecorator::__construct";i:347;s:35:"PHPStan\Parser\LexerFactory::create";i:348;s:37:"PhpParser\ParserAbstract::__construct";i:349;s:39:"PHPStan\Rules\LazyRegistry::__construct";i:350;s:46:"PHPStan\PhpDoc\StubPhpDocProvider::__construct";i:351;s:76:"PHPStan\Reflection\ReflectionProvider\ReflectionProviderFactory::__construct";i:353;s:80:"PHPStan\Reflection\BetterReflection\BetterReflectionSourceLocatorFactory::create";i:354;s:64:"PHPStan\BetterReflection\Reflector\DefaultReflector::__construct";i:355;s:77:"PHPStan\Reflection\BetterReflection\Reflector\MemoizingReflector::__construct";i:356;s:62:"PHPStan\BetterReflection\Reflector\ClassReflector::__construct";i:357;s:65:"PHPStan\BetterReflection\Reflector\FunctionReflector::__construct";i:358;s:65:"PHPStan\BetterReflection\Reflector\ConstantReflector::__construct";i:360;s:73:"PHPStan\Reflection\BetterReflection\BetterReflectionProvider::__construct";i:361;s:85:"PHPStan\Reflection\BetterReflection\BetterReflectionSourceLocatorFactory::__construct";i:363;s:96:"PHPStan\Reflection\BetterReflection\SourceStubber\PhpStormStubsSourceStubberFactory::__construct";i:366;s:93:"PHPStan\Reflection\BetterReflection\SourceStubber\ReflectionSourceStubberFactory::__construct";i:367;s:44:"PHPStan\Parser\LexerFactory::createEmulative";i:370;s:45:"PHPStan\Parser\PathRoutingParser::__construct";i:371;s:54:"PHPStan\Diagnose\PHPStanDiagnoseExtension::__construct";i:372;s:68:"PHPStan\Command\ErrorFormatter\CiDetectedErrorFormatter::__construct";i:373;s:63:"PHPStan\Command\ErrorFormatter\TableErrorFormatter::__construct";i:374;s:68:"PHPStan\Command\ErrorFormatter\CheckstyleErrorFormatter::__construct";i:375;s:62:"PHPStan\Command\ErrorFormatter\JsonErrorFormatter::__construct";i:376;s:63:"PHPStan\Command\ErrorFormatter\JunitErrorFormatter::__construct";i:378;s:64:"PHPStan\Command\ErrorFormatter\GitlabErrorFormatter::__construct";i:379;s:64:"PHPStan\Command\ErrorFormatter\GithubErrorFormatter::__construct";i:380;s:66:"PHPStan\Command\ErrorFormatter\TeamcityErrorFormatter::__construct";i:381;s:53:"PHPStan\Rules\Api\ApiClassConstFetchRule::__construct";i:382;s:48:"PHPStan\Rules\Api\ApiInstanceofRule::__construct";i:383;s:52:"PHPStan\Rules\Api\ApiInstanceofTypeRule::__construct";i:384;s:66:"PHPStan\Rules\Api\NodeConnectingVisitorAttributesRule::__construct";i:385;s:60:"PHPStan\Rules\Api\RuntimeReflectionFunctionRule::__construct";i:386;s:65:"PHPStan\Rules\Api\RuntimeReflectionInstantiationRule::__construct";i:387;s:66:"PHPStan\Rules\Classes\ExistingClassInClassExtendsRule::__construct";i:388;s:64:"PHPStan\Rules\Classes\ExistingClassInInstanceOfRule::__construct";i:389;s:63:"PHPStan\Rules\Classes\LocalTypeTraitUseAliasesRule::__construct";i:390;s:66:"PHPStan\Rules\Exceptions\CaughtExceptionExistenceRule::__construct";i:391;s:66:"PHPStan\Rules\Functions\CallToNonExistentFunctionRule::__construct";i:392;s:59:"PHPStan\Rules\Constants\OverridingConstantRule::__construct";i:393;s:55:"PHPStan\Rules\Methods\OverridingMethodRule::__construct";i:394;s:60:"PHPStan\Rules\Methods\ConsistentConstructorRule::__construct";i:395;s:52:"PHPStan\Rules\Missing\MissingReturnRule::__construct";i:396;s:65:"PHPStan\Rules\Namespaces\ExistingNamesInGroupUseRule::__construct";i:397;s:60:"PHPStan\Rules\Namespaces\ExistingNamesInUseRule::__construct";i:398;s:63:"PHPStan\Rules\Operators\InvalidIncDecOperationRule::__construct";i:399;s:58:"PHPStan\Rules\Properties\AccessPropertiesRule::__construct";i:400;s:64:"PHPStan\Rules\Properties\AccessStaticPropertiesRule::__construct";i:401;s:69:"PHPStan\Rules\Properties\ExistingClassesInPropertiesRule::__construct";i:402;s:57:"PHPStan\Rules\Functions\FunctionCallableRule::__construct";i:403;s:79:"PHPStan\Rules\Properties\MissingReadOnlyByPhpDocPropertyAssignRule::__construct";i:404;s:60:"PHPStan\Rules\Properties\OverridingPropertyRule::__construct";i:405;s:63:"PHPStan\Rules\Properties\UninitializedPropertyRule::__construct";i:406;s:69:"PHPStan\Rules\Properties\WritingToReadOnlyPropertiesRule::__construct";i:407;s:68:"PHPStan\Rules\Properties\ReadingWriteOnlyPropertiesRule::__construct";i:408;s:57:"PHPStan\Rules\Variables\CompactVariablesRule::__construct";i:409;s:56:"PHPStan\Rules\Variables\DefinedVariableRule::__construct";i:410;s:62:"PHPStan\Rules\Regexp\RegularExpressionPatternRule::__construct";i:411;s:50:"PHPStan\Reflection\ConstructorsHelper::__construct";i:412;s:71:"PHPStan\Rules\Methods\MissingMagicSerializationMethodsRule::__construct";i:413;s:67:"PHPStan\Rules\Functions\UselessFunctionReturnValueRule::__construct";i:414;s:62:"PHPStan\Rules\Functions\PrintfArrayParametersRule::__construct";i:415;s:62:"PHPStan\Rules\Regexp\RegularExpressionQuotingRule::__construct";i:416;s:57:"PHPStan\Rules\Keywords\RequireFileExistsRule::__construct";i:417;s:44:"PHPStan\Rules\Classes\MixinRule::__construct";i:418;s:49:"PHPStan\Rules\Classes\MixinTraitRule::__construct";i:419;s:52:"PHPStan\Rules\Classes\MixinTraitUseRule::__construct";i:420;s:48:"PHPStan\Rules\Classes\MethodTagRule::__construct";i:421;s:53:"PHPStan\Rules\Classes\MethodTagTraitRule::__construct";i:422;s:56:"PHPStan\Rules\Classes\MethodTagTraitUseRule::__construct";i:423;s:50:"PHPStan\Rules\Classes\PropertyTagRule::__construct";i:424;s:55:"PHPStan\Rules\Classes\PropertyTagTraitRule::__construct";i:425;s:58:"PHPStan\Rules\Classes\PropertyTagTraitUseRule::__construct";i:426;s:53:"PHPStan\Rules\PhpDoc\RequireExtendsCheck::__construct";i:427;s:70:"PHPStan\Rules\PhpDoc\RequireImplementsDefinitionTraitRule::__construct";i:428;s:54:"PHPStan\Rules\Functions\CallCallablesRule::__construct";i:429;s:66:"PHPStan\Rules\Generics\MethodTagTemplateTypeTraitRule::__construct";i:430;s:59:"PHPStan\Rules\PhpDoc\InvalidPhpDocTagValueRule::__construct";i:431;s:61:"PHPStan\Rules\PhpDoc\InvalidPhpDocVarTagTypeRule::__construct";i:432;s:58:"PHPStan\Rules\PhpDoc\InvalidPHPStanDocTagRule::__construct";i:433;s:65:"PHPStan\Rules\PhpDoc\VarTagChangedExpressionTypeRule::__construct";i:434;s:63:"PHPStan\Rules\PhpDoc\WrongVariableNameInVarTagRule::__construct";i:435;s:56:"PHPStan\Rules\Generics\PropertyVarianceRule::__construct";i:436;s:48:"PHPStan\Rules\Pure\PureFunctionRule::__construct";i:437;s:46:"PHPStan\Rules\Pure\PureMethodRule::__construct";i:438;s:63:"PHPStan\Rules\Operators\InvalidBinaryOperationRule::__construct";i:439;s:62:"PHPStan\Rules\Operators\InvalidUnaryOperationRule::__construct";i:440;s:63:"PHPStan\Rules\Arrays\InvalidKeyInArrayDimFetchRule::__construct";i:441;s:59:"PHPStan\Rules\Arrays\InvalidKeyInArrayItemRule::__construct";i:442;s:70:"PHPStan\Rules\Arrays\NonexistentOffsetInArrayDimFetchRule::__construct";i:443;s:82:"PHPStan\Rules\Exceptions\ThrowsVoidFunctionWithExplicitThrowPointRule::__construct";i:444;s:80:"PHPStan\Rules\Exceptions\ThrowsVoidMethodWithExplicitThrowPointRule::__construct";i:445;s:55:"PHPStan\Rules\Generators\YieldFromTypeRule::__construct";i:446;s:58:"PHPStan\Rules\Generators\YieldInGeneratorRule::__construct";i:447;s:52:"PHPStan\Rules\Arrays\ArrayUnpackingRule::__construct";i:448;s:75:"PHPStan\Rules\Properties\ReadOnlyByPhpDocPropertyAssignRefRule::__construct";i:449;s:72:"PHPStan\Rules\Properties\ReadOnlyByPhpDocPropertyAssignRule::__construct";i:450;s:65:"PHPStan\Rules\Variables\ParameterOutAssignedTypeRule::__construct";i:451;s:69:"PHPStan\Rules\Variables\ParameterOutExecutionEndTypeRule::__construct";i:452;s:59:"PHPStan\Rules\Classes\ImpossibleInstanceOfRule::__construct";i:453;s:69:"PHPStan\Rules\Comparison\BooleanAndConstantConditionRule::__construct";i:454;s:68:"PHPStan\Rules\Comparison\BooleanOrConstantConditionRule::__construct";i:455;s:69:"PHPStan\Rules\Comparison\BooleanNotConstantConditionRule::__construct";i:456;s:44:"PHPStan\Rules\DeadCode\NoopRule::__construct";i:457;s:60:"PHPStan\Rules\DeadCode\PossiblyPureNewCollector::__construct";i:458;s:65:"PHPStan\Rules\DeadCode\PossiblyPureFuncCallCollector::__construct";i:459;s:67:"PHPStan\Rules\DeadCode\PossiblyPureMethodCallCollector::__construct";i:460;s:67:"PHPStan\Rules\DeadCode\PossiblyPureStaticCallCollector::__construct";i:461;s:61:"PHPStan\Rules\DeadCode\UnusedPrivatePropertyRule::__construct";i:462;s:70:"PHPStan\Rules\Comparison\DoWhileLoopConstantConditionRule::__construct";i:463;s:65:"PHPStan\Rules\Comparison\ElseIfConstantConditionRule::__construct";i:464;s:61:"PHPStan\Rules\Comparison\IfConstantConditionRule::__construct";i:465;s:73:"PHPStan\Rules\Comparison\ImpossibleCheckTypeFunctionCallRule::__construct";i:466;s:71:"PHPStan\Rules\Comparison\ImpossibleCheckTypeMethodCallRule::__construct";i:467;s:77:"PHPStan\Rules\Comparison\ImpossibleCheckTypeStaticMethodCallRule::__construct";i:468;s:69:"PHPStan\Rules\Comparison\LogicalXorConstantConditionRule::__construct";i:469;s:50:"PHPStan\Rules\DeadCode\BetterNoopRule::__construct";i:470;s:57:"PHPStan\Rules\Comparison\MatchExpressionRule::__construct";i:471;s:84:"PHPStan\Rules\Comparison\NumberComparisonOperatorsConstantConditionRule::__construct";i:472;s:74:"PHPStan\Rules\Comparison\StrictComparisonOfDifferentTypesRule::__construct";i:473;s:65:"PHPStan\Rules\Comparison\ConstantLooseComparisonRule::__construct";i:474;s:74:"PHPStan\Rules\Comparison\TernaryOperatorConstantConditionRule::__construct";i:475;s:63:"PHPStan\Rules\Comparison\UnreachableIfBranchesRule::__construct";i:476;s:70:"PHPStan\Rules\Comparison\UnreachableTernaryElseBranchRule::__construct";i:477;s:71:"PHPStan\Rules\Comparison\WhileLoopAlwaysFalseConditionRule::__construct";i:478;s:70:"PHPStan\Rules\Comparison\WhileLoopAlwaysTrueConditionRule::__construct";i:479;s:83:"PHPStan\Rules\Methods\CallToConstructorStatementWithoutSideEffectsRule::__construct";i:480;s:75:"PHPStan\Rules\TooWideTypehints\TooWideMethodReturnTypehintRule::__construct";i:481;s:63:"PHPStan\Rules\Properties\NullsafePropertyFetchRule::__construct";i:482;s:68:"PHPStan\Rules\Exceptions\CatchWithUnthrownExceptionRule::__construct";i:483;s:79:"PHPStan\Rules\TooWideTypehints\TooWideFunctionParameterOutTypeRule::__construct";i:484;s:77:"PHPStan\Rules\TooWideTypehints\TooWideMethodParameterOutTypeRule::__construct";i:485;s:67:"PHPStan\Rules\TooWideTypehints\TooWidePropertyTypeRule::__construct";i:486;s:60:"PHPStan\Rules\Functions\RandomIntParametersRule::__construct";i:487;s:52:"PHPStan\Rules\Functions\ArrayFilterRule::__construct";i:488;s:52:"PHPStan\Rules\Functions\ArrayValuesRule::__construct";i:489;s:53:"PHPStan\Rules\Functions\CallUserFuncRule::__construct";i:490;s:56:"PHPStan\Rules\Functions\ImplodeFunctionRule::__construct";i:491;s:66:"PHPStan\Rules\Functions\ParameterCastableToStringRule::__construct";i:492;s:73:"PHPStan\Rules\Functions\ImplodeParameterCastableToStringRule::__construct";i:493;s:70:"PHPStan\Rules\Functions\SortParameterCastableToStringRule::__construct";i:494;s:73:"PHPStan\Rules\Functions\MissingFunctionParameterTypehintRule::__construct";i:495;s:69:"PHPStan\Rules\Methods\MissingMethodParameterTypehintRule::__construct";i:496;s:63:"PHPStan\Rules\Methods\MissingMethodSelfOutTypeRule::__construct";i:497;s:69:"Larastan\Larastan\Methods\RelationForwardsCallsExtension::__construct";i:498;s:66:"Larastan\Larastan\Methods\ModelForwardsCallsExtension::__construct";i:499;s:76:"Larastan\Larastan\Methods\EloquentBuilderForwardsCallsExtension::__construct";i:500;s:74:"Larastan\Larastan\Methods\HigherOrderCollectionProxyExtension::__construct";i:501;s:77:"Larastan\Larastan\Methods\StorageMethodsClassReflectionExtension::__construct";i:502;s:48:"Larastan\Larastan\Methods\Extension::__construct";i:503;s:82:"Larastan\Larastan\Methods\ModelFactoryMethodsClassReflectionExtension::__construct";i:504;s:75:"Larastan\Larastan\Methods\MacroMethodsClassReflectionExtension::__construct";i:505;s:64:"Larastan\Larastan\Properties\ModelAccessorExtension::__construct";i:506;s:64:"Larastan\Larastan\Properties\ModelPropertyExtension::__construct";i:507;s:85:"Larastan\Larastan\Properties\HigherOrderCollectionProxyPropertyExtension::__construct";i:508;s:77:"Larastan\Larastan\Types\RelationDynamicMethodReturnTypeExtension::__construct";i:509;s:83:"Larastan\Larastan\Types\ModelRelationsDynamicMethodReturnTypeExtension::__construct";i:510;s:95:"Larastan\Larastan\ReturnTypes\ContainerArrayAccessDynamicMethodReturnTypeExtension::__construct";i:514;s:65:"Larastan\Larastan\Properties\ModelRelationsExtension::__construct";i:515;s:93:"Larastan\Larastan\ReturnTypes\ModelFactoryDynamicStaticMethodReturnTypeExtension::__construct";i:516;s:86:"Larastan\Larastan\ReturnTypes\ModelDynamicStaticMethodReturnTypeExtension::__construct";i:517;s:76:"Larastan\Larastan\ReturnTypes\AppMakeDynamicReturnTypeExtension::__construct";i:518;s:67:"Larastan\Larastan\ReturnTypes\EloquentBuilderExtension::__construct";i:519;s:70:"Larastan\Larastan\ReturnTypes\RelationCollectionExtension::__construct";i:520;s:61:"Larastan\Larastan\ReturnTypes\ModelFindExtension::__construct";i:521;s:68:"Larastan\Larastan\ReturnTypes\BuilderModelFindExtension::__construct";i:522;s:55:"Larastan\Larastan\Support\CollectionHelper::__construct";i:523;s:67:"Larastan\Larastan\ReturnTypes\Helpers\CollectExtension::__construct";i:524;s:88:"Larastan\Larastan\ReturnTypes\NewModelQueryDynamicMethodReturnTypeExtension::__construct";i:525;s:75:"Larastan\Larastan\Types\AbortIfFunctionTypeSpecifyingExtension::__construct";i:529;s:63:"Larastan\Larastan\ReturnTypes\Helpers\AppExtension::__construct";i:530;s:87:"Larastan\Larastan\Types\GenericEloquentCollectionTypeNodeResolverExtension::__construct";i:531;s:66:"Larastan\Larastan\Rules\NoEnvCallsOutsideOfConfigRule::__construct";i:532;s:52:"Larastan\Larastan\Rules\NoModelMakeRule::__construct";i:533;s:68:"Larastan\Larastan\Rules\NoUnnecessaryCollectionCallRule::__construct";i:534;s:53:"Larastan\Larastan\Rules\ModelAppendsRule::__construct";i:535;s:84:"Larastan\Larastan\Types\GenericEloquentBuilderTypeNodeResolverExtension::__construct";i:536;s:76:"Larastan\Larastan\ReturnTypes\AppEnvironmentReturnTypeExtension::__construct";i:538;s:89:"Larastan\Larastan\Types\ModelProperty\ModelPropertyTypeNodeResolverExtension::__construct";i:539;s:57:"Larastan\Larastan\Types\RelationParserHelper::__construct";i:540;s:57:"Larastan\Larastan\Properties\MigrationHelper::__construct";i:541;s:65:"Larastan\Larastan\Properties\SquashedMigrationHelper::__construct";i:542;s:57:"Larastan\Larastan\Properties\ModelCastHelper::__construct";i:543;s:61:"Larastan\Larastan\Properties\ModelPropertyHelper::__construct";i:544;s:52:"Larastan\Larastan\Methods\BuilderHelper::__construct";i:545;s:58:"Larastan\Larastan\Rules\RelationExistenceRule::__construct";i:546;s:97:"Larastan\Larastan\Rules\CheckDispatchArgumentTypesCompatibleWithClassConstructorRule::__construct";i:548;s:52:"Larastan\Larastan\Rules\UnusedViewsRule::__construct";i:549;s:72:"Larastan\Larastan\Collectors\UsedViewInAnotherViewCollector::__construct";i:550;s:53:"Larastan\Larastan\Support\ViewFileHelper::__construct";i:551;s:84:"Larastan\Larastan\ReturnTypes\ApplicationMakeDynamicReturnTypeExtension::__construct";i:552;s:82:"Larastan\Larastan\ReturnTypes\ContainerMakeDynamicReturnTypeExtension::__construct";i:553;s:92:"Larastan\Larastan\ReturnTypes\ConsoleCommand\ArgumentDynamicReturnTypeExtension::__construct";i:554;s:95:"Larastan\Larastan\ReturnTypes\ConsoleCommand\HasArgumentDynamicReturnTypeExtension::__construct";i:555;s:90:"Larastan\Larastan\ReturnTypes\ConsoleCommand\OptionDynamicReturnTypeExtension::__construct";i:556;s:93:"Larastan\Larastan\ReturnTypes\ConsoleCommand\HasOptionDynamicReturnTypeExtension::__construct";i:557;s:64:"Larastan\Larastan\Internal\ConsoleApplicationHelper::__construct";i:558;s:71:"Larastan\Larastan\Support\HigherOrderCollectionProxyHelper::__construct";i:559;s:99:"Larastan\Larastan\ReturnTypes\Helpers\ConfigFunctionDynamicFunctionReturnTypeExtension::__construct";i:560;s:84:"Larastan\Larastan\ReturnTypes\ConfigGetDynamicMethodReturnTypeExtension::__construct";i:561;s:51:"Larastan\Larastan\Support\ConfigParser::__construct";}i:5;s:32:"665a756f6cbb36cf5ab73f209a65e568";}
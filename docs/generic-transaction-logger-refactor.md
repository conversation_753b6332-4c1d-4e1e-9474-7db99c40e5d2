# 通用事务日志方法重构文档

## 重构概述

将 `SimpleTransactionLogger` 中的特定业务方法（`logLeadCreate()`, `logLeadUpdate()`, `logLeadDelete()`）重构为一个通用的 `logBusinessTransaction()` 方法，支持任意业务模块的事务日志记录。

## 重构内容

### 1. 新增通用方法

#### `logBusinessTransaction()` 方法签名

```php
public static function logBusinessTransaction(
    callable $callback,
    string $module,
    string $action,
    array $businessData,
    array $additionalContext = []
): mixed
```

#### 参数说明

- `$callback`: 事务回调函数，包含具体的业务逻辑
- `$module`: 业务模块名称（如 'Lead', 'Contact', 'User'）
- `$action`: 操作类型（如 'create', 'update', 'delete'）
- `$businessData`: 业务数据，将记录在事务日志中
- `$additionalContext`: 可选的额外上下文信息

### 2. 智能结果提取

新增 `extractResultContext()` 私有方法，根据不同模块和操作类型智能提取结果信息：

```php
private static function extractResultContext(mixed $result, string $module, string $action): array
```

**支持的提取逻辑**：
- **Create 操作**：提取 `id` 和模块特定字段（如 Lead 的 `company_name`、Contact 的 `contact_name`）
- **Update 操作**：记录更新状态和相关 ID
- **Delete 操作**：记录删除状态
- **其他操作**：通用的结果类型和实体 ID 提取

### 3. 向后兼容

保留原有的特定业务方法作为 `@deprecated` 方法，内部调用通用方法：

```php
/**
 * @deprecated 请使用 logBusinessTransaction() 方法
 */
public static function logLeadCreate(callable $callback, array $leadData): mixed
{
    return self::logBusinessTransaction(
        $callback,
        'Lead',
        'create',
        $leadData,
        ['company_name' => $leadData['company_full_name'] ?? 'unknown']
    );
}
```

## 使用示例

### 1. Lead 模块（重构后）

```php
// LeadService::createLead()
return SimpleTransactionLogger::logBusinessTransaction(
    function () use ($dto) {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务逻辑
        });
    },
    'Lead',
    'create',
    $dto->toArray(),
    ['company_name' => $dto->companyFullName]
);
```

### 2. Contact 模块示例

```php
// ContactService::createContact()
return SimpleTransactionLogger::logBusinessTransaction(
    function () use ($dto) {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 联系人创建逻辑
        });
    },
    'Contact',
    'create',
    $dto->toArray(),
    ['department' => $dto->department]
);
```

### 3. User 模块示例

```php
// UserService::updateUser()
return SimpleTransactionLogger::logBusinessTransaction(
    function () use ($id, $dto) {
        return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
            // 用户更新逻辑
        });
    },
    'User',
    'update',
    $dto->toArray(),
    ['user_id' => $id]
);
```

### 4. 自定义模块示例

```php
// OrderService::processOrder()
return SimpleTransactionLogger::logBusinessTransaction(
    function () use ($orderData) {
        return $this->transactionManager->executeInTransaction(function () use ($orderData) {
            // 订单处理逻辑
        });
    },
    'Order',
    'process',
    $orderData,
    [
        'order_type' => $orderData['type'],
        'customer_id' => $orderData['customer_id'],
        'amount' => $orderData['total_amount']
    ]
);
```

## 日志输出示例

### Lead 模块日志

```json
{
  "message": "事务开始",
  "transaction_id": "tx_7ef55204-7344-4141-93f5-a2d2e8e6696d",
  "trace_id": "generic-logger-test-1754108357",
  "module": "Lead",
  "action": "create",
  "business_data": {
    "company_full_name": "通用方法测试公司1754108357",
    "company_short_name": "通用测试",
    "region": 1,
    "status": 1
  },
  "company_name": "通用方法测试公司1754108357"
}
```

### Contact 模块日志

```json
{
  "message": "事务开始",
  "transaction_id": "tx_d0f3d5dd-453e-4df5-b0f9-e8468043866d",
  "trace_id": "generic-logger-test-1754108356",
  "module": "Contact",
  "action": "create",
  "business_data": {
    "name": "测试联系人1754108357",
    "mobile": "***********",
    "email": "<EMAIL>",
    "department": "技术部",
    "position": "工程师"
  },
  "department": "技术部"
}
```

### User 模块日志

```json
{
  "message": "事务开始",
  "transaction_id": "tx_23da296d-fcc4-4b57-852d-8f4de30300e6",
  "trace_id": "generic-logger-test-1754108356",
  "module": "User",
  "action": "update",
  "business_data": {
    "name": "更新用户名1754108357",
    "email": "<EMAIL>",
    "status": "active"
  },
  "user_id": 123
}
```

## 重构优势

### 1. **代码复用**
- 消除了重复的事务日志逻辑
- 统一的异常处理和上下文管理
- 减少了代码维护成本

### 2. **扩展性**
- 支持任意新业务模块，无需修改 SimpleTransactionLogger
- 灵活的上下文信息配置
- 智能的结果信息提取

### 3. **一致性**
- 所有模块使用相同的日志格式
- 统一的错误分类和处理
- 标准化的事务生命周期管理

### 4. **向后兼容**
- 保留原有方法作为 deprecated 方法
- 现有代码无需立即修改
- 平滑的迁移路径

## 测试验证结果

✅ **功能验证**：
- Lead 模块：19 条日志记录
- Contact 模块：2 条日志记录  
- User 模块：2 条日志记录
- Create 操作：21 条日志记录
- Update 操作：2 条日志记录

✅ **性能验证**：
- 内存使用正常（22MB）
- 事务执行效率未受影响
- 日志记录完整准确

## 迁移指南

### 1. 立即可用
新的通用方法已经可以在任何业务服务中使用

### 2. 逐步迁移
现有的 `logLeadCreate()` 等方法仍然可用，可以逐步迁移到通用方法

### 3. 新模块开发
新的业务模块直接使用 `logBusinessTransaction()` 方法，无需创建特定的日志方法

## 总结

✅ **重构完成**：成功将特定业务方法重构为通用的事务日志包装方法
✅ **功能验证**：通过多模块测试验证了通用方法的正确性
✅ **扩展性提升**：新方法支持任意业务模块和操作类型
✅ **向后兼容**：保持了现有代码的兼容性

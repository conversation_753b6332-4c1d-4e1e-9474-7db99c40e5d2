# Redis 组件实现文档

## 项目概述

本文档描述了为 Laravel CRM 项目实现的 Redis 组件，该组件提供了统一的 Redis 操作接口，支持数据缓存、队列任务数据存储等功能。

## 架构设计

### 设计原则

1. **接口抽象**：通过 `RedisServiceInterface` 定义统一的操作接口
2. **依赖注入**：支持 Laravel 的依赖注入容器
3. **异常处理**：完善的错误处理和日志记录机制
4. **数据序列化**：自动处理 JSON 序列化/反序列化
5. **连接管理**：支持多个 Redis 连接配置

### 分层架构

```
Controller/Service Layer
         ↓
RedisServiceInterface (Contract)
         ↓
RedisService (Implementation)
         ↓
Laravel Redis Manager
         ↓
Redis Server
```

## 核心组件

### 1. 接口定义 (RedisServiceInterface)

**文件位置**: `app/Contracts/RedisServiceInterface.php`

定义了 Redis 操作的标准接口，包括：
- 基础键值操作：`set()`, `get()`, `delete()`, `exists()`
- 过期时间管理：`expire()`, `ttl()`
- 批量操作：`setMultiple()`, `getMultiple()`, `deleteMultiple()`
- 计数器操作：`increment()`, `decrement()`
- 模式匹配：`keys()`
- 连接管理：`ping()`, `getConnectionInfo()`

### 2. 服务实现 (RedisService)

**文件位置**: `app/Services/RedisService.php`

**核心特性**:
- **自动序列化**: 支持复杂数据类型的 JSON 序列化
- **异常处理**: 捕获 Redis 异常并记录日志
- **连接管理**: 支持多个 Redis 连接
- **类型安全**: 完整的类型声明和 PHPDoc 注释

**关键方法实现**:
```php
// 数据序列化
protected function serialize(mixed $value): string
{
    if (is_string($value)) {
        return $value;
    }
    return json_encode($value, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE);
}

// 错误日志记录
protected function logError(string $message, array $context = []): void
{
    Log::error('[RedisService] ' . $message, array_merge($context, [
        'connection' => $this->connection,
    ]));
}
```

### 3. 服务提供者 (RedisServiceProvider)

**文件位置**: `app/Providers/RedisServiceProvider.php`

**功能**:
- 注册 Redis 服务为单例
- 支持命名连接的服务实例
- 配置文件发布

**服务绑定**:
```php
$this->app->singleton(RedisServiceInterface::class, function ($app) {
    $redisManager = $app['redis'];
    $connection = config('database.redis.default_connection', 'default');
    return new RedisService($redisManager, $connection);
});
```

## 配置管理

### Redis 连接配置

**文件位置**: `config/database.php`

使用 Laravel 标准的 Redis 配置：
```php
'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    'default' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
    'cache' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_CACHE_DB', '1'),
    ],
]
```

### Redis 服务配置

**文件位置**: `config/redis.php`

扩展配置选项：
- 默认连接设置
- 序列化选项
- 错误处理配置
- 性能参数
- 监控设置

## 使用示例

### 基础使用

```php
use App\Contracts\RedisServiceInterface;

class ExampleService
{
    public function __construct(
        private RedisServiceInterface $redis
    ) {}

    public function cacheUserData(int $userId, array $userData): void
    {
        $key = "user:profile:{$userId}";
        $this->redis->set($key, $userData, 3600); // 缓存1小时
    }

    public function getUserData(int $userId): ?array
    {
        $key = "user:profile:{$userId}";
        return $this->redis->get($key);
    }
}
```

### 业务应用示例

**文件位置**: `app/Services/LeadCacheService.php`

展示了如何在实际业务中使用 Redis 服务：
- 线索详情缓存
- 线索列表缓存
- 访问计数统计
- 热度分数计算
- 批量操作优化

## 测试工具

### 1. HTTP API 测试

**控制器**: `app/Http/Controllers/RedisTestController.php`
**路由**: `/api/test/redis/*`

提供的测试接口：
- `GET /api/test/redis/ping` - 连接测试
- `POST /api/test/redis/basic` - 基础操作测试
- `GET /api/test/redis/batch` - 批量操作测试
- `GET /api/test/redis/counter` - 计数器测试
- `GET /api/test/redis/pattern` - 模式匹配测试
- `GET /api/test/redis/comprehensive` - 综合测试

### 2. 命令行测试

**命令**: `app/Console/Commands/TestRedisServiceCommand.php`

使用方法：
```bash
# 使用默认连接测试
php artisan redis:test

# 使用指定连接测试
php artisan redis:test --connection=cache
```

## 性能特性

### 1. 批量操作优化

- 支持批量设置、获取、删除操作
- 减少网络往返次数
- 提高大量数据处理效率

### 2. 连接管理

- 复用 Laravel Redis 连接池
- 支持多个命名连接
- 自动连接恢复机制

### 3. 数据序列化

- 智能序列化策略
- 支持复杂数据类型
- JSON 格式便于调试

## 错误处理

### 异常捕获

- 捕获 `RedisException` 连接异常
- 捕获 `JsonException` 序列化异常
- 记录详细的错误日志

### 降级处理

- 连接失败时返回默认值
- 不影响主业务流程
- 提供连接状态检查

### 日志记录

```php
Log::error('[RedisService] Failed to set key', [
    'key' => $key,
    'ttl' => $ttl,
    'connection' => $this->connection,
    'error' => $e->getMessage()
]);
```

## 监控和调试

### 连接状态监控

```php
// 检查连接状态
$isConnected = $redisService->ping();

// 获取连接信息
$info = $redisService->getConnectionInfo();
```

### 性能监控

- 支持慢查询记录
- 操作统计信息
- 内存使用监控

## 部署注意事项

### 环境配置

确保以下环境变量正确配置：
```env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CACHE_DB=1
```

### 依赖要求

- PHP Redis 扩展 (phpredis 或 predis)
- Redis 服务器 5.0+
- Laravel 10.x

### 生产环境优化

1. **连接池配置**: 适当设置连接池大小
2. **内存管理**: 设置合理的过期时间
3. **监控告警**: 集成监控系统
4. **备份策略**: 配置 Redis 持久化

## 扩展方向

### 短期优化

1. **缓存标签**: 支持标签化缓存管理
2. **分布式锁**: 实现 Redis 分布式锁
3. **发布订阅**: 支持 Redis pub/sub 功能

### 长期规划

1. **集群支持**: 支持 Redis Cluster
2. **读写分离**: 支持主从读写分离
3. **自动故障转移**: 集成 Redis Sentinel
4. **性能优化**: 连接池和批量操作优化

## 总结

Redis 组件的实现遵循了项目的分层架构规范和渐进式开发原则：

1. **先实现核心功能**: 基础的键值操作和数据序列化
2. **完善错误处理**: 异常捕获和日志记录
3. **提供测试工具**: HTTP API 和命令行测试
4. **编写详细文档**: 使用指南和实现文档
5. **预留扩展空间**: 接口设计支持未来功能扩展

该组件现已可以投入生产使用，为 CRM 系统提供可靠的缓存和数据存储服务。

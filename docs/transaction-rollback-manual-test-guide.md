# 事务回滚手动测试指南

## 概述

本文档描述了如何手动测试 `LeadService::createLead` 方法中添加的事务回滚机制。

## 测试目的

验证当事务中的某个操作失败时，整个事务能够正确回滚，包括已经创建的线索记录。

## 测试代码说明

在 `app/Services/LeadService.php` 的 `createLead` 方法中，我们添加了以下测试代码：

### 测试触发条件

测试代码通过 HTTP 请求头 `X-Test-Transaction-Rollback` 来控制，支持以下测试场景：

1. **constraint-violation**: 约束违反测试
2. **table-not-found**: 表不存在测试  
3. **runtime-exception**: 运行时异常测试
4. **business-exception**: 业务异常测试
5. **relation-failure**: 关联操作失败测试

### 测试代码结构

```php
// 1. 创建线索记录
$lead = $this->leadRepository->create($dto->toArray());

// 2. 根据测试头部执行不同的失败场景
if (config('app.debug') && request()->header('X-Test-Transaction-Rollback') === 'constraint-violation') {
    // 尝试插入重复的公司名称（违反唯一约束）
    $this->leadRepository->create([...]);
}
// ... 其他测试场景
```

## 手动测试步骤

### 前置条件

1. 确保应用处于调试模式 (`APP_DEBUG=true`)
2. 确保数据库表已创建
3. 准备测试数据

### 测试方法 1: 使用 Postman 或 curl

#### 1. 约束违反测试

```bash
curl -X POST http://your-app.com/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Test-Transaction-Rollback: constraint-violation" \
  -d '{
    "company_full_name": "Test Company Constraint",
    "company_short_name": "Test Constraint",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "creator_id": 1
  }'
```

**期望结果**:
- 返回 500 错误（TransactionException）
- 数据库中不存在 "Test Company Constraint" 的线索记录

#### 2. 表不存在测试

```bash
curl -X POST http://your-app.com/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Test-Transaction-Rollback: table-not-found" \
  -d '{
    "company_full_name": "Test Company Table",
    "company_short_name": "Test Table",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "creator_id": 1
  }'
```

**期望结果**:
- 返回 500 错误（TransactionException）
- 数据库中不存在 "Test Company Table" 的线索记录

#### 3. 运行时异常测试

```bash
curl -X POST http://your-app.com/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Test-Transaction-Rollback: runtime-exception" \
  -d '{
    "company_full_name": "Test Company Runtime",
    "company_short_name": "Test Runtime",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "creator_id": 1
  }'
```

**期望结果**:
- 返回 500 错误（TransactionException）
- 错误消息包含 "模拟的系统异常"
- 数据库中不存在 "Test Company Runtime" 的线索记录

#### 4. 业务异常测试

```bash
curl -X POST http://your-app.com/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Test-Transaction-Rollback: business-exception" \
  -d '{
    "company_full_name": "Test Company Business",
    "company_short_name": "Test Business",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "creator_id": 1
  }'
```

**期望结果**:
- 返回 422 错误（BusinessException）
- 错误消息包含 "测试业务异常"
- 数据库中不存在 "Test Company Business" 的线索记录

#### 5. 关联操作失败测试

```bash
curl -X POST http://your-app.com/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Test-Transaction-Rollback: relation-failure" \
  -d '{
    "company_full_name": "Test Company Relation",
    "company_short_name": "Test Relation",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "creator_id": 1
  }'
```

**期望结果**:
- 返回 500 错误（TransactionException）
- 数据库中不存在 "Test Company Relation" 的线索记录
- 关联表中不存在相关记录

### 测试方法 2: 使用 Laravel Tinker

```bash
php artisan tinker
```

```php
// 创建 DTO
$dto = new App\DTOs\Lead\LeadCreateDTO([
    'company_full_name' => 'Test Company Tinker',
    'company_short_name' => 'Test Tinker',
    'region' => 1,
    'source' => 1,
    'industry' => 1,
    'status' => 1,
    'stage' => 1,
    'creator_id' => 1,
]);

// 设置测试头部
request()->headers->set('X-Test-Transaction-Rollback', 'runtime-exception');

// 获取服务实例
$leadService = app(App\Services\LeadService::class);

// 执行测试
try {
    $result = $leadService->createLead($dto);
    echo "创建成功: " . $result->id;
} catch (Exception $e) {
    echo "异常类型: " . get_class($e) . "\n";
    echo "异常代码: " . $e->getCode() . "\n";
    echo "异常消息: " . $e->getMessage() . "\n";
}

// 验证数据库回滚
$count = App\Models\Lead::where('company_full_name', 'Test Company Tinker')->count();
echo "数据库记录数: " . $count . " (应该为 0)\n";
```

## 验证要点

### 1. 异常类型验证

- **BusinessException**: 应该直接传播，不被包装
- **系统异常**: 应该被包装成 TransactionException
- **HTTP 状态码**: BusinessException 返回配置的状态码，系统异常返回 500

### 2. 数据库回滚验证

- 检查线索主表记录是否被回滚
- 检查关联表记录是否被回滚
- 确保没有脏数据残留

### 3. 日志记录验证

检查应用日志，应该能看到：
- 线索创建成功的日志
- 测试异常触发的警告日志
- 事务回滚的相关日志

## 测试结果分析

### 成功的测试结果

1. **异常正确抛出**: 每种测试场景都应该抛出预期的异常类型
2. **数据库完全回滚**: 所有相关表的记录都应该被回滚
3. **错误信息准确**: 异常消息应该包含预期的内容
4. **HTTP 状态码正确**: BusinessException 返回 422，系统异常返回 500

### 失败的测试结果

如果测试失败，可能的原因：
1. 事务管理器配置错误
2. 异常处理逻辑有问题
3. 数据库连接或配置问题
4. 测试代码逻辑错误

## 清理测试代码

**重要提醒**: 在生产环境部署前，必须移除或注释掉所有测试代码！

测试代码位于 `app/Services/LeadService.php` 的 `createLead` 方法中，搜索 "测试代码开始" 和 "测试代码结束" 注释即可找到。

## 安全注意事项

1. 测试代码只在 `APP_DEBUG=true` 时生效
2. 需要特定的 HTTP 头部才能触发
3. 生产环境必须移除测试代码
4. 测试时使用独立的测试数据库

## 总结

通过这些测试，我们可以验证：

1. ✅ TransactionManager 正确处理不同类型的异常
2. ✅ BusinessException 不被包装，直接传播
3. ✅ 系统异常被正确包装成 TransactionException
4. ✅ 数据库事务在异常时正确回滚
5. ✅ 没有脏数据残留在数据库中

这确保了我们的事务管理机制在各种异常场景下都能正确工作。

# TransactionManager 事务回调机制分析

## 概述

TransactionManager 中定义了三个事务回调函数数组，用于在事务的不同阶段执行自定义逻辑。本文档详细分析这些回调机制的实现、使用情况和扩展建议。

## 1. 回调函数注册机制

### 1.1 回调函数定义

在 `TransactionManager` 类中定义了三个回调函数数组：

```php
protected array $callbacks = [
    'before_begin' => [],    // 事务开始前回调
    'after_commit' => [],    // 事务提交后回调
    'after_rollback' => [],  // 事务回滚后回调
];
```

### 1.2 注册方法

通过 `registerCallback()` 方法注册回调函数：

```php
/**
 * 注册事务回调
 *
 * @param string $event 事件类型
 * @param callable $callback 回调函数
 */
public function registerCallback(string $event, callable $callback): bool
{
    if (!isset($this->callbacks[$event])) {
        return false;
    }

    $this->callbacks[$event][] = $callback;
    return true;
}
```

### 1.3 当前注册情况

**搜索结果显示**：
- 在测试文件中有使用示例
- 在文档中有使用说明
- **在实际业务代码中未发现使用**

## 2. 回调函数触发时机

### 2.1 触发机制

通过 `triggerCallbacks()` 方法触发回调：

```php
protected function triggerCallbacks(string $event): void
{
    if (isset($this->callbacks[$event])) {
        foreach ($this->callbacks[$event] as $callback) {
            try {
                $callback();
            } catch (Exception $e) {
                Log::warning('事务回调执行失败', [
                    'event' => $event,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
```

### 2.2 具体触发时机

#### 2.2.1 before_begin 回调

**触发位置**：`executeInTransaction()` 方法开始时
```php
// 触发开始前回调
$this->triggerCallbacks('before_begin');

// 设置事务超时
$this->setTimeout($timeout);

$result = DB::transaction(function () use ($callback, $timeout, $startTime) {
    // 事务逻辑
});
```

**作用**：
- 在事务开始前执行准备工作
- 可用于资源预分配、状态检查等
- 如果回调失败，不会影响事务执行（仅记录警告日志）

#### 2.2.2 after_commit 回调

**触发位置**：事务成功提交后
```php
$result = DB::transaction(function () use ($callback, $timeout, $startTime) {
    return $callback();
});

// 触发提交后回调
$this->triggerCallbacks('after_commit');
```

**作用**：
- 在事务成功提交后执行后续处理
- 适用于缓存清理、事件通知、审计日志等
- 回调失败不会影响已提交的事务

#### 2.2.3 after_rollback 回调

**触发位置**：事务发生异常回滚时
```php
} catch (Exception $e) {
    $lastException = $e;
    // 触发回滚后回调
    $this->triggerCallbacks('after_rollback');
    
    // 异常处理逻辑
}
```

**作用**：
- 在事务回滚后执行清理工作
- 适用于状态恢复、错误通知、补偿操作等
- 回调失败不会影响回滚操作

## 3. 实际使用情况

### 3.1 测试代码中的使用

在 `TransactionManagerTest.php` 中有基本的功能测试：

```php
public function test_register_callback(): void
{
    $callbackExecuted = false;

    $callback = function () use (&$callbackExecuted) {
        $callbackExecuted = true;
    };

    $result = $this->transactionManager->registerCallback('after_commit', $callback);
    $this->assertTrue($result);

    // 执行事务以触发回调
    $this->transactionManager->executeInTransaction(function () {
        return 'test';
    });

    $this->assertTrue($callbackExecuted);
}
```

### 3.2 文档中的使用示例

在 `database-optimization-usage.md` 中有使用说明：

```php
// 注册事务回调
$this->transactionManager->registerCallback('after_commit', function () {
    // 事务提交后执行
    $this->clearCache();
});
```

### 3.3 业务代码中的使用情况

**当前状态**：在实际的业务 Service 层代码中未发现使用事务回调的地方。

**原因分析**：
1. 功能相对较新，业务代码尚未采用
2. 开发团队可能不了解此功能
3. 当前业务场景相对简单，暂未需要复杂的事务回调

## 4. 扩展建议

基于线索管理的业务场景，以下是事务回调的实际应用建议：

### 4.1 缓存管理

**场景**：线索创建/更新后清理相关缓存

```php
// 在 LeadService 构造函数中注册回调
public function __construct(
    private LeadRepositoryInterface $leadRepository,
    private TransactionManagerInterface $transactionManager,
    private CacheManager $cacheManager
) {
    // 注册事务提交后的缓存清理回调
    $this->transactionManager->registerCallback('after_commit', function () {
        $this->cacheManager->forget([
            'leads:list:*',
            'leads:stats:*',
            'leads:regions:*'
        ]);
    });
}

public function createLead(LeadCreateDTO $dto): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        $lead = $this->leadRepository->create($dto->toArray());
        // 事务提交后会自动清理缓存
        return $lead;
    });
}
```

### 4.2 事件通知

**场景**：线索状态变更后发送通知

```php
public function updateLeadStatus(int $leadId, int $status): bool
{
    // 注册事务提交后的通知回调
    $this->transactionManager->registerCallback('after_commit', function () use ($leadId, $status) {
        // 发送状态变更通知
        event(new LeadStatusChanged($leadId, $status));
        
        // 发送邮件通知
        Mail::to($this->getLeadOwner($leadId))
            ->send(new LeadStatusChangedMail($leadId, $status));
    });

    return $this->transactionManager->executeInTransaction(function () use ($leadId, $status) {
        return $this->leadRepository->updateStatus($leadId, $status);
    });
}
```

### 4.3 审计日志

**场景**：记录业务操作的审计日志

```php
public function deleteLeadWithAudit(int $leadId, int $operatorId): bool
{
    $lead = $this->leadRepository->findById($leadId);
    
    // 注册审计日志回调
    $this->transactionManager->registerCallback('after_commit', function () use ($lead, $operatorId) {
        // 记录删除操作的审计日志
        AuditLog::create([
            'entity_type' => 'Lead',
            'entity_id' => $lead->id,
            'action' => 'delete',
            'operator_id' => $operatorId,
            'old_data' => $lead->toArray(),
            'created_at' => now(),
        ]);
    });

    // 注册回滚后的补偿操作
    $this->transactionManager->registerCallback('after_rollback', function () use ($leadId) {
        Log::warning('线索删除失败，需要人工检查', [
            'lead_id' => $leadId,
            'timestamp' => now(),
        ]);
    });

    return $this->transactionManager->executeInTransaction(function () use ($leadId) {
        return $this->leadRepository->delete($leadId);
    });
}
```

### 4.4 数据同步

**场景**：与外部系统同步数据

```php
public function createLeadWithSync(LeadCreateDTO $dto): Lead
{
    $lead = null;
    
    // 注册同步回调
    $this->transactionManager->registerCallback('after_commit', function () use (&$lead) {
        // 同步到 CRM 系统
        $this->crmSyncService->syncLead($lead);
        
        // 同步到数据仓库
        $this->dataWarehouseService->syncLead($lead);
    });

    // 注册回滚后的清理回调
    $this->transactionManager->registerCallback('after_rollback', function () use ($dto) {
        // 记录同步失败
        Log::error('线索创建失败，同步操作已取消', [
            'company_name' => $dto->companyFullName,
            'timestamp' => now(),
        ]);
    });

    $lead = $this->transactionManager->executeInTransaction(function () use ($dto) {
        return $this->leadRepository->create($dto->toArray());
    });

    return $lead;
}
```

### 4.5 性能监控

**场景**：监控事务执行性能

```php
public function __construct(
    private LeadRepositoryInterface $leadRepository,
    private TransactionManagerInterface $transactionManager,
    private MetricsCollector $metricsCollector
) {
    // 注册性能监控回调
    $this->transactionManager->registerCallback('before_begin', function () {
        $this->metricsCollector->startTimer('transaction.lead.duration');
    });

    $this->transactionManager->registerCallback('after_commit', function () {
        $this->metricsCollector->endTimer('transaction.lead.duration');
        $this->metricsCollector->increment('transaction.lead.success');
    });

    $this->transactionManager->registerCallback('after_rollback', function () {
        $this->metricsCollector->endTimer('transaction.lead.duration');
        $this->metricsCollector->increment('transaction.lead.failure');
    });
}
```

## 5. 实现建议

### 5.1 创建回调管理服务

为了更好地管理事务回调，建议创建专门的回调管理服务：

```php
<?php

namespace App\Services\Transaction;

use App\Contracts\TransactionManagerInterface;

/**
 * 事务回调管理服务
 */
class TransactionCallbackManager
{
    public function __construct(
        private TransactionManagerInterface $transactionManager
    ) {}

    /**
     * 注册线索相关的事务回调
     */
    public function registerLeadCallbacks(): void
    {
        // 缓存清理回调
        $this->transactionManager->registerCallback('after_commit', 
            $this->createCacheCleanupCallback()
        );

        // 审计日志回调
        $this->transactionManager->registerCallback('after_commit', 
            $this->createAuditLogCallback()
        );

        // 错误通知回调
        $this->transactionManager->registerCallback('after_rollback', 
            $this->createErrorNotificationCallback()
        );
    }

    private function createCacheCleanupCallback(): callable
    {
        return function () {
            // 缓存清理逻辑
        };
    }

    private function createAuditLogCallback(): callable
    {
        return function () {
            // 审计日志逻辑
        };
    }

    private function createErrorNotificationCallback(): callable
    {
        return function () {
            // 错误通知逻辑
        };
    }
}
```

### 5.2 在 ServiceProvider 中注册

```php
<?php

namespace App\Providers;

use App\Services\Transaction\TransactionCallbackManager;
use Illuminate\Support\ServiceProvider;

class TransactionServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $callbackManager = $this->app->make(TransactionCallbackManager::class);
        $callbackManager->registerLeadCallbacks();
    }
}
```

## 6. 注意事项

### 6.1 回调执行顺序

- 回调按注册顺序执行
- 如果某个回调失败，不会影响后续回调的执行
- 回调失败只会记录警告日志

### 6.2 性能考虑

- 回调应该尽量轻量级，避免耗时操作
- 对于耗时操作，建议使用队列异步处理
- 避免在回调中执行数据库事务操作

### 6.3 异常处理

- 回调中的异常不会影响主事务
- 建议在回调中添加适当的异常处理
- 重要的回调操作应该有重试机制

## 7. 总结

TransactionManager 的事务回调机制提供了强大的扩展能力，但目前在项目中使用较少。建议：

1. **逐步引入**：从简单的缓存清理开始使用
2. **统一管理**：创建专门的回调管理服务
3. **文档完善**：为团队提供使用指南和最佳实践
4. **监控完善**：添加回调执行的监控和告警

通过合理使用事务回调，可以实现更好的关注点分离，提高代码的可维护性和扩展性。

# Makefile 使用指南

本文档详细说明了 Laravel CRM API 项目 Makefile 的使用方法和最佳实践。

## 快速开始

### 查看所有可用命令
```bash
make help
```

### 初始化开发环境
```bash
# 完整的开发环境初始化（推荐新项目使用）
make dev-setup

# 或者分步执行
make install    # 安装依赖
make migrate    # 运行迁移
make seed       # 填充数据
```

### 启动开发服务器
```bash
# 前台启动（推荐开发时使用）
make serve

# 后台启动
make serve-bg

# 停止后台服务器
make stop
```

## 命令分类详解

### 1. 开发环境管理

| 命令 | 说明 | 使用场景 |
|------|------|----------|
| `make install` | 安装项目依赖 | 首次克隆项目后 |
| `make update` | 更新项目依赖 | 定期更新依赖包 |
| `make serve` | 启动开发服务器 | 日常开发 |
| `make dev-setup` | 完整环境初始化 | 新项目或新环境 |
| `make dev-reset` | 重置开发环境 | 环境出现问题时 |
| `make dev-status` | 检查环境状态 | 排查环境问题 |

### 2. 代码质量检查

| 命令 | 说明 | 建议频率 |
|------|------|----------|
| `make lint` | 检查代码格式 | 提交前 |
| `make lint-fix` | 自动修复代码格式 | 开发过程中 |
| `make analyze` | 静态代码分析 | 每日构建 |
| `make security-check` | 安全漏洞检查 | 每周 |

### 3. 测试相关

| 命令 | 说明 | 使用场景 |
|------|------|----------|
| `make test` | 运行所有测试 | 提交前 |
| `make test-unit` | 运行单元测试 | 开发过程中 |
| `make test-feature` | 运行功能测试 | 功能完成后 |
| `make test-coverage` | 生成覆盖率报告 | 质量检查 |
| `make test-parallel` | 并行运行测试 | 加速测试 |

### 4. 缓存和优化

| 命令 | 说明 | 使用场景 |
|------|------|----------|
| `make cache-clear` | 清除所有缓存 | 配置更改后 |
| `make optimize` | 优化应用性能 | 生产部署前 |

### 5. Telescope 监控管理

| 命令 | 说明 | 使用场景 |
|------|------|----------|
| `make telescope-install` | 安装 Telescope | 首次使用 |
| `make telescope-enable` | 启用监控 | 开发调试 |
| `make telescope-disable` | 禁用监控 | 生产环境 |
| `make telescope-status` | 检查状态 | 故障排查 |
| `make telescope-clear` | 清除监控数据 | 定期维护 |
| `make telescope-prune` | 清理过期数据 | 每周维护 |
| `make telescope-config` | 显示配置建议 | 初始配置 |

### 6. 实用工具
|------|------|----------|
| `make lint` | 检查代码格式 | 每次提交前 |
| `make lint-fix` | 自动修复格式 | 代码格式有问题时 |
| `make analyze` | 静态代码分析 | 每周一次 |
| `make security-check` | 安全检查 | 每次更新依赖后 |

### 3. 测试相关

| 命令 | 说明 | 使用时机 |
|------|------|----------|
| `make test` | 运行所有测试 | 每次提交前 |
| `make test-unit` | 运行单元测试 | 开发单个功能时 |
| `make test-feature` | 运行功能测试 | 集成测试时 |
| `make test-coverage` | 生成覆盖率报告 | 评估测试质量 |
| `make test-parallel` | 并行运行测试 | 加速测试执行 |

### 4. 数据库操作

| 命令 | 说明 | 注意事项 |
|------|------|----------|
| `make migrate` | 运行迁移 | 安全操作 |
| `make migrate-fresh` | 重建数据库 | ⚠️ 会删除所有数据 |
| `make seed` | 填充测试数据 | 开发环境使用 |
| `make db-reset` | 重置数据库 | ⚠️ 会删除所有数据 |
| `make db-status` | 查看迁移状态 | 检查数据库状态 |

### 5. 缓存和优化

| 命令 | 说明 | 使用场景 |
|------|------|----------|
| `make cache-clear` | 清除所有缓存 | 配置更改后 |
| `make optimize` | 优化应用性能 | 生产环境部署前 |
| `make config-clear` | 清除配置缓存 | 配置文件更改后 |
| `make route-clear` | 清除路由缓存 | 路由更改后 |

### 6. 代码生成

代码生成命令需要传递参数，使用格式：`make 命令 name=名称`

```bash
# 创建控制器
make make-controller name=LeadController

# 创建模型（包含迁移、工厂、填充器、资源）
make make-model name=Lead

# 创建迁移文件
make make-migration name=create_leads_table

# 创建请求验证类
make make-request name=StoreLeadRequest

# 创建API资源类
make make-resource name=LeadResource

# 创建服务类
make make-service name=LeadService

# 创建仓储类（包含接口）
make make-repository name=LeadRepository
```

## 常用工作流程

### 日常开发流程
```bash
# 1. 启动开发环境
make serve

# 2. 开发过程中运行测试
make test-unit

# 3. 提交前完整检查
make quick-test
```

### 新功能开发流程
```bash
# 1. 创建相关文件
make make-model name=Lead
make make-controller name=LeadController
make make-request name=StoreLeadRequest
make make-service name=LeadService

# 2. 运行迁移
make migrate

# 3. 测试功能
make test-feature

# 4. 代码检查
make full-check
```

### 环境问题排查流程
```bash
# 1. 检查环境状态
make dev-status

# 2. 清除缓存
make cache-clear

# 3. 重置环境（如果需要）
make dev-reset
```

### Telescope 监控设置流程
```bash
# 1. 安装 Telescope
make telescope-install

# 2. 启用监控
make telescope-enable

# 3. 清除缓存
make cache-clear

# 4. 启动服务器
make serve

# 5. 访问监控界面
# http://127.0.0.1:8000/telescope
```

### Telescope 日常维护
```bash
# 每周清理过期数据
make telescope-prune

# 检查监控状态
make telescope-status

# 生产环境禁用
make telescope-disable
```

### 部署准备流程
```bash
# 1. 运行完整检查
make full-check

# 2. 禁用 Telescope（生产环境）
make telescope-disable

# 3. 优化应用
make optimize

# 3. 准备部署
make deploy-prepare
```

## 组合命令说明

为了提高效率，Makefile 提供了几个组合命令：

- `make quick-test`: 代码格式检查 + 测试
- `make full-check`: 格式检查 + 静态分析 + 测试 + 安全检查
- `make reset-all`: 清除缓存 + 重置数据库 + 优化
- `make dev-setup`: 安装依赖 + 迁移 + 填充数据
- `make dev-reset`: 清除缓存 + 重建数据库 + 填充数据

## 最佳实践

### 1. 提交前检查清单
```bash
make lint-fix      # 修复代码格式
make test          # 运行测试
make security-check # 安全检查
```

### 2. 定期维护
```bash
# 每周执行一次
make update        # 更新依赖
make analyze       # 静态分析
make test-coverage # 检查测试覆盖率
```

### 3. 环境切换
```bash
# 切换到测试环境
cp .test.env .env
make cache-clear
make migrate

# 切换到生产环境
cp .prod.env .env
make optimize
```

## 故障排除

### 常见问题及解决方案

1. **数据库连接失败**
   ```bash
   make dev-status  # 检查配置
   # 检查 .env 文件中的数据库配置
   ```

2. **缓存问题**
   ```bash
   make cache-clear  # 清除所有缓存
   ```

3. **权限问题**
   ```bash
   chmod -R 775 storage bootstrap/cache
   ```

4. **依赖问题**
   ```bash
   make update  # 更新依赖
   composer dump-autoload  # 重新生成自动加载
   ```

## 扩展 Makefile

如果需要添加新的命令，请遵循以下规范：

1. 添加适当的注释说明
2. 使用 `.PHONY` 声明
3. 提供清晰的错误信息
4. 考虑命令的幂等性
5. 添加到相应的分类中

示例：
```makefile
.PHONY: my-command

my-command: ## 我的自定义命令
	@echo "执行自定义操作..."
	# 具体命令
	@echo "操作完成！"
```

## 注意事项

1. **危险操作**：带有 ⚠️ 标记的命令会删除数据，请谨慎使用
2. **环境变量**：确保 `.env` 文件配置正确
3. **权限问题**：某些命令可能需要适当的文件权限
4. **依赖检查**：部分命令依赖特定的工具，如 PHPStan、Psalm 等

通过合理使用这些 Makefile 命令，可以大大提高开发效率和代码质量。建议将常用命令加入到你的开发工作流程中。

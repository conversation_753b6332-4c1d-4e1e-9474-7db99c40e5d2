# SimpleTransactionLogger 使用指南

## 概述

`SimpleTransactionLogger` 提供了统一的事务日志记录功能，支持任意业务模块的事务操作日志记录。

## 核心方法

### `logBusinessTransaction()` 通用方法

```php
public static function logBusinessTransaction(
    callable $callback,
    string $module,
    string $action,
    array $businessData,
    array $additionalContext = []
): mixed
```

## 使用模式

### 1. 基本使用模式

```php
class SomeService
{
    public function createSomething(SomeCreateDTO $dto): mixed
    {
        return SimpleTransactionLogger::logBusinessTransaction(
            function () use ($dto) {
                return $this->transactionManager->executeInTransaction(function () use ($dto) {
                    // 具体的业务逻辑
                    $entity = $this->repository->create($dto->toArray());
                    
                    // 其他业务处理
                    $this->processRelatedData($entity);
                    
                    return $entity;
                });
            },
            'ModuleName',        // 模块名称
            'create',            // 操作类型
            $dto->toArray(),     // 业务数据
            [                   // 额外上下文（可选）
                'key_field' => $dto->someImportantField,
                'user_id' => auth()->id(),
            ]
        );
    }
}
```

### 2. 不同操作类型示例

#### Create 操作

```php
public function createContact(ContactCreateDTO $dto): Contact
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($dto) {
            return $this->transactionManager->executeInTransaction(function () use ($dto) {
                $contact = $this->contactRepository->create($dto->toArray());
                
                // 关联到线索
                if ($dto->leadId) {
                    $this->leadContactRepository->create([
                        'lead_id' => $dto->leadId,
                        'contact_id' => $contact->id,
                    ]);
                }
                
                return $contact;
            });
        },
        'Contact',
        'create',
        $dto->toArray(),
        [
            'lead_id' => $dto->leadId,
            'department' => $dto->department,
        ]
    );
}
```

#### Update 操作

```php
public function updateUser(int $userId, UserUpdateDTO $dto): bool
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($userId, $dto) {
            return $this->transactionManager->executeInTransaction(function () use ($userId, $dto) {
                $updated = $this->userRepository->update($userId, $dto->toArray());
                
                // 更新相关缓存
                $this->cacheManager->forget("user:{$userId}");
                
                return $updated;
            });
        },
        'User',
        'update',
        $dto->toArray(),
        [
            'user_id' => $userId,
            'updated_fields' => array_keys($dto->toArray()),
        ]
    );
}
```

#### Delete 操作

```php
public function deleteLead(int $leadId): bool
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($leadId) {
            return $this->transactionManager->executeInTransaction(function () use ($leadId) {
                // 软删除线索
                $deleted = $this->leadRepository->delete($leadId);
                
                // 删除相关关系
                $this->leadUserRepository->deleteByLeadId($leadId);
                $this->leadContactRepository->deleteByLeadId($leadId);
                
                return $deleted;
            });
        },
        'Lead',
        'delete',
        [],
        ['lead_id' => $leadId]
    );
}
```

### 3. 复杂业务场景示例

#### 批量操作

```php
public function batchCreateContacts(array $contactsData): array
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($contactsData) {
            return $this->transactionManager->executeInTransaction(function () use ($contactsData) {
                $createdContacts = [];
                
                foreach ($contactsData as $contactData) {
                    $contact = $this->contactRepository->create($contactData);
                    $createdContacts[] = $contact;
                }
                
                return $createdContacts;
            });
        },
        'Contact',
        'batch_create',
        $contactsData,
        [
            'batch_size' => count($contactsData),
            'operation_type' => 'bulk_import',
        ]
    );
}
```

#### 跨模块操作

```php
public function transferLead(int $leadId, int $fromUserId, int $toUserId): bool
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($leadId, $fromUserId, $toUserId) {
            return $this->transactionManager->executeInTransaction(function () use ($leadId, $fromUserId, $toUserId) {
                // 更新线索负责人
                $this->leadUserRepository->updatePrimaryUser($leadId, $toUserId);
                
                // 记录转移历史
                $this->leadTransferRepository->create([
                    'lead_id' => $leadId,
                    'from_user_id' => $fromUserId,
                    'to_user_id' => $toUserId,
                    'transferred_at' => now(),
                ]);
                
                // 发送通知
                $this->notificationService->notifyLeadTransfer($leadId, $fromUserId, $toUserId);
                
                return true;
            });
        },
        'Lead',
        'transfer',
        [],
        [
            'lead_id' => $leadId,
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
            'operation_type' => 'ownership_transfer',
        ]
    );
}
```

## 日志输出格式

### 事务开始日志

```json
{
  "message": "事务开始",
  "transaction_id": "tx_7ef55204-7344-4141-93f5-a2d2e8e6696d",
  "trace_id": "request-trace-id",
  "operation_type": "lead_create",
  "user_id": 1,
  "timestamp": "2025-08-02T04:19:17.049053Z",
  "memory_usage_mb": 22.0,
  "module": "Lead",
  "action": "create",
  "business_data": {
    "company_full_name": "测试公司",
    "company_short_name": "测试",
    "region": 1,
    "status": 1
  },
  "company_name": "测试公司"
}
```

### 事务提交日志

```json
{
  "message": "事务提交成功",
  "transaction_id": "tx_7ef55204-7344-4141-93f5-a2d2e8e6696d",
  "trace_id": "request-trace-id",
  "operation_type": "lead_create",
  "user_id": 1,
  "timestamp": "2025-08-02T04:19:17.084289Z",
  "duration_ms": 35.2,
  "memory_usage_mb": 22.5,
  "memory_peak_mb": 23.0,
  "memory_diff_mb": 0.5,
  "module": "Lead",
  "action": "create",
  "result": {
    "operation_success": true,
    "module": "Lead",
    "action": "create",
    "created_id": 47,
    "company_name": "测试公司"
  }
}
```

### 事务回滚日志

```json
{
  "message": "事务回滚",
  "transaction_id": "tx_7ef55204-7344-4141-93f5-a2d2e8e6696d",
  "trace_id": "request-trace-id",
  "operation_type": "lead_create",
  "user_id": 1,
  "timestamp": "2025-08-02T04:19:17.090983Z",
  "duration_ms": 41.9,
  "memory_usage_mb": 22.0,
  "memory_peak_mb": 23.0,
  "memory_diff_mb": 0.0,
  "module": "Lead",
  "action": "create",
  "error_context": {
    "failed_data": {
      "company_full_name": "重复公司名称"
    },
    "additional_context": {
      "company_name": "重复公司名称"
    },
    "exception_class": "App\\Exceptions\\BusinessException",
    "exception_message": "公司名称已存在",
    "exception_code": 409,
    "error_severity": "low"
  }
}
```

## 最佳实践

### 1. 模块命名规范
- 使用 PascalCase 格式：`Lead`, `Contact`, `User`, `Order`
- 保持简洁明了，避免缩写

### 2. 操作类型规范
- 使用小写下划线格式：`create`, `update`, `delete`, `batch_create`, `transfer`
- 描述具体的业务操作

### 3. 业务数据规范
- 传入完整的业务数据，便于问题排查
- 避免传入敏感信息（如密码）
- 使用 DTO 的 `toArray()` 方法

### 4. 额外上下文规范
- 传入关键的业务标识符（如 ID、名称）
- 包含重要的业务属性
- 避免冗余信息

## 性能考虑

- 通用方法的性能开销极小
- 智能结果提取避免了不必要的数据处理
- 静态变量管理确保内存效率

## 总结

通用事务日志方法提供了：
- ✅ 统一的事务日志记录接口
- ✅ 灵活的多模块支持
- ✅ 智能的结果信息提取
- ✅ 完整的错误处理机制
- ✅ 向后兼容的迁移路径

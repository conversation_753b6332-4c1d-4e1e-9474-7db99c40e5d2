# Laravel API 日志中间件使用指南

## 功能概述

Laravel API 日志中间件是一个功能强大的请求日志记录组件，提供以下核心功能：

- **完整的 API 请求链路追踪**：自动生成或使用请求头中的 trace_id 进行链路追踪
- **MySQL 查询统计集成**：记录每个请求执行的所有 SQL 语句及其耗时
- **容错机制**：多层容错保护，确保日志记录失败不影响业务流程
- **敏感数据过滤**：自动过滤密码、token 等敏感信息
- **性能监控**：记录请求耗时、内存使用、慢查询等性能指标
- **灵活配置**：支持路径排除、方法过滤、异步记录等配置选项

### 核心价值

1. **问题排查效率提升**：通过 trace_id 快速定位问题请求及其相关的数据库操作
2. **性能优化支持**：详细的查询统计帮助识别性能瓶颈
3. **系统稳定性保障**：容错机制确保日志功能不会影响主业务
4. **安全合规**：自动过滤敏感信息，满足数据安全要求

## 配置指南

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# API 日志基础配置
API_LOGGING_ENABLED=true
API_LOGGING_ASYNC=false

# 数据库查询日志配置
DB_QUERY_LOGGING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=100
DB_MAX_QUERIES_LOGGED=50

# 容错机制配置
LOG_RESILIENT_MAX_FAILURES=5
LOG_RESILIENT_CIRCUIT_TIMEOUT=300
LOG_RESILIENT_TIMEOUT=2
LOG_RESILIENT_ENABLE_BACKUP=true
LOG_RESILIENT_ENABLE_QUEUE=true

# 日志限制配置
LOG_MAX_USER_AGENT_LENGTH=500
LOG_MAX_INPUT_SIZE=10240

# 健康监控配置
LOG_MONITORING_ALERT_COOLDOWN=1800
LOG_MONITORING_DISK_THRESHOLD=85
LOG_MONITORING_FILE_SIZE_THRESHOLD=100
LOG_MONITORING_FAILURE_RATE_THRESHOLD=10
LOG_MONITORING_RESPONSE_TIME_THRESHOLD=1000
```

### 配置参数说明

#### API 日志配置
- `API_LOGGING_ENABLED`: 是否启用 API 日志记录（默认：true）
- `API_LOGGING_ASYNC`: 是否异步记录日志（默认：false）

#### 数据库查询配置
- `DB_QUERY_LOGGING_ENABLED`: 是否启用查询统计（默认：true）
- `DB_SLOW_QUERY_THRESHOLD`: 慢查询阈值，单位毫秒（默认：100）
- `DB_MAX_QUERIES_LOGGED`: 单个请求最大记录查询数（默认：50）

#### 容错机制配置
- `LOG_RESILIENT_MAX_FAILURES`: 触发熔断器的最大失败次数（默认：5）
- `LOG_RESILIENT_CIRCUIT_TIMEOUT`: 熔断器超时时间，单位秒（默认：300）
- `LOG_RESILIENT_TIMEOUT`: 单次日志记录超时时间，单位秒（默认：2）
- `LOG_RESILIENT_ENABLE_BACKUP`: 是否启用缓存备份（默认：true）
- `LOG_RESILIENT_ENABLE_QUEUE`: 是否启用队列降级（默认：true）
## 使用方法

### 中间件注册

#### 全局中间件注册

在 `app/Http/Kernel.php` 中注册为全局中间件：

```php
protected $middleware = [
    // 其他中间件...
    \App\Http\Middleware\ApiLoggingMiddleware::class,
];
```

#### 路由组中间件注册

在 `app/Http/Kernel.php` 中注册为路由中间件：

```php
protected $middlewareGroups = [
    'api' => [
        // 其他中间件...
        \App\Http\Middleware\ApiLoggingMiddleware::class,
    ],
];
```

或者注册为命名中间件：

```php
protected $middlewareAliases = [
    // 其他中间件...
    'api.logging' => \App\Http\Middleware\ApiLoggingMiddleware::class,
];
```

#### 路由中使用

```php
// 单个路由
Route::get('/api/users', [UserController::class, 'index'])
    ->middleware('api.logging');

// 路由组
Route::middleware(['api.logging'])->group(function () {
    Route::get('/api/users', [UserController::class, 'index']);
    Route::post('/api/users', [UserController::class, 'store']);
    Route::get('/api/users/{id}', [UserController::class, 'show']);
});

// API 路由组（推荐）
Route::prefix('api')->middleware(['api.logging'])->group(function () {
    Route::apiResource('users', UserController::class);
    Route::apiResource('posts', PostController::class);
});
```

### 链路追踪使用

#### 前端发送请求时携带 trace_id

```javascript
// 前端 JavaScript 示例
const traceId = generateUUID(); // 生成唯一 ID

fetch('/api/users', {
    method: 'GET',
    headers: {
        'X-Request-ID': traceId,
        'Content-Type': 'application/json'
    }
});
```

#### 服务间调用传递 trace_id

```php
// Laravel HTTP 客户端示例
use Illuminate\Support\Facades\Http;

$traceId = request()->header('X-Request-ID') ?? Str::uuid();

$response = Http::withHeaders([
    'X-Request-ID' => $traceId,
    'Accept' => 'application/json'
])->get('https://api.example.com/data');
```

### 排除特定路径

在 `config/logging.php` 中配置排除路径：

```php
'api_logging' => [
    'enabled' => env('API_LOGGING_ENABLED', true),
    'excluded_paths' => [
        '/health',
        '/ping',
        '/metrics',
        '/api/health',
        '/api/system/*',  // 支持通配符
    ],
    'excluded_methods' => [
        'OPTIONS',
    ],
],
```
## 日志输出示例

### 基础日志格式

```json
{
    "trace_id": "9d1e8c2f-4b3a-4d5e-8f7a-1c2b3d4e5f6g",
    "method": "GET",
    "uri": "/api/users",
    "full_url": "https://api.example.com/api/users?page=1&limit=10",
    "status": 200,
    "duration_ms": 156.78,
    "ip": "*************",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "user_id": 123,
    "memory_usage": {
        "current_mb": "12.5M",
        "peak_mb": "15.2M"
    },
    "input_size": "245 bytes",
    "input": {
        "page": 1,
        "limit": 10,
        "search": "john"
    },
    "timestamp": "2024-01-15T10:30:45.123Z"
}
```

### 包含数据库查询统计的日志

```json
{
    "trace_id": "9d1e8c2f-4b3a-4d5e-8f7a-1c2b3d4e5f6g",
    "method": "POST",
    "uri": "/api/users",
    "full_url": "https://api.example.com/api/users",
    "status": 201,
    "duration_ms": 234.56,
    "ip": "*************",
    "user_agent": "PostmanRuntime/7.32.3",
    "user_id": null,
    "memory_usage": {
        "current_mb": "14.8M",
        "peak_mb": "18.3M"
    },
    "input_size": "156 bytes",
    "input": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "password": "[FILTERED]"
    },
    "timestamp": "2024-01-15T10:35:22.456Z",
    "database_queries": {
        "total_queries": 3,
        "total_time_ms": 45.67,
        "slow_queries_count": 1,
        "queries": [
            {
                "sql": "SELECT * FROM users WHERE email = ?",
                "bindings": ["<EMAIL>"],
                "execution_time": 12.34,
                "is_slow": false,
                "timestamp": 1705312522.456
            },
            {
                "sql": "INSERT INTO users (name, email, password, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                "bindings": ["John Doe", "<EMAIL>", "$2y$10$...", "2024-01-15 10:35:22", "2024-01-15 10:35:22"],
                "execution_time": 23.45,
                "is_slow": false,
                "timestamp": 1705312522.468
            },
            {
                "sql": "SELECT * FROM user_profiles WHERE user_id = ?",
                "bindings": [456],
                "execution_time": 156.78,
                "is_slow": true,
                "timestamp": 1705312522.491
            }
        ],
        "slow_queries_summary": [
            {
                "sql": "SELECT * FROM user_profiles WHERE user_id = ?",
                "time_ms": 156.78
            }
        ]
    }
}
```

### 错误情况下的日志

```json
{
    "trace_id": "9d1e8c2f-4b3a-4d5e-8f7a-1c2b3d4e5f6g",
    "method": "POST",
    "uri": "/api/users",
    "full_url": "https://api.example.com/api/users",
    "status": 422,
    "duration_ms": 89.12,
    "ip": "*************",
    "user_agent": "curl/7.68.0",
    "user_id": null,
    "memory_usage": {
        "current_mb": "11.2M",
        "peak_mb": "13.5M"
    },
    "input_size": "89 bytes",
    "input": {
        "name": "",
        "email": "invalid-email"
    },
    "timestamp": "2024-01-15T10:40:15.789Z",
    "database_queries": {
        "total_queries": 1,
        "total_time_ms": 8.45,
        "slow_queries_count": 0,
        "queries": [
            {
                "sql": "SELECT * FROM users WHERE email = ?",
                "bindings": ["invalid-email"],
                "execution_time": 8.45,
                "is_slow": false,
                "timestamp": 1705312815.789
            }
        ],
        "slow_queries_summary": []
    }
}
```

### 查询统计不可用时的日志

```json
{
    "trace_id": "9d1e8c2f-4b3a-4d5e-8f7a-1c2b3d4e5f6g",
    "method": "GET",
    "uri": "/api/users",
    "status": 200,
    "duration_ms": 123.45,
    "database_queries": {
        "total_queries": 0,
        "total_time_ms": 0.0,
        "slow_queries_count": 0,
        "queries": [],
        "slow_queries_summary": [],
        "error": "Query statistics unavailable"
    }
}
```
## 日志字段说明

### 基础字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `trace_id` | string | 链路追踪 ID，用于关联相关请求 |
| `method` | string | HTTP 请求方法（GET、POST、PUT、DELETE 等） |
| `uri` | string | 请求路径（不包含域名和查询参数） |
| `full_url` | string | 完整请求 URL（包含查询参数） |
| `status` | integer | HTTP 响应状态码 |
| `duration_ms` | float | 请求处理耗时（毫秒） |
| `ip` | string | 客户端 IP 地址 |
| `user_agent` | string | 用户代理字符串（已截断处理） |
| `user_id` | integer\|null | 当前认证用户 ID |
| `memory_usage` | object | 内存使用情况 |
| `input_size` | string | 请求数据大小 |
| `input` | object\|string | 请求输入数据（敏感信息已过滤） |
| `timestamp` | string | 请求时间戳（ISO 8601 格式） |

### 数据库查询统计字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `database_queries` | object | 数据库查询统计信息 |
| `database_queries.total_queries` | integer | 总查询数量 |
| `database_queries.total_time_ms` | float | 总执行时间（毫秒） |
| `database_queries.slow_queries_count` | integer | 慢查询数量 |
| `database_queries.queries` | array | 查询详情列表 |
| `database_queries.slow_queries_summary` | array | 慢查询摘要 |
| `database_queries.error` | string | 查询统计错误信息（可选） |

### 查询详情字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `sql` | string | SQL 语句 |
| `bindings` | array | 参数绑定值 |
| `execution_time` | float | 执行时间（毫秒） |
| `is_slow` | boolean | 是否为慢查询 |
| `timestamp` | float | 执行时间戳 |

### 内存使用字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `current_mb` | string | 当前内存使用量 |
| `peak_mb` | string | 峰值内存使用量 |

## 容错机制说明

### 熔断器机制

当日志记录连续失败达到配置的阈值时，熔断器会自动开启，暂停日志记录以保护系统：

1. **失败计数**：记录连续失败次数
2. **熔断触发**：达到最大失败次数时开启熔断器
3. **自动恢复**：超时后自动尝试恢复
4. **降级处理**：熔断期间使用备用策略

### 降级策略

当主要日志记录失败时，系统会依次尝试以下降级策略：

1. **缓存备份**：将日志数据存储到 Redis/文件缓存
2. **队列延迟处理**：将失败的日志放入队列稍后处理
3. **文件备份**：直接写入备份日志文件
4. **系统日志记录**：记录失败信息到系统日志

### 超时保护

- **同步记录超时**：默认 2 秒，防止日志记录阻塞请求
- **异步记录超时**：使用 `register_shutdown_function` 在请求结束后处理
- **熔断器超时**：默认 5 分钟，超时后尝试恢复正常记录

### 异常隔离

- **业务逻辑隔离**：日志记录异常不会影响主业务流程
- **多层异常捕获**：在不同层级捕获和处理异常
- **错误日志记录**：将日志系统的错误记录到独立的系统日志
## 监控和健康检查

### API 端点

系统提供了完整的健康检查 API 端点：

#### 1. 基础健康检查

```bash
GET /api/system/log-health
```

**响应示例：**

```json
{
    "overall_status": "healthy",
    "timestamp": "2024-01-15T10:45:30.123Z",
    "checks": {
        "log_channels": {
            "status": "healthy",
            "details": {
                "api": "accessible",
                "single": "accessible"
            }
        },
        "disk_space": {
            "status": "healthy",
            "usage_percent": 45.2,
            "threshold_percent": 85
        },
        "log_file_sizes": {
            "status": "healthy",
            "files": {
                "api.log": "23.5MB",
                "laravel.log": "12.8MB"
            }
        },
        "circuit_breaker": {
            "status": "closed",
            "failure_count": 0
        }
    }
}
```

#### 2. 容错服务状态

```bash
GET /api/system/log-health/resilient-status
```

**响应示例：**

```json
{
    "status": "ok",
    "resilient_logger": {
        "circuit_breaker_open": false,
        "failure_count": 0,
        "last_failure_time": null,
        "backup_logs_count": 0
    },
    "timestamp": "2024-01-15T10:45:30.123Z"
}
```

#### 3. 健康历史查询

```bash
GET /api/system/log-health/history?hours=24
```

**响应示例：**

```json
{
    "period": "24 hours",
    "summary": {
        "total_checks": 144,
        "healthy_count": 142,
        "degraded_count": 2,
        "unhealthy_count": 0
    },
    "timeline": [
        {
            "timestamp": "2024-01-15T10:00:00.000Z",
            "status": "healthy",
            "details": {...}
        }
    ]
}
```

#### 4. 手动触发健康检查

```bash
POST /api/system/log-health/check
```

#### 5. 重置熔断器

```bash
POST /api/system/log-health/reset-circuit-breaker
```

**响应示例：**

```json
{
    "status": "success",
    "message": "Circuit breaker reset successfully",
    "timestamp": "2024-01-15T10:45:30.123Z"
}
```

#### 6. 测试日志记录

```bash
POST /api/system/log-health/test-logging
Content-Type: application/json

{
    "async": false
}
```

**响应示例：**

```json
{
    "status": "success",
    "message": "Test log recorded successfully",
    "test_data": {
        "test_id": "test_65a5c2f2b1234",
        "timestamp": "2024-01-15T10:45:30.123Z",
        "test_type": "manual_test",
        "user_agent": "curl/7.68.0",
        "ip": "*************"
    },
    "async": false,
    "timestamp": "2024-01-15T10:45:30.123Z"
}
```

### 监控指标

#### 关键性能指标（KPI）

1. **日志记录成功率**：正常记录的请求比例
2. **平均响应时间**：日志记录对请求响应时间的影响
3. **熔断器触发频率**：系统稳定性指标
4. **磁盘使用率**：日志文件存储空间监控
5. **内存使用情况**：日志处理的内存开销

#### 告警阈值

- 磁盘使用率 > 85%
- 日志文件大小 > 100MB
- 失败率 > 10%
- 响应时间 > 1000ms
- 熔断器连续开启 > 30分钟

### 日志文件位置

```bash
# API 请求日志
storage/logs/api.log
storage/logs/api-2024-01-15.log  # 按日分割

# JSON 格式日志
storage/logs/api_json.log
storage/logs/api_json-2024-01-15.log

# 系统日志
storage/logs/laravel.log

# 备份日志（容错机制）
storage/logs/api_backup.log
```
## 故障排除

### 常见问题和解决方案

#### 1. 日志记录失败

**症状：** API 请求正常，但没有生成日志记录

**可能原因和解决方案：**

```bash
# 检查配置是否启用
php artisan tinker
>>> config('logging.api_logging.enabled')
=> true

# 检查日志目录权限
ls -la storage/logs/
chmod -R 775 storage/logs/
chown -R www-data:www-data storage/logs/

# 检查磁盘空间
df -h

# 查看错误日志
tail -f storage/logs/laravel.log | grep "api_logging_middleware"
```

#### 2. 查询统计缺失

**症状：** 日志中没有 `database_queries` 字段

**检查步骤：**

```bash
# 检查查询日志配置
php artisan tinker
>>> config('database.query_logging.enabled')
=> true

# 检查服务绑定
>>> app()->bound(\App\Contracts\DatabaseQueryCollectorInterface::class)
=> true

# 手动测试查询收集器
>>> $collector = app(\App\Contracts\DatabaseQueryCollectorInterface::class);
>>> $collector->startCollection();
>>> \App\Models\User::first();
>>> $stats = $collector->getQueryStatistics();
>>> dd($stats);
```

#### 3. 熔断器频繁触发

**症状：** 日志显示 "Circuit breaker open" 错误

**解决步骤：**

```bash
# 检查熔断器状态
curl -X GET http://localhost/api/system/log-health/resilient-status

# 重置熔断器
curl -X POST http://localhost/api/system/log-health/reset-circuit-breaker

# 调整熔断器配置
# 在 .env 中增加阈值
LOG_RESILIENT_MAX_FAILURES=10
LOG_RESILIENT_CIRCUIT_TIMEOUT=600
```

#### 4. 内存使用过高

**症状：** 服务器内存占用异常增长

**优化措施：**

```env
# 减少记录的查询数量
DB_MAX_QUERIES_LOGGED=20

# 限制输入数据大小
LOG_MAX_INPUT_SIZE=5120

# 启用异步记录
API_LOGGING_ASYNC=true

# 排除不必要的路径
# 在 config/logging.php 中添加更多排除路径
```

#### 5. 日志文件过大

**症状：** 日志文件快速增长，占用大量磁盘空间

**解决方案：**

```bash
# 检查日志轮转配置
# config/logging.php 中的 days 参数
'api' => [
    'driver' => 'daily',
    'path' => storage_path('logs/api.log'),
    'level' => 'info',
    'days' => 7,  # 保留7天
],

# 手动清理旧日志
find storage/logs -name "api-*.log" -mtime +7 -delete

# 设置定时任务清理
# 在 crontab 中添加
0 2 * * * find /path/to/storage/logs -name "api-*.log" -mtime +7 -delete
```

#### 6. 性能影响过大

**症状：** API 响应时间明显增加

**优化建议：**

```env
# 启用异步记录
API_LOGGING_ASYNC=true

# 减少查询记录数量
DB_MAX_QUERIES_LOGGED=20

# 排除高频路径
# 在 config/logging.php 中添加
'excluded_paths' => [
    '/api/health',
    '/api/metrics',
    '/api/ping',
    '/api/status/*',
],

# 调整超时时间
LOG_RESILIENT_TIMEOUT=1
```

### 调试技巧

#### 1. 启用详细日志

```php
// 临时在中间件中添加调试日志
Log::debug('API Logging Debug', [
    'trace_id' => $traceId,
    'query_logging_enabled' => $queryLoggingEnabled,
    'should_log' => $this->shouldLogRequest($request),
]);
```

#### 2. 使用 Artisan 命令测试

```bash
# 创建测试命令
php artisan make:command TestApiLogging

# 在命令中测试各个组件
php artisan test:api-logging
```

#### 3. 监控日志记录性能

```php
// 在中间件中添加性能监控
$loggingStart = microtime(true);
$this->resilientLogger->logApiRequest($logData, $asyncLogging);
$loggingDuration = (microtime(true) - $loggingStart) * 1000;

if ($loggingDuration > 100) { // 超过100ms记录警告
    Log::warning('Slow logging detected', [
        'duration_ms' => $loggingDuration,
        'trace_id' => $traceId
    ]);
}
```

### 错误代码参考

| 错误类型 | 日志关键词 | 说明 |
|----------|------------|------|
| 配置错误 | `Failed to read query logging configuration` | 配置文件读取失败 |
| 收集器错误 | `Failed to start database query collection` | 查询收集器启动失败 |
| 统计错误 | `Failed to get database query statistics` | 查询统计获取失败 |
| 重置错误 | `Failed to reset database query collector` | 收集器重置失败 |
| 处理错误 | `API request processing failed` | 请求处理异常 |
| 日志错误 | `API logging system failure` | 日志系统故障 |
| 熔断器 | `Circuit breaker open` | 熔断器开启 |
## 最佳实践

### 生产环境部署建议

#### 1. 性能优化配置

```env
# 生产环境推荐配置
API_LOGGING_ENABLED=true
API_LOGGING_ASYNC=true  # 启用异步记录

# 适中的查询记录限制
DB_QUERY_LOGGING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=200  # 生产环境可适当放宽
DB_MAX_QUERIES_LOGGED=30     # 减少记录数量

# 容错机制配置
LOG_RESILIENT_MAX_FAILURES=3
LOG_RESILIENT_CIRCUIT_TIMEOUT=600
LOG_RESILIENT_ENABLE_BACKUP=true
LOG_RESILIENT_ENABLE_QUEUE=true

# 限制日志大小
LOG_MAX_INPUT_SIZE=8192
LOG_MAX_USER_AGENT_LENGTH=300
```

#### 2. 安全配置

```php
// config/logging.php - 扩展敏感字段列表
'sensitive_fields' => [
    'password',
    'password_confirmation',
    'token',
    'api_key',
    'secret',
    'authorization',
    'x-api-key',
    'access_token',
    'refresh_token',
    'private_key',
    'credit_card',
    'card_number',
    'cvv',
    'ssn',
    'id_card',
    // 业务相关敏感字段
    'bank_account',
    'phone',
    'id_number',
    'address',
],
```

#### 3. 路径排除策略

```php
// config/logging.php - 生产环境路径排除
'api_logging' => [
    'excluded_paths' => [
        '/health',
        '/ping',
        '/metrics',
        '/api/health',
        '/api/system/*',
        '/api/monitoring/*',
        '/api/internal/*',
        // 高频低价值接口
        '/api/heartbeat',
        '/api/status',
        '/api/version',
    ],
    'excluded_methods' => [
        'OPTIONS',
        'HEAD',
    ],
],
```

#### 4. 日志轮转和清理

```php
// config/logging.php - 日志保留策略
'channels' => [
    'api' => [
        'driver' => 'daily',
        'path' => storage_path('logs/api.log'),
        'level' => 'info',
        'days' => 30,  // 生产环境保留30天
    ],
],
```

#### 5. 监控告警设置

```bash
# 设置 crontab 监控任务
# 每5分钟检查一次健康状态
*/5 * * * * curl -s http://localhost/api/system/log-health | jq '.overall_status' | grep -v "healthy" && echo "API logging unhealthy" | mail -s "Alert" <EMAIL>

# 每小时检查磁盘使用率
0 * * * * df -h | grep -E '9[0-9]%|100%' && echo "Disk usage high" | mail -s "Disk Alert" <EMAIL>

# 每天清理旧日志
0 2 * * * find /path/to/storage/logs -name "api-*.log" -mtime +30 -delete
```

### 开发环境配置

#### 1. 详细日志配置

```env
# 开发环境配置
API_LOGGING_ENABLED=true
API_LOGGING_ASYNC=false  # 同步记录便于调试

# 详细的查询记录
DB_QUERY_LOGGING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=50   # 更严格的慢查询阈值
DB_MAX_QUERIES_LOGGED=100    # 记录更多查询

# 宽松的容错配置
LOG_RESILIENT_MAX_FAILURES=10
LOG_RESILIENT_CIRCUIT_TIMEOUT=300

# 更大的日志限制
LOG_MAX_INPUT_SIZE=20480
```

#### 2. 调试辅助

```php
// 开发环境可以添加更多调试信息
// 在 AppServiceProvider 中
if (app()->environment('local')) {
    // 记录所有 SQL 查询到日志
    DB::listen(function ($query) {
        Log::channel('single')->debug('SQL Query', [
            'sql' => $query->sql,
            'bindings' => $query->bindings,
            'time' => $query->time,
        ]);
    });
}
```

### 团队协作规范

#### 1. trace_id 使用规范

```javascript
// 前端统一 trace_id 生成规范
function generateTraceId() {
    return 'web-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

// 微服务间调用规范
function callExternalService(url, data, traceId) {
    return fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': traceId || generateTraceId(),
        },
        body: JSON.stringify(data)
    });
}
```

#### 2. 日志查询规范

```bash
# 按 trace_id 查询完整链路
grep "9d1e8c2f-4b3a-4d5e-8f7a-1c2b3d4e5f6g" storage/logs/api-*.log

# 查询慢查询
grep -E '"is_slow":\s*true' storage/logs/api-*.log | jq '.database_queries.slow_queries_summary'

# 查询错误请求
grep -E '"status":\s*[45][0-9][0-9]' storage/logs/api-*.log

# 统计接口调用频率
grep -o '"uri":"[^"]*"' storage/logs/api-$(date +%Y-%m-%d).log | sort | uniq -c | sort -nr
```

#### 3. 性能分析规范

```bash
# 分析平均响应时间
grep '"duration_ms"' storage/logs/api-$(date +%Y-%m-%d).log | \
  grep -o '"duration_ms":[0-9.]*' | \
  cut -d: -f2 | \
  awk '{sum+=$1; count++} END {print "Average:", sum/count, "ms"}'

# 分析数据库查询性能
grep '"total_time_ms"' storage/logs/api-$(date +%Y-%m-%d).log | \
  grep -o '"total_time_ms":[0-9.]*' | \
  cut -d: -f2 | \
  awk '{sum+=$1; count++} END {print "DB Average:", sum/count, "ms"}'
```

### 扩展建议

#### 1. 集成 ELK Stack

```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./storage/logs:/logs

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

#### 2. 集成 Prometheus 监控

```php
// 创建自定义 Prometheus 指标
use Prometheus\CollectorRegistry;
use Prometheus\Storage\Redis;

class ApiMetricsCollector
{
    private $registry;
    
    public function __construct()
    {
        $this->registry = new CollectorRegistry(new Redis());
    }
    
    public function recordApiRequest($method, $uri, $status, $duration)
    {
        $counter = $this->registry->getOrRegisterCounter(
            'api_requests_total',
            'Total API requests',
            ['method', 'uri', 'status']
        );
        $counter->inc([$method, $uri, $status]);
        
        $histogram = $this->registry->getOrRegisterHistogram(
            'api_request_duration_seconds',
            'API request duration',
            ['method', 'uri']
        );
        $histogram->observe($duration / 1000, [$method, $uri]);
    }
}
```

#### 3. 自定义告警规则

```php
// 创建自定义告警服务
class ApiLoggingAlertService
{
    public function checkAndAlert()
    {
        $healthStatus = app(LogHealthMonitorService::class)->performHealthCheck();
        
        if ($healthStatus['overall_status'] !== 'healthy') {
            $this->sendAlert($healthStatus);
        }
        
        $this->checkSlowQueries();
        $this->checkHighErrorRate();
    }
    
    private function checkSlowQueries()
    {
        // 检查慢查询比例
        $slowQueryRate = $this->calculateSlowQueryRate();
        if ($slowQueryRate > 0.1) { // 超过10%
            $this->sendSlowQueryAlert($slowQueryRate);
        }
    }
}
```

这份使用文档涵盖了 Laravel API 日志中间件的所有核心功能和使用方法，包括配置、使用、监控、故障排除和最佳实践。文档结构清晰，示例完整，可以帮助开发者和运维人员快速理解和使用这个中间件组件。

# 错误处理重构文档

## 概述

本文档说明了如何重构项目中的硬编码错误处理机制，提供更优雅、可维护的错误管理方案。

## 重构目标

1. **消除硬编码**：将错误消息和状态码从代码中提取到配置文件
2. **统一管理**：通过配置文件集中管理所有错误消息
3. **类型安全**：使用常量类提供类型安全的错误码引用
4. **参数化支持**：支持动态参数替换的错误消息
5. **向后兼容**：保持现有异常处理机制不变

## 核心组件

### 1. 错误配置文件 (config/errors.php)

按模块组织错误消息和状态码：

```php
return [
    'Lead' => [
        'not_found' => [
            'message' => '线索不存在',
            'code' => 404,
        ],
        'company_already_exists' => [
            'message' => '该公司已存在线索记录',
            'code' => 409,
        ],
        'operation_not_supported' => [
            'message' => '不支持的操作类型: :type',
            'code' => 400,
        ],
    ],
];
```

### 2. 错误码常量类 (app/Constants/ErrorCode.php)

提供类型安全的错误码引用：

```php
class ErrorCode
{
    public const LEAD_NOT_FOUND = 'Lead.not_found';
    public const LEAD_COMPANY_ALREADY_EXISTS = 'Lead.company_already_exists';
    // ...
}
```

### 3. 扩展的 BusinessException 类

支持通过错误码创建异常：

```php
// 基本用法
throw BusinessException::fromErrorCode(ErrorCode::LEAD_NOT_FOUND);

// 带参数的用法
throw BusinessException::fromErrorCode(
    ErrorCode::LEAD_OPERATION_NOT_SUPPORTED,
    ['type' => 'invalid_operation']
);

// 向后兼容
throw new BusinessException('自定义错误消息', 422);
```

## 使用方法

### 1. 基本错误处理

```php
// 重构前
if (!$lead) {
    throw new BusinessException('线索不存在', 404);
}

// 重构后
if (!$lead) {
    throw BusinessException::fromErrorCode(ErrorCode::LEAD_NOT_FOUND);
}
```

### 2. 参数化错误消息

```php
// 重构前
throw new BusinessException("不支持的操作类型: {$operation['type']}", 400);

// 重构后
throw BusinessException::fromErrorCode(
    ErrorCode::LEAD_OPERATION_NOT_SUPPORTED,
    ['type' => $operation['type']]
);
```

### 3. 添加新的错误类型

1. 在 `config/errors.php` 中添加错误配置：

```php
'Lead' => [
    'new_error' => [
        'message' => '新的错误消息',
        'code' => 400,
    ],
],
```

2. 在 `ErrorCode` 类中添加常量：

```php
public const LEAD_NEW_ERROR = 'Lead.new_error';
```

3. 在代码中使用：

```php
throw BusinessException::fromErrorCode(ErrorCode::LEAD_NEW_ERROR);
```

## 重构收益

### 1. 可维护性提升

- 错误消息集中管理，修改更容易
- 避免重复的错误消息定义
- 统一的错误码和状态码管理

### 2. 代码质量改善

- 消除硬编码，提高代码可读性
- 类型安全的错误码引用
- IDE 自动补全支持

### 3. 扩展性增强

- 支持参数化错误消息
- 易于添加新的错误类型
- 支持国际化扩展

### 4. 一致性保证

- 统一的错误处理模式
- 一致的 API 响应格式
- 标准化的错误码体系

## 最佳实践

### 1. 错误码命名规范

- 使用模块前缀：`MODULE_ERROR_TYPE`
- 使用描述性名称：`LEAD_NOT_FOUND` 而不是 `LEAD_ERROR_1`
- 保持一致的命名风格

### 2. 错误消息编写

- 使用用户友好的语言
- 避免技术术语
- 提供明确的错误原因

### 3. 状态码选择

- 遵循 HTTP 状态码标准
- 业务逻辑错误使用 4xx
- 系统错误使用 5xx

### 4. 参数化消息

- 使用 `:param` 格式定义参数
- 避免在消息中暴露敏感信息
- 保持参数名称的一致性

## 测试策略

### 1. 单元测试

- 测试错误码创建异常
- 测试参数化消息替换
- 测试向后兼容性

### 2. 集成测试

- 测试 API 错误响应格式
- 测试异常处理器集成
- 测试日志记录功能

## 迁移指南

### 1. 识别硬编码

搜索项目中的硬编码错误：

```bash
grep -r "throw new BusinessException" app/
```

### 2. 逐步重构

- 先添加错误配置和常量
- 逐个文件重构硬编码
- 运行测试确保功能正常

### 3. 验证重构

- 检查所有错误消息是否正确
- 验证状态码是否一致
- 确认 API 响应格式正确

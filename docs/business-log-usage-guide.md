# 业务日志组件使用指南

## 概述

业务日志组件为 Laravel CRM API 项目提供了统一的业务操作日志记录功能，支持 trace_id 链路追踪、自动日志轮转和敏感信息过滤。

## 功能特性

- ✅ **独立日志通道**：使用专门的 `business` 通道记录业务日志
- ✅ **自动日志轮转**：按日期分割日志文件，格式为 `business-YYYY-MM-DD.log`
- ✅ **链路追踪**：每个请求自动生成 `trace_id`，支持分布式追踪
- ✅ **敏感信息过滤**：自动过滤密码、token 等敏感信息
- ✅ **JSON 格式**：结构化日志便于分析和查询
- ✅ **自动清理**：支持定时清理过期日志文件

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 业务日志配置
BUSINESS_LOG_LEVEL=info
BUSINESS_LOG_RETENTION_DAYS=14
```

### 日志通道配置

在 `config/logging.php` 中已自动配置 `business` 通道：

```php
'business' => [
    'driver' => 'daily',
    'path' => storage_path('logs/business.log'),
    'level' => env('BUSINESS_LOG_LEVEL', 'info'),
    'days' => env('BUSINESS_LOG_RETENTION_DAYS', 14),
    'replace_placeholders' => true,
    'tap' => [App\Logging\BusinessLogFormatter::class],
],
```

## 使用方法

### 1. 使用 Facade（推荐）

```php
use App\Facades\BusinessLog;

// 记录 info 级别日志
BusinessLog::info([
    'message' => '用户登录成功',
    'module' => 'auth',
    'action' => 'login',
    'user_id' => 123,
    'ip' => '*************'
]);

// 记录线索操作
BusinessLog::info([
    'message' => '创建线索',
    'module' => 'Lead',
    'action' => 'create',
    'lead_id' => 456,
    'data' => [
        'company_name' => '示例公司',
        'status' => 1
    ]
]);

// 记录不同级别的日志
BusinessLog::debug(['message' => '调试信息', 'debug_data' => 'test']);
BusinessLog::warning(['message' => '警告信息', 'warning_type' => 'validation']);
BusinessLog::error(['message' => '错误信息', 'error_code' => 'DB_ERROR']);
BusinessLog::critical(['message' => '严重错误', 'system_status' => 'down']);

// 记录异常
BusinessLog::logException('业务处理失败', $exception, [
    'module' => 'Lead',
    'action' => 'process',
    'lead_id' => 789
]);
```

### 2. 依赖注入使用

```php
use App\Services\BusinessLogService;

class YourController extends Controller
{
    public function __construct(private BusinessLogService $businessLog)
    {
    }

    public function someAction()
    {
        $this->businessLog->info([
            'message' => '查看线索',
            'module' => 'Lead',
            'action' => 'view',
            'lead_id' => $leadId
        ]);
    }
}
```

### 3. 手动设置 trace_id

```php
// 设置自定义 trace_id
BusinessLog::setTraceId('custom-trace-id-123');

// 获取当前 trace_id
$traceId = BusinessLog::getTraceId();

// 使用自定义 trace_id 记录日志
BusinessLog::info(['message' => '自定义追踪的操作']);
```

## 日志格式

业务日志采用 JSON 格式，包含以下字段：

```json
{
    "timestamp": "2024-01-15T10:30:45.123456Z",
    "level": "INFO",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000",
    "message": "创建线索",
    "context": {
        "module": "Lead",
        "action": "create",
        "lead_id": 123,
        "data": {
            "company_full_name": "示例公司",
            "company_short_name": "示例",
            "password": "[REDACTED]"
        }
    },
    "extra": {},
    "request": {
        "method": "POST",
        "url": "https://api.example.com/api/leads",
        "ip": "*************",
        "user_agent": "Mozilla/5.0..."
    },
    "user": {
        "id": 1,
        "email": "<EMAIL>"
    }
}
```

## 日志级别支持

业务日志组件支持以下标准日志级别：

- **debug**: 调试信息，详细的程序执行信息
- **info**: 一般信息，正常的业务操作记录
- **warning**: 警告信息，需要注意但不影响正常运行
- **error**: 错误信息，出现错误但程序可以继续运行
- **critical**: 严重错误，可能导致程序无法正常运行

### 使用不同日志级别

```php
// Debug 级别 - 调试信息
BusinessLog::debug([
    'message' => '查询参数验证',
    'module' => 'validation',
    'params' => $request->all()
]);

// Info 级别 - 正常业务操作
BusinessLog::info([
    'message' => '用户登录成功',
    'module' => 'auth',
    'user_id' => 123
]);

// Warning 级别 - 警告信息
BusinessLog::warning([
    'message' => '数据库连接缓慢',
    'module' => 'database',
    'response_time' => 2.5
]);

// Error 级别 - 错误信息
BusinessLog::error([
    'message' => '第三方API调用失败',
    'module' => 'external_api',
    'api_name' => 'payment_gateway',
    'error_code' => 'TIMEOUT'
]);

// Critical 级别 - 严重错误
BusinessLog::critical([
    'message' => '数据库连接失败',
    'module' => 'database',
    'error' => 'Connection refused'
]);
```

## 推荐的日志结构

为了保持日志的一致性和可读性，建议使用以下结构：

### 线索操作 (Lead Operations)

```php
// 查询线索列表
BusinessLog::info([
    'message' => '查询线索列表',
    'module' => 'Lead',
    'action' => 'list',
    'filters' => $filters,
    'page' => $page,
    'per_page' => $perPage
]);

// 查看线索详情
BusinessLog::info([
    'message' => '查看线索详情',
    'module' => 'Lead',
    'action' => 'view',
    'lead_id' => $leadId
]);

// 创建线索
BusinessLog::info([
    'message' => '创建线索',
    'module' => 'Lead',
    'action' => 'create',
    'data' => $leadData
]);

// 更新线索
BusinessLog::info([
    'message' => '更新线索',
    'module' => 'Lead',
    'action' => 'update',
    'lead_id' => $leadId,
    'data' => $updateData
]);

// 删除线索
BusinessLog::info([
    'message' => '删除线索',
    'module' => 'Lead',
    'action' => 'delete',
    'lead_id' => $leadId
]);

// 批量操作
BusinessLog::info([
    'message' => '批量更新线索状态',
    'module' => 'Lead',
    'action' => 'batch_update_status',
    'lead_ids' => [1, 2, 3],
    'new_status' => 2,
    'total_count' => 3
]);
```

### 联系人操作 (Contact Operations)

```php
BusinessLog::info([
    'message' => '创建联系人',
    'module' => 'contact',
    'action' => 'create',
    'data' => $contactData
]);

BusinessLog::info([
    'message' => '更新联系人',
    'module' => 'contact',
    'action' => 'update',
    'contact_id' => $contactId,
    'data' => $updateData
]);

BusinessLog::info([
    'message' => '删除联系人',
    'module' => 'contact',
    'action' => 'delete',
    'contact_id' => $contactId
]);
```

### 认证操作 (Auth Operations)

```php
BusinessLog::info([
    'message' => '用户登录',
    'module' => 'auth',
    'action' => 'login',
    'user_id' => $userId,
    'ip' => $request->ip(),
    'user_agent' => $request->userAgent()
]);

BusinessLog::info([
    'message' => '用户登出',
    'module' => 'auth',
    'action' => 'logout',
    'user_id' => $userId
]);

BusinessLog::info([
    'message' => '用户注册',
    'module' => 'auth',
    'action' => 'register',
    'email' => $email,
    'name' => $name
]);
```

### 系统操作 (System Operations)

```php
BusinessLog::info([
    'message' => '系统维护开始',
    'module' => 'system',
    'action' => 'maintenance_start',
    'duration' => '30 minutes',
    'reason' => 'database upgrade'
]);

BusinessLog::info([
    'message' => '清理缓存',
    'module' => 'system',
    'action' => 'cache_clear',
    'type' => 'all',
    'size_freed' => '256MB'
]);

BusinessLog::info([
    'message' => '配置更新',
    'module' => 'system',
    'action' => 'config_update',
    'key' => 'app.debug',
    'old_value' => true,
    'new_value' => false
]);
```

## 中间件集成

业务日志功能已集成到 `ApiLoggingMiddleware` 中，会自动：

1. 为每个请求生成唯一的 `trace_id`
2. 记录详细的 API 请求日志
3. 在响应头中添加 `X-Request-ID`
4. 捕获和记录异常信息
5. 提供数据库查询统计和性能监控

**注意**：业务日志通过 `BusinessLogService` 独立记录到 `business` 日志通道，与 API 请求日志分离存储。

## 日志管理

### 查看日志文件

```bash
# 查看今天的业务日志
tail -f storage/logs/business-$(date +%Y-%m-%d).log

# 查看最近的业务日志
ls -la storage/logs/business-*.log
```

### 清理过期日志

```bash
# 清理 14 天前的日志（默认）
php artisan logs:clean-business

# 清理 7 天前的日志
php artisan logs:clean-business --days=7

# 预览模式（不实际删除）
php artisan logs:clean-business --dry-run
```

### 定时清理配置

在 `app/Console/Kernel.php` 中添加定时任务：

```php
protected function schedule(Schedule $schedule)
{
    // 每天凌晨 2 点清理过期的业务日志
    $schedule->command('logs:clean-business')->dailyAt('02:00');
}
```

## 最佳实践

### 1. 合理使用日志级别

```php
// 正常业务操作使用 info 级别
BusinessLog::info([
    'message' => '创建线索',
    'module' => 'Lead',
    'action' => 'create',
    'lead_id' => $leadId,
    'data' => $data
]);

// 警告情况使用 warning 级别
BusinessLog::warning([
    'message' => '数据验证警告',
    'module' => 'validation',
    'warning_type' => 'format_issue',
    'field' => 'mobile',
    'value' => $mobileNumber
]);

// 错误情况使用 error 级别
BusinessLog::error([
    'message' => '业务处理失败',
    'module' => 'Lead',
    'action' => 'process',
    'error_type' => 'database_error'
]);
```

### 2. 避免记录敏感信息

组件会自动过滤常见的敏感字段，但建议在记录前手动清理：

```php
$safeData = collect($data)->except(['password', 'secret_key'])->toArray();
BusinessLog::info([
    'message' => '更新线索',
    'module' => 'Lead',
    'action' => 'update',
    'lead_id' => $leadId,
    'data' => $safeData
]);
```

### 3. 使用有意义的消息和结构化数据

```php
// ❌ 不好的做法
BusinessLog::info(['message' => 'update']);

// ✅ 好的做法
BusinessLog::info([
    'message' => '更新线索基本信息',
    'module' => 'Lead',
    'action' => 'update_basic_info',
    'lead_id' => $leadId,
    'updated_fields' => ['company_name', 'status']
]);
```

### 4. 记录关键业务节点

```php
// 业务流程开始
BusinessLog::info([
    'message' => '开始处理线索转换',
    'module' => 'Lead',
    'action' => 'process_start',
    'lead_id' => $leadId,
    'process_type' => 'conversion'
]);

// 关键步骤
BusinessLog::info([
    'message' => '线索数据验证通过',
    'module' => 'Lead',
    'action' => 'validation_passed',
    'lead_id' => $leadId,
    'validation_rules' => ['required_fields', 'format_check']
]);

// 业务流程结束
BusinessLog::info([
    'message' => '线索转换处理完成',
    'module' => 'Lead',
    'action' => 'process_complete',
    'lead_id' => $leadId,
    'result' => $result,
    'duration_ms' => $processingTime
]);
```

### 5. 使用统一的数据结构

建议在项目中定义统一的日志数据结构：

```php
// 推荐的基础结构
$logData = [
    'message' => '操作描述',           // 必需：人类可读的操作描述
    'module' => 'module_name',        // 必需：模块名称
    'action' => 'action_name',        // 必需：具体操作
    'user_id' => $userId,             // 可选：操作用户ID
    'resource_id' => $resourceId,     // 可选：操作的资源ID
    'data' => $operationData,         // 可选：操作相关数据
    'duration_ms' => $duration,       // 可选：操作耗时
    'ip' => $request->ip(),           // 可选：客户端IP
];

BusinessLog::info($logData);
```

## 故障排查

### 1. 日志文件权限问题

```bash
# 确保日志目录有写权限
chmod -R 755 storage/logs
chown -R www-data:www-data storage/logs
```

### 2. 日志格式问题

检查 `BusinessLogFormatter` 是否正确配置：

```php
// 在 config/logging.php 中确认
'tap' => [App\Logging\BusinessLogFormatter::class],
```

### 3. trace_id 不一致

确保在同一个请求周期内使用相同的 `BusinessLogService` 实例：

```php
// 使用 Facade 确保单例
BusinessLog::setTraceId($traceId);
```

## 性能考虑

1. **异步日志记录**：考虑使用队列异步处理大量日志
2. **日志级别控制**：生产环境可设置更高的日志级别
3. **定期清理**：及时清理过期日志文件避免磁盘空间不足
4. **监控磁盘使用**：监控日志目录的磁盘使用情况

## 扩展开发

### 自定义日志格式化器

```php
class CustomBusinessLogFormatter extends BusinessLogFormatter
{
    public function format(LogRecord $record): string
    {
        // 自定义格式化逻辑
        return parent::format($record);
    }
}
```

### 添加新的操作类型

```php
// 在 BusinessLogService 中添加新方法
public function logCustomOperation(string $action, array $data = []): void
{
    $this->logOperation('custom_module', "自定义操作: {$action}", [
        'module' => 'custom',
        'action' => $action,
        'data' => $data,
    ]);
}
```

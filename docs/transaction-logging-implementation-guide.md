# TransactionManager 事务日志完整实施方案

## 概述

基于现有的 TransactionManager 事务回调机制，实现了完整的事务生命周期日志记录功能，提供结构化的事务日志、链路追踪和问题排查支持。

## 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    事务日志架构                              │
├─────────────────────────────────────────────────────────────┤
│  EventServiceProvider                                       │
│  ├── registerTransactionCallbacks()                        │
│  └── registerDatabaseTransactionEvents()                   │
├─────────────────────────────────────────────────────────────┤
│  TransactionCallbackManager                                │
│  ├── registerTransactionLogCallbacks()                     │
│  ├── registerCacheManagementCallbacks()                    │
│  ├── registerPerformanceMonitoringCallbacks()              │
│  └── registerErrorHandlingCallbacks()                      │
├─────────────────────────────────────────────────────────────┤
│  TransactionLogService                                      │
│  ├── logTransactionBegin()                                 │
│  ├── logTransactionCommit()                                │
│  └── logTransactionRollback()                              │
├─────────────────────────────────────────────────────────────┤
│  LeadTransactionLogService                                 │
│  ├── logLeadCreateBegin/Commit()                           │
│  ├── logLeadUpdateBegin/Commit()                           │
│  └── logLeadDeleteBegin/Commit()                           │
└─────────────────────────────────────────────────────────────┘
```

### 日志流向

```
TransactionManager → Callbacks → TransactionLogService → BusinessLog/Log
                              ↓
                    LeadTransactionLogService → 业务级别日志
```

## 实施的功能

### ✅ 1. 事务生命周期日志

#### 1.1 before_begin 回调
- **触发时机**：事务开始前
- **记录内容**：
  - 事务ID（自动生成）
  - trace_id（链路追踪）
  - 操作类型（自动检测）
  - 用户ID、时间戳
  - 内存使用情况

#### 1.2 after_commit 回调
- **触发时机**：事务成功提交后
- **记录内容**：
  - 执行时长（毫秒级精度）
  - 影响的数据实体
  - 执行结果摘要
  - 性能指标

#### 1.3 after_rollback 回调
- **触发时机**：事务回滚时
- **记录内容**：
  - 失败原因和异常信息
  - 错误分类和严重程度
  - 建议的处理动作
  - 堆栈跟踪（清理后）

### ✅ 2. 结构化日志格式

#### 2.1 事务日志格式（transaction.log）

```json
{
    "timestamp": "2024-01-15T10:30:45.123456Z",
    "level": "INFO",
    "message": "事务提交成功",
    "transaction_id": "tx_550e8400-e29b-41d4-a716-446655440000",
    "trace_id": "550e8400-e29b-41d4-a716-446655440001",
    "operation_type": "lead_create",
    "user_id": 123,
    "connection": "mysql",
    "performance": {
        "duration_ms": 45.67,
        "memory_usage_mb": 12.5,
        "peak_memory_mb": 15.2
    },
    "request": {
        "method": "POST",
        "path": "api/leads",
        "ip": "*************",
        "user_agent": "Mozilla/5.0..."
    },
    "context": {
        "module": "Lead",
        "entity_type": "crm_lead"
    }
}
```

#### 2.2 业务日志格式（business.log）

```json
{
    "timestamp": "2024-01-15T10:30:45.123456Z",
    "level": "INFO",
    "trace_id": "550e8400-e29b-41d4-a716-446655440001",
    "message": "线索创建成功",
    "context": {
        "module": "Lead",
        "action": "create",
        "transaction_id": "tx_550e8400-e29b-41d4-a716-446655440000",
        "lead_id": 456,
        "company_full_name": "示例公司有限公司",
        "company_short_name": "示例公司",
        "region": 1,
        "creator_id": 123,
        "business_impact": {
            "new_lead_count": 1,
            "affected_tables": ["crm_lead"]
        }
    }
}
```

### ✅ 3. 自动回调注册

#### 3.1 在 EventServiceProvider 中注册

```php
private function registerTransactionCallbacks(): void
{
    try {
        $callbackManager = app(\App\Services\Transaction\TransactionCallbackManager::class);
        $callbackManager->registerLeadCallbacks();
        
        Log::debug('事务回调管理器注册成功');
    } catch (\Exception $e) {
        Log::error('事务回调管理器注册失败', [
            'error' => $e->getMessage(),
        ]);
    }
}
```

#### 3.2 服务提供者注册

在 `config/app.php` 中添加：
```php
App\Providers\TransactionServiceProvider::class,
```

### ✅ 4. 业务集成支持

#### 4.1 线索管理业务集成

- **crm_lead 表**：记录线索创建、更新、删除操作
- **crm_lead_user_relation 表**：记录用户关联操作
- **crm_lead_contact_relation 表**：记录联系人关联操作

#### 4.2 操作类型自动检测

```php
private function detectOperationType(): string
{
    $request = app('request');
    $method = $request->method();
    $path = $request->path();

    if (str_contains($path, 'leads')) {
        return match ($method) {
            'POST' => 'lead_create',
            'PUT', 'PATCH' => 'lead_update',
            'DELETE' => 'lead_delete',
            'GET' => 'lead_read',
            default => 'lead_unknown',
        };
    }
    
    // 支持其他业务模块...
}
```

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加：

```env
# 事务日志配置
TRANSACTION_LOG_LEVEL=info
TRANSACTION_LOG_RETENTION_DAYS=30

# 业务日志配置（已存在）
BUSINESS_LOG_LEVEL=info
BUSINESS_LOG_RETENTION_DAYS=14
```

### 2. 日志通道配置

在 `config/logging.php` 中已配置：

```php
'transaction' => [
    'driver' => 'daily',
    'path' => storage_path('logs/transaction.log'),
    'level' => env('TRANSACTION_LOG_LEVEL', 'info'),
    'days' => env('TRANSACTION_LOG_RETENTION_DAYS', 30),
    'tap' => [App\Logging\TransactionLogFormatter::class],
],
```

## 使用方法

### 1. 自动日志记录

事务日志会自动记录，无需在业务代码中手动调用：

```php
// 在 LeadService 中
public function createLead(LeadCreateDTO $dto): Lead
{
    // 事务开始时自动记录 before_begin 日志
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        $lead = $this->leadRepository->create($dto->toArray());
        
        // 事务成功时自动记录 after_commit 日志
        // 事务失败时自动记录 after_rollback 日志
        return $lead;
    });
}
```

### 2. 业务级别日志记录

如需更详细的业务日志，可以使用 LeadTransactionLogService：

```php
public function createLeadWithDetailedLogging(LeadCreateDTO $dto): Lead
{
    $leadLogService = app(LeadTransactionLogService::class);
    
    return $this->transactionManager->executeInTransaction(function () use ($dto, $leadLogService) {
        // 记录业务开始日志
        $transactionId = $leadLogService->logLeadCreateBegin($dto);
        
        try {
            $lead = $this->leadRepository->create($dto->toArray());
            
            // 记录业务成功日志
            $leadLogService->logLeadCreateCommit($transactionId, $lead);
            
            return $lead;
        } catch (\Exception $e) {
            // 记录业务失败日志
            $leadLogService->logLeadCreateRollback($transactionId, $e, $dto);
            throw $e;
        }
    });
}
```

## 问题排查支持

### 1. 按 trace_id 查询完整链路

```bash
# 查询特定请求的所有日志
grep "550e8400-e29b-41d4-a716-446655440001" storage/logs/transaction-*.log
grep "550e8400-e29b-41d4-a716-446655440001" storage/logs/business-*.log
```

### 2. 按操作类型过滤日志

```bash
# 查询所有线索创建操作
grep "lead_create" storage/logs/transaction-*.log

# 查询所有事务失败
grep "rollback" storage/logs/transaction-*.log
```

### 3. 按时间范围查询

```bash
# 查询今天的事务日志
tail -f storage/logs/transaction-$(date +%Y-%m-%d).log

# 查询最近1小时的错误
grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" storage/logs/transaction-*.log | grep ERROR
```

### 4. 性能分析

```bash
# 查询执行时间超过1秒的事务
grep "duration_ms" storage/logs/transaction-*.log | awk -F'"duration_ms":' '{print $2}' | awk -F',' '{if($1>1000) print}'
```

## 监控和告警

### 1. 错误分类

- **low**：数据约束错误、重复数据等
- **medium**：死锁、业务逻辑错误等
- **high**：数据库连接失败、系统级错误等

### 2. 自动告警

对于 `high` 级别的错误，系统会：
- 记录 CRITICAL 级别日志
- 标记 `requires_immediate_attention: true`
- 可扩展集成邮件、短信、钉钉等告警方式

### 3. 性能监控

自动记录：
- 事务执行时长
- 内存使用情况
- 事务成功率
- 操作类型分布

## 扩展建议

### 1. 集成外部监控系统

```php
// 可以扩展发送到 Prometheus、Grafana 等
$this->transactionManager->registerCallback('after_commit', function () {
    $this->metricsCollector->increment('transaction.lead.success');
});
```

### 2. 集成告警系统

```php
// 可以扩展集成钉钉、企业微信等
private function sendCriticalErrorAlert(\Exception $exception): void
{
    // 发送到钉钉群
    $this->dingTalkService->sendAlert([
        'title' => '严重事务错误',
        'content' => $exception->getMessage(),
    ]);
}
```

### 3. 数据分析支持

- 导出日志到数据仓库
- 生成事务执行报表
- 分析业务操作模式
- 优化数据库性能

## 验证测试

### 1. 功能测试

```bash
# 测试事务成功场景
curl -X POST http://localhost/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Request-ID: test-trace-001" \
  -d '{"company_full_name":"测试公司","company_short_name":"测试","region":1}'

# 检查日志
tail -f storage/logs/transaction-$(date +%Y-%m-%d).log
tail -f storage/logs/business-$(date +%Y-%m-%d).log
```

### 2. 异常测试

```bash
# 测试重复数据异常
curl -X POST http://localhost/api/leads \
  -H "Content-Type: application/json" \
  -H "X-Request-ID: test-trace-002" \
  -d '{"company_full_name":"测试公司","company_short_name":"测试","region":1}'

# 检查回滚日志
grep "test-trace-002" storage/logs/transaction-*.log
```

## 总结

### ✅ 已实现功能

1. **完整的事务生命周期日志记录**
2. **结构化的 JSON 日志格式**
3. **自动的 trace_id 链路追踪**
4. **业务级别的详细日志**
5. **错误分类和告警机制**
6. **性能监控和分析**
7. **与现有业务的无缝集成**

### 🚀 即时收益

1. **问题排查效率提升**：通过 trace_id 快速定位问题
2. **性能监控能力**：实时监控事务执行性能
3. **业务洞察**：了解业务操作模式和频率
4. **运维效率**：自动化的错误分类和告警

### 📈 长期价值

1. **数据驱动优化**：基于日志数据优化业务流程
2. **预防性维护**：提前发现潜在问题
3. **合规审计**：完整的操作审计日志
4. **系统演进**：为系统扩展提供数据支撑

这个方案可以直接应用到当前的 CRM 项目中，无需修改现有的业务逻辑代码，通过事务回调机制自动提供完整的日志记录功能。

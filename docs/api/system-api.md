# 系统监控模块 API 接口文档

## 概述

系统监控模块提供了日志系统健康检查、容错日志服务状态监控、健康历史查询等功能。这些接口主要用于系统运维和监控，帮助及时发现和解决系统问题。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`
- **模块前缀**: `/system`

## 健康状态说明

### 系统健康状态
| 状态 | 描述 | HTTP状态码 |
|------|------|-----------|
| healthy | 系统健康，所有组件正常工作 | 200 |
| degraded | 系统降级，部分功能受影响但仍可用 | 200 |
| unhealthy | 系统不健康，服务不可用 | 503 |
| error | 健康检查失败 | 500 |

## 接口列表

### 1. 获取日志系统健康状态

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/system/log-health`
- **接口描述**: 获取日志系统的整体健康状态，包括各个组件的状态信息

#### 请求参数
无需参数

#### 响应格式

##### 成功响应 (200 OK) - 系统健康
```json
{
    "overall_status": "healthy",
    "message": "日志系统运行正常",
    "components": {
        "database": {
            "status": "healthy",
            "response_time_ms": 15,
            "last_check": "2025-07-31T16:30:00Z"
        },
        "file_system": {
            "status": "healthy",
            "disk_usage_percent": 45,
            "available_space_gb": 120
        },
        "cache": {
            "status": "healthy",
            "hit_rate_percent": 85,
            "memory_usage_mb": 256
        }
    },
    "metrics": {
        "total_logs_today": 15420,
        "error_rate_percent": 0.5,
        "average_response_time_ms": 25
    },
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 降级响应 (200 OK) - 系统降级
```json
{
    "overall_status": "degraded",
    "message": "日志系统部分功能降级",
    "components": {
        "database": {
            "status": "healthy",
            "response_time_ms": 15
        },
        "file_system": {
            "status": "degraded",
            "disk_usage_percent": 85,
            "warning": "磁盘空间不足"
        },
        "cache": {
            "status": "unhealthy",
            "error": "缓存连接失败"
        }
    },
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (503 Service Unavailable) - 系统不健康
```json
{
    "overall_status": "unhealthy",
    "message": "日志系统不可用",
    "components": {
        "database": {
            "status": "unhealthy",
            "error": "数据库连接失败"
        }
    },
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "overall_status": "error",
    "message": "Health check failed",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 系统健康或降级但仍可用
- **503**: 系统不健康，服务不可用
- **500**: 健康检查失败

#### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/system/log-health" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 2. 获取容错日志服务状态

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/system/log-health/resilient-status`
- **接口描述**: 获取容错日志服务的详细状态信息，包括熔断器状态、重试机制等

#### 请求参数
无需参数

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "status": "ok",
    "resilient_logger": {
        "circuit_breaker": {
            "state": "closed",
            "failure_count": 0,
            "last_failure_time": null,
            "next_attempt_time": null
        },
        "retry_mechanism": {
            "enabled": true,
            "max_retries": 3,
            "retry_delay_ms": 1000
        },
        "async_processing": {
            "enabled": true,
            "queue_size": 0,
            "processed_today": 1250
        },
        "fallback_storage": {
            "enabled": true,
            "local_file_path": "/tmp/api_logs_fallback.log",
            "entries_count": 0
        }
    },
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "status": "error",
    "message": "Failed to get resilient logger status",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 获取状态成功
- **500**: 获取状态失败

#### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/system/log-health/resilient-status" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 3. 获取健康历史

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/system/log-health/history`
- **接口描述**: 获取指定时间范围内的系统健康历史记录

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| hours | integer | 否 | 24 | 查询时间范围（小时），范围：1-168 |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "time_range": {
        "hours": 24,
        "start_time": "2025-07-30T16:30:00Z",
        "end_time": "2025-07-31T16:30:00Z"
    },
    "summary": {
        "total_checks": 144,
        "healthy_count": 140,
        "degraded_count": 3,
        "unhealthy_count": 1,
        "uptime_percent": 97.2
    },
    "history": [
        {
            "timestamp": "2025-07-31T16:00:00Z",
            "status": "healthy",
            "response_time_ms": 25,
            "components_healthy": 3,
            "components_total": 3
        },
        {
            "timestamp": "2025-07-31T15:00:00Z",
            "status": "degraded",
            "response_time_ms": 45,
            "components_healthy": 2,
            "components_total": 3,
            "issues": ["cache_connection_slow"]
        }
    ],
    "trends": {
        "average_response_time_ms": 28,
        "error_rate_percent": 2.8,
        "most_common_issues": [
            "cache_connection_slow",
            "disk_space_warning"
        ]
    }
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "status": "error",
    "message": "Failed to get health history",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 获取历史成功
- **500**: 获取历史失败

#### cURL 请求示例

##### 获取默认24小时历史
```bash
curl -X GET "http://127.0.0.1:8000/api/system/log-health/history" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 获取指定时间范围历史
```bash
curl -X GET "http://127.0.0.1:8000/api/system/log-health/history?hours=72" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 4. 手动触发健康检查

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/system/log-health/check`
- **接口描述**: 手动触发一次完整的系统健康检查

#### 请求参数
无需参数

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "status": "completed",
    "message": "Health check triggered successfully",
    "result": {
        "overall_status": "healthy",
        "components": {
            "database": {
                "status": "healthy",
                "response_time_ms": 15
            },
            "file_system": {
                "status": "healthy",
                "disk_usage_percent": 45
            },
            "cache": {
                "status": "healthy",
                "hit_rate_percent": 85
            }
        }
    },
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "status": "error",
    "message": "Failed to trigger health check",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 健康检查触发成功
- **500**: 健康检查触发失败

#### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/system/log-health/check" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 5. 重置熔断器

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/system/log-health/reset-circuit-breaker`
- **接口描述**: 重置日志系统的熔断器状态，清除失败计数和熔断状态

#### 请求参数
无需参数

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "status": "success",
    "message": "Circuit breaker reset successfully",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "status": "error",
    "message": "Failed to reset circuit breaker",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 熔断器重置成功
- **500**: 熔断器重置失败

#### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/system/log-health/reset-circuit-breaker" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 6. 测试日志记录功能

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/system/log-health/test-logging`
- **接口描述**: 测试日志记录功能，验证日志系统是否正常工作

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| async | boolean | 否 | false | 是否使用异步日志记录 |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "status": "success",
    "message": "Test log recorded successfully",
    "test_data": {
        "test_id": "64f7a8b9c1234",
        "timestamp": "2025-07-31T16:30:00Z",
        "test_type": "manual_test",
        "user_agent": "curl/7.68.0",
        "ip": "127.0.0.1"
    },
    "async": false,
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 失败响应 (200 OK)
```json
{
    "status": "failed",
    "message": "Test log recording failed",
    "test_data": {
        "test_id": "64f7a8b9c1234",
        "timestamp": "2025-07-31T16:30:00Z",
        "test_type": "manual_test",
        "user_agent": "curl/7.68.0",
        "ip": "127.0.0.1"
    },
    "async": false,
    "timestamp": "2025-07-31T16:30:00Z"
}
```

##### 错误响应 (500 Internal Server Error)
```json
{
    "status": "error",
    "message": "Test logging failed",
    "error": "具体错误信息",
    "timestamp": "2025-07-31T16:30:00Z"
}
```

#### 状态码说明
- **200**: 测试执行完成（成功或失败）
- **500**: 测试执行异常

#### cURL 请求示例

##### 同步日志测试
```bash
curl -X POST "http://127.0.0.1:8000/api/system/log-health/test-logging" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 异步日志测试
```bash
curl -X POST "http://127.0.0.1:8000/api/system/log-health/test-logging?async=true" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

## 接口测试

### 测试环境准备

1. **启动本地服务器**
```bash
php artisan serve
```

2. **确保日志系统配置正确**
```bash
# 检查日志目录权限
ls -la storage/logs/

# 检查缓存配置
php artisan config:cache
```

### 完整测试流程

#### 1. 测试基础健康检查
```bash
# 获取系统健康状态
curl -X GET "http://127.0.0.1:8000/api/system/log-health" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含系统整体健康状态

#### 2. 测试容错服务状态
```bash
# 获取容错日志服务状态
curl -X GET "http://127.0.0.1:8000/api/system/log-health/resilient-status" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含熔断器和重试机制状态

#### 3. 测试健康历史查询
```bash
# 获取24小时健康历史
curl -X GET "http://127.0.0.1:8000/api/system/log-health/history" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含历史健康数据和趋势分析

#### 4. 测试手动健康检查
```bash
# 触发手动健康检查
curl -X POST "http://127.0.0.1:8000/api/system/log-health/check" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含实时健康检查结果

#### 5. 测试日志记录功能
```bash
# 测试同步日志记录
curl -X POST "http://127.0.0.1:8000/api/system/log-health/test-logging" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，显示日志记录测试结果

#### 6. 测试熔断器重置
```bash
# 重置熔断器状态
curl -X POST "http://127.0.0.1:8000/api/system/log-health/reset-circuit-breaker" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，确认熔断器重置成功

### 监控场景测试

#### 1. 系统正常场景
- 所有组件状态为 `healthy`
- 响应时间在正常范围内
- 错误率低于阈值

#### 2. 系统降级场景
- 部分组件状态为 `degraded`
- 系统仍可用但性能下降
- 触发告警但不影响核心功能

#### 3. 系统异常场景
- 关键组件状态为 `unhealthy`
- 系统不可用，返回503状态码
- 需要立即处理

#### 4. 熔断器测试场景
- 模拟连续失败触发熔断
- 验证熔断器状态变化
- 测试熔断器重置功能

## 使用建议

### 1. 监控集成
- 将健康检查接口集成到监控系统
- 设置合适的检查频率（建议1-5分钟）
- 配置告警规则和通知机制

### 2. 运维操作
- 定期查看健康历史，分析系统趋势
- 在系统异常时使用手动健康检查确认状态
- 必要时重置熔断器恢复服务

### 3. 性能优化
- 根据健康检查结果优化系统配置
- 监控响应时间和错误率趋势
- 及时处理磁盘空间、内存等资源问题

### 4. 故障排查
- 使用测试日志功能验证日志系统
- 查看容错服务状态了解系统保护机制
- 结合健康历史分析问题发生时间和模式

## 注意事项

1. **权限控制**: 生产环境建议添加认证中间件保护这些接口
2. **频率限制**: 避免过于频繁的健康检查影响系统性能
3. **数据保留**: 健康历史数据有保留期限，及时备份重要数据
4. **告警配置**: 合理设置告警阈值，避免误报和漏报
5. **依赖服务**: 健康检查依赖缓存和数据库，确保这些服务正常

## 更新日志

- **v1.0.0** (2025-07-31): 初始版本，包含基础健康检查和监控功能
- **v1.1.0** (2025-07-31): 添加容错机制和熔断器功能

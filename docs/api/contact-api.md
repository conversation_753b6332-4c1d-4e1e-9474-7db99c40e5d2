# 联系人模块 API 接口文档

## 概述

联系人模块提供了完整的联系人管理功能，包括联系人的创建、查询、更新、删除以及搜索等。所有接口都经过数据库操作优化，支持查询缓存和事务管理。

⚠️ **开发状态**: 当前联系人模块的后端逻辑（Repository、Service、Resource）已完成，但 HTTP 接口层（Controller）尚未实现。本文档描述的是计划中的接口设计。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`

## 数据字典

### 性别 (gender)
| 值 | 标签 |
|----|------|
| 男 | 男性 |
| 女 | 女性 |

## 接口列表

### 1. 获取联系人列表

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/contacts`
- **接口描述**: 获取联系人分页列表，支持多种筛选条件和排序

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 15 | 每页数量 (1-100) |
| name | string | 否 | - | 姓名模糊搜索 |
| mobile | string | 否 | - | 手机号模糊搜索 |
| email | string | 否 | - | 邮箱模糊搜索 |
| department | string | 否 | - | 部门模糊搜索 |
| position | string | 否 | - | 职位模糊搜索 |
| gender | string | 否 | - | 性别筛选 (男/女) |
| sort_by | string | 否 | created_at | 排序字段 |
| sort_direction | string | 否 | desc | 排序方向 (asc/desc) |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "data": [
            {
                "id": 1,
                "name": "张三",
                "gender": "男",
                "age": 35,
                "mobile": "13800138000",
                "telephone": "010-12345678",
                "email": "<EMAIL>",
                "wx": "zhangsan_wx",
                "department": "销售部",
                "position": "销售经理",
                "remark": "重要联系人",
                "created_at": "2025-07-29 14:30:00",
                "updated_at": "2025-07-30 10:30:00",
                "leads": [
                    {
                        "id": 1,
                        "company_full_name": "北京科技有限公司"
                    }
                ]
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 50,
            "last_page": 4,
            "from": 1,
            "to": 15
        }
    }
}
```

##### 错误响应 (400 Bad Request)
```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": {
        "per_page": ["每页数量不能超过100"]
    }
}
```

#### 状态码说明
- **200**: 获取成功
- **400**: 请求参数错误
- **500**: 服务器内部错误

#### cURL 请求示例

##### 基础查询
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts?page=1&per_page=15" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 带筛选条件的查询
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts?page=1&per_page=10&name=张&department=销售&gender=男" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 自定义排序查询
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts?sort_by=name&sort_direction=asc" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 2. 获取联系人详情

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/contacts/{id}`
- **接口描述**: 根据联系人ID获取联系人详细信息

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 联系人ID |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "张三",
        "gender": "男",
        "age": 35,
        "mobile": "13800138000",
        "telephone": "010-12345678",
        "email": "<EMAIL>",
        "wx": "zhangsan_wx",
        "department": "销售部",
        "position": "销售经理",
        "remark": "重要联系人",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-30 10:30:00",
        "leads": [
            {
                "id": 1,
                "company_full_name": "北京科技有限公司"
            }
        ]
    }
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "联系人不存在"
}
```

#### 状态码说明
- **200**: 获取成功
- **404**: 联系人不存在
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 3. 创建联系人

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/contacts`
- **接口描述**: 创建新的联系人记录

#### 请求参数

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | string | 是 | 姓名 (最大100字符) | "张三" |
| gender | string | 否 | 性别 (男/女) | "男" |
| age | integer | 否 | 年龄 (1-150) | 35 |
| mobile | string | 否 | 手机号 (最大20字符，唯一) | "13800138000" |
| telephone | string | 否 | 座机号 (最大20字符) | "010-12345678" |
| email | string | 否 | 邮箱 (最大100字符) | "<EMAIL>" |
| wx | string | 否 | 微信号 (最大100字符) | "zhangsan_wx" |
| department | string | 否 | 部门 (最大100字符) | "销售部" |
| position | string | 否 | 职位 (最大100字符) | "销售经理" |
| remark | string | 否 | 备注 (最大1000字符) | "重要联系人" |

#### 响应格式

##### 成功响应 (201 Created)
```json
{
    "code": 201,
    "message": "创建成功",
    "data": {
        "id": 1,
        "name": "张三",
        "gender": "男",
        "age": 35,
        "mobile": "13800138000",
        "telephone": "010-12345678",
        "email": "<EMAIL>",
        "wx": "zhangsan_wx",
        "department": "销售部",
        "position": "销售经理",
        "remark": "重要联系人",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-29 14:30:00"
    }
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "mobile": ["该手机号已存在"],
        "email": ["邮箱格式不正确"]
    }
}
```

#### 状态码说明
- **201**: 创建成功
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/contacts" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "name": "张三",
    "gender": "男",
    "age": 35,
    "mobile": "13800138000",
    "telephone": "010-12345678",
    "email": "<EMAIL>",
    "wx": "zhangsan_wx",
    "department": "销售部",
    "position": "销售经理",
    "remark": "重要联系人"
  }'
```

---

### 4. 更新联系人

#### 接口信息
- **HTTP 方法**: PUT
- **URL 路径**: `/contacts/{id}`
- **接口描述**: 更新指定联系人的信息

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 联系人ID |

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| name | string | 否 | 姓名 (最大100字符) | "张三" |
| gender | string | 否 | 性别 (男/女) | "男" |
| age | integer | 否 | 年龄 (1-150) | 36 |
| mobile | string | 否 | 手机号 (最大20字符，唯一) | "13800138001" |
| telephone | string | 否 | 座机号 (最大20字符) | "010-12345679" |
| email | string | 否 | 邮箱 (最大100字符) | "<EMAIL>" |
| wx | string | 否 | 微信号 (最大100字符) | "zhangsan_new_wx" |
| department | string | 否 | 部门 (最大100字符) | "市场部" |
| position | string | 否 | 职位 (最大100字符) | "市场总监" |
| remark | string | 否 | 备注 (最大1000字符) | "已升职为市场总监" |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "name": "张三",
        "gender": "男",
        "age": 36,
        "mobile": "13800138001",
        "telephone": "010-12345679",
        "email": "<EMAIL>",
        "wx": "zhangsan_new_wx",
        "department": "市场部",
        "position": "市场总监",
        "remark": "已升职为市场总监",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-31 16:30:00"
    }
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "联系人不存在"
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "mobile": ["该手机号已被其他联系人使用"],
        "age": ["年龄必须在1-150之间"]
    }
}
```

#### 状态码说明
- **200**: 更新成功
- **404**: 联系人不存在
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X PUT "http://127.0.0.1:8000/api/contacts/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "age": 36,
    "department": "市场部",
    "position": "市场总监",
    "remark": "已升职为市场总监"
  }'
```

---

### 5. 删除联系人

#### 接口信息
- **HTTP 方法**: DELETE
- **URL 路径**: `/contacts/{id}`
- **接口描述**: 删除指定的联系人记录（软删除）

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 联系人ID |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "删除成功"
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "联系人不存在"
}
```

#### 状态码说明
- **200**: 删除成功
- **404**: 联系人不存在
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X DELETE "http://127.0.0.1:8000/api/contacts/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 6. 搜索联系人

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/contacts/search`
- **接口描述**: 根据关键词搜索联系人，支持姓名、手机号、邮箱等字段的模糊搜索

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| keyword | string | 是 | - | 搜索关键词 |
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 15 | 每页数量 (1-100) |
| fields | string | 否 | name,mobile,email | 搜索字段 (逗号分隔) |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "搜索成功",
    "data": {
        "data": [
            {
                "id": 1,
                "name": "张三",
                "gender": "男",
                "age": 35,
                "mobile": "13800138000",
                "telephone": "010-12345678",
                "email": "<EMAIL>",
                "wx": "zhangsan_wx",
                "department": "销售部",
                "position": "销售经理",
                "remark": "重要联系人",
                "created_at": "2025-07-29 14:30:00",
                "updated_at": "2025-07-30 10:30:00",
                "match_fields": ["name", "mobile"]
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 5,
            "last_page": 1,
            "from": 1,
            "to": 5
        },
        "search_info": {
            "keyword": "张",
            "fields": ["name", "mobile", "email"],
            "total_matches": 5
        }
    }
}
```

##### 错误响应 (400 Bad Request)
```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": {
        "keyword": ["搜索关键词不能为空"]
    }
}
```

#### 状态码说明
- **200**: 搜索成功
- **400**: 请求参数错误
- **500**: 服务器内部错误

#### cURL 请求示例

##### 基础搜索
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts/search?keyword=张" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 指定字段搜索
```bash
curl -X GET "http://127.0.0.1:8000/api/contacts/search?keyword=138&fields=mobile,telephone" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 7. 批量导入联系人

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/contacts/batch-import`
- **接口描述**: 批量导入联系人数据

#### 请求参数

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| contacts | array | 是 | 联系人数据数组 (最多100个) | 见下方示例 |
| skip_duplicates | boolean | 否 | 是否跳过重复数据 (默认false) | true |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "批量导入完成",
    "data": {
        "total_count": 10,
        "success_count": 8,
        "failed_count": 2,
        "skipped_count": 1,
        "success_rate": "80%",
        "failed_items": [
            {
                "index": 3,
                "data": {
                    "name": "李四",
                    "mobile": "13800138000"
                },
                "errors": ["该手机号已存在"]
            },
            {
                "index": 7,
                "data": {
                    "name": "",
                    "mobile": "13900139000"
                },
                "errors": ["姓名不能为空"]
            }
        ]
    }
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "contacts": ["联系人数据不能为空"],
        "contacts.0.name": ["姓名不能为空"]
    }
}
```

#### 状态码说明
- **200**: 批量导入完成
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/contacts/batch-import" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "contacts": [
        {
            "name": "张三",
            "mobile": "13800138000",
            "email": "<EMAIL>",
            "department": "销售部",
            "position": "销售经理"
        },
        {
            "name": "李四",
            "mobile": "13900139000",
            "email": "<EMAIL>",
            "department": "技术部",
            "position": "技术经理"
        }
    ],
    "skip_duplicates": true
  }'
```

---

## 接口测试

### 测试环境准备

1. **启动本地服务器**
```bash
php artisan serve
```

2. **确保数据库连接正常**
```bash
php artisan migrate:status
```

3. **创建测试数据**
```bash
php artisan db:seed --class=ContactSeeder
```

### 完整测试流程

#### 1. 测试创建联系人
```bash
# 创建测试联系人
curl -X POST "http://127.0.0.1:8000/api/contacts" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "name": "测试联系人",
    "gender": "男",
    "age": 30,
    "mobile": "13800138999",
    "email": "<EMAIL>",
    "department": "测试部",
    "position": "测试工程师",
    "remark": "API测试创建的联系人"
  }'
```

**预期结果**: 返回201状态码，包含新创建联系人的完整信息

#### 2. 测试获取联系人列表
```bash
# 获取联系人列表
curl -X GET "http://127.0.0.1:8000/api/contacts?page=1&per_page=5" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含分页的联系人列表

#### 3. 测试搜索联系人
```bash
# 搜索联系人
curl -X GET "http://127.0.0.1:8000/api/contacts/search?keyword=测试" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含匹配的联系人列表

#### 4. 测试更新联系人
```bash
# 更新联系人信息
curl -X PUT "http://127.0.0.1:8000/api/contacts/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "age": 31,
    "position": "高级测试工程师",
    "remark": "已升职为高级测试工程师"
  }'
```

**预期结果**: 返回200状态码，包含更新后的联系人信息

#### 5. 测试批量导入
```bash
# 批量导入联系人
curl -X POST "http://127.0.0.1:8000/api/contacts/batch-import" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "contacts": [
        {
            "name": "批量导入1",
            "mobile": "13700137001",
            "department": "销售部"
        },
        {
            "name": "批量导入2",
            "mobile": "13700137002",
            "department": "市场部"
        }
    ],
    "skip_duplicates": true
  }'
```

**预期结果**: 返回200状态码，包含批量导入的结果统计

#### 6. 测试删除联系人
```bash
# 删除联系人
curl -X DELETE "http://127.0.0.1:8000/api/contacts/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码表示删除成功

### 错误场景测试

#### 1. 测试参数验证
```bash
# 测试无效的分页参数
curl -X GET "http://127.0.0.1:8000/api/contacts?page=0&per_page=200" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回400状态码，包含参数验证错误信息

#### 2. 测试重复手机号
```bash
# 尝试创建重复手机号的联系人
curl -X POST "http://127.0.0.1:8000/api/contacts" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "name": "重复测试",
    "mobile": "13800138999"
  }'
```

**预期结果**: 返回422状态码，包含手机号重复的错误信息

#### 3. 测试不存在的联系人
```bash
# 尝试获取不存在的联系人
curl -X GET "http://127.0.0.1:8000/api/contacts/99999" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回404状态码，包含联系人不存在的错误信息

## 注意事项

1. **数据唯一性**: 手机号必须唯一，不能重复
2. **软删除**: 删除操作为软删除，数据仍保留在数据库中
3. **批量操作**: 批量导入最多支持100条记录
4. **搜索性能**: 大量数据时建议使用分页和字段限制
5. **数据验证**: 所有输入数据都会进行严格验证

## 更新日志

- **v1.0.0** (2025-07-31): 初始版本，包含基础CRUD和搜索功能
- **v1.1.0** (2025-07-31): 添加批量导入功能和性能优化

# 开发测试模块 API 接口文档

## 概述

开发测试模块提供了用于开发和调试的各种测试接口，包括 Redis 服务测试、错误处理测试、Telescope 监控测试等。这些接口仅在非生产环境中可用，用于验证系统功能和排查问题。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`
- **环境限制**: 仅在非生产环境启用

## 重要说明

⚠️ **安全警告**: 这些接口仅用于开发和测试，在生产环境中会被自动禁用。请勿在生产环境中启用这些接口。

## 模块列表

### 1. Redis 服务测试模块

#### 模块前缀: `/test/redis`

Redis 测试模块用于验证 Redis 服务的各种功能，包括连接测试、基础操作、批量操作等。

#### 1.1 Redis 连接测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/redis/ping`
- **接口描述**: 测试 Redis 服务连接状态

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "status": "success",
    "message": "Redis 连接正常",
    "connection_info": {
        "host": "127.0.0.1",
        "port": 6379,
        "database": 0,
        "ping_time_ms": 2
    }
}
```

###### 失败响应 (200 OK)
```json
{
    "status": "failed",
    "message": "Redis 连接失败",
    "connection_info": null
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/redis/ping" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 1.2 Redis 基础操作测试

##### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/test/redis/basic`
- **接口描述**: 测试 Redis 基础键值操作

##### 请求参数
可以在请求体中传入任意测试数据，将被存储到 Redis 中。

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "test_key": "test:basic:1690876800",
    "set_result": true,
    "get_value": {
        "message": "Hello Redis!",
        "timestamp": 1690876800,
        "data": {}
    },
    "exists": true,
    "ttl": 299,
    "operations": {
        "set": "success",
        "get": "success",
        "exists": "success",
        "ttl": "success"
    }
}
```

##### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/test/redis/basic" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "test_data": "这是测试数据",
    "user_id": 123
  }'
```

#### 1.3 Redis 批量操作测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/redis/batch`
- **接口描述**: 测试 Redis 批量操作功能

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "batch_set": true,
    "batch_get": {
        "batch:test1:1690876800": {
            "name": "张三",
            "age": 25
        },
        "batch:test2:1690876800": {
            "name": "李四",
            "age": 30
        },
        "batch:test3:1690876800": {
            "name": "王五",
            "age": 28
        }
    },
    "batch_delete": 3,
    "operations": {
        "set_multiple": "success",
        "get_multiple": "success",
        "delete_multiple": "success"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/redis/batch" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 1.4 Redis 计数器操作测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/redis/counter`
- **接口描述**: 测试 Redis 计数器操作

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "counter_key": "test:counter:1690876800",
    "increment_1": 1,
    "increment_5": 6,
    "decrement_2": 4,
    "final_value": 4,
    "operations": {
        "increment": "success",
        "decrement": "success",
        "final_check": "success"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/redis/counter" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 1.5 Redis 模式匹配测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/redis/pattern`
- **接口描述**: 测试 Redis 键模式匹配功能

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "pattern": "pattern:test:*",
    "created_keys": [
        "pattern:test:user:1",
        "pattern:test:user:2",
        "pattern:test:order:1"
    ],
    "matched_keys": [
        "pattern:test:user:1",
        "pattern:test:user:2",
        "pattern:test:order:1"
    ],
    "deleted_count": 3,
    "operations": {
        "create_keys": "success",
        "pattern_match": "success",
        "cleanup": "success"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/redis/pattern" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 1.6 Redis 综合测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/redis/comprehensive`
- **接口描述**: 执行 Redis 服务的综合功能测试

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "test_summary": {
        "total_tests": 6,
        "passed_tests": 6,
        "failed_tests": 0,
        "success_rate": "100%"
    },
    "test_results": {
        "connection": "passed",
        "basic_operations": "passed",
        "batch_operations": "passed",
        "counter_operations": "passed",
        "pattern_matching": "passed",
        "performance": "passed"
    },
    "performance_metrics": {
        "total_time_ms": 45,
        "operations_per_second": 133,
        "memory_usage_mb": 2.5
    },
    "recommendations": [
        "Redis 服务运行正常",
        "所有功能测试通过",
        "性能表现良好"
    ]
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/redis/comprehensive" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 2. 错误处理测试模块

#### 模块前缀: `/test/errors`

错误测试模块用于验证系统的错误处理机制，包括各种类型的 PHP 错误和异常。

#### 2.1 获取错误测试列表

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors`
- **接口描述**: 获取所有可用的错误测试类型

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "Available error tests",
    "data": {
        "normal": "Normal response test",
        "fatal-error": "PHP Fatal Error test",
        "parse-error": "PHP Parse Error test",
        "warning": "PHP Warning test",
        "notice": "PHP Notice test",
        "exception": "Uncaught Exception test",
        "business-exception": "Business Exception test",
        "memory-error": "Memory overflow test",
        "division-by-zero": "Division by zero test",
        "type-error": "Type Error test",
        "array-error": "Array access error test",
        "stack-overflow": "Stack overflow test"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.2 正常响应测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/normal`
- **接口描述**: 测试正常响应（对照组）

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "Normal response",
    "data": {
        "timestamp": "2025-07-31T16:30:00Z",
        "message": "All systems working normally"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/normal" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.3 PHP Fatal Error 测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/fatal-error`
- **接口描述**: 触发 PHP Fatal Error，测试致命错误处理

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "UNCAUGHT_EXCEPTION",
            "exception_class": "Error",
            "message": "Call to undefined function nonExistentFunction()",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 25
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/fatal-error" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.4 PHP Parse Error 测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/parse-error`
- **接口描述**: 触发 PHP Parse Error，测试语法错误处理

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "PARSE_ERROR",
            "exception_class": "ParseError",
            "message": "syntax error, unexpected 'syntax'",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 38
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/parse-error" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.5 PHP Warning 测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/warning`
- **接口描述**: 触发 PHP Warning，测试警告处理

##### 响应格式

###### 成功响应 (200 OK) - 警告被记录但继续执行
```json
{
    "code": 200,
    "message": "Warning triggered",
    "data": {
        "file": false
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/warning" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.6 PHP Notice 测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/notice`
- **接口描述**: 触发 PHP Notice，测试通知处理

##### 响应格式

###### 成功响应 (200 OK) - 通知被记录但继续执行
```json
{
    "code": 200,
    "message": "Notice triggered",
    "data": {
        "value": null
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/notice" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.7 未捕获异常测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/exception`
- **接口描述**: 抛出未捕获的异常，测试异常处理机制

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "UNCAUGHT_EXCEPTION",
            "exception_class": "Exception",
            "message": "This is a test exception",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 76
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/exception" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.8 业务异常测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/business-exception`
- **接口描述**: 抛出业务异常，测试业务错误处理

##### 响应格式

###### 错误响应 (400 Bad Request)
```json
{
    "code": 400,
    "message": "This is a test business exception"
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/business-exception" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.9 内存溢出测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/memory-error`
- **接口描述**: 触发内存溢出错误，测试内存限制处理

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "FATAL_ERROR",
            "exception_class": "Error",
            "message": "Allowed memory size exhausted",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 99
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/memory-error" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.10 除零错误测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/division-by-zero`
- **接口描述**: 触发除零错误，测试数学运算错误处理

##### 响应格式

###### 成功响应 (200 OK) - PHP 8+ 中除零返回 INF
```json
{
    "code": 200,
    "message": "Division result",
    "data": {
        "result": "INF"
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/division-by-zero" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.11 类型错误测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/type-error`
- **接口描述**: 触发类型错误，测试类型检查处理

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "TYPE_ERROR",
            "exception_class": "TypeError",
            "message": "Argument 1 passed to requireString() must be of the type string, int given",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 124
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/type-error" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.12 数组访问错误测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/array-error`
- **接口描述**: 触发数组访问错误，测试数组操作错误处理

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "UNCAUGHT_EXCEPTION",
            "exception_class": "Error",
            "message": "Trying to access array offset on value of type null",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 137
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/array-error" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 2.13 栈溢出测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/errors/stack-overflow`
- **接口描述**: 触发栈溢出错误，测试递归调用限制

##### 响应格式

###### 错误响应 (500 Internal Server Error)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "details": {
            "type": "FATAL_ERROR",
            "exception_class": "Error",
            "message": "Maximum function nesting level reached, aborting!",
            "file": "/app/Http/Controllers/ErrorTestController.php",
            "line": 207
        }
    }
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/errors/stack-overflow" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 3. Telescope 监控测试模块

#### 模块前缀: `/test/telescope`

Telescope 测试模块用于验证 Laravel Telescope 监控功能。

#### 3.1 查询监控测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/telescope/queries`
- **接口描述**: 模拟多种查询场景，测试 Telescope 查询监控

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "message": "Telescope 查询监控测试完成",
    "leads_count": 5,
    "note": "请访问 /telescope 查看查询监控数据"
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/telescope/queries" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

#### 3.2 性能监控测试

##### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/test/telescope/performance`
- **接口描述**: 执行复杂查询，测试 Telescope 性能监控

##### 响应格式

###### 成功响应 (200 OK)
```json
{
    "message": "性能测试完成",
    "duration_ms": 125.67,
    "note": "请在 Telescope 中查看详细的查询分析"
}
```

##### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/test/telescope/performance" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

## 完整测试流程

### 1. Redis 服务测试流程

```bash
# 1. 测试 Redis 连接
curl -X GET "http://127.0.0.1:8000/api/test/redis/ping" \
  -H "Accept: application/json" --noproxy "*"

# 2. 测试基础操作
curl -X POST "http://127.0.0.1:8000/api/test/redis/basic" \
  -H "Accept: application/json" -H "Content-Type: application/json" \
  --noproxy "*" -d '{"test": "data"}'

# 3. 测试批量操作
curl -X GET "http://127.0.0.1:8000/api/test/redis/batch" \
  -H "Accept: application/json" --noproxy "*"

# 4. 测试计数器操作
curl -X GET "http://127.0.0.1:8000/api/test/redis/counter" \
  -H "Accept: application/json" --noproxy "*"

# 5. 测试模式匹配
curl -X GET "http://127.0.0.1:8000/api/test/redis/pattern" \
  -H "Accept: application/json" --noproxy "*"

# 6. 综合测试
curl -X GET "http://127.0.0.1:8000/api/test/redis/comprehensive" \
  -H "Accept: application/json" --noproxy "*"
```

### 2. 错误处理测试流程

```bash
# 1. 获取测试列表
curl -X GET "http://127.0.0.1:8000/api/test/errors" \
  -H "Accept: application/json" --noproxy "*"

# 2. 测试正常响应（对照组）
curl -X GET "http://127.0.0.1:8000/api/test/errors/normal" \
  -H "Accept: application/json" --noproxy "*"

# 3. 测试业务异常（安全测试）
curl -X GET "http://127.0.0.1:8000/api/test/errors/business-exception" \
  -H "Accept: application/json" --noproxy "*"

# 4. 测试 PHP Warning（安全测试）
curl -X GET "http://127.0.0.1:8000/api/test/errors/warning" \
  -H "Accept: application/json" --noproxy "*"

# 5. 测试 PHP Notice（安全测试）
curl -X GET "http://127.0.0.1:8000/api/test/errors/notice" \
  -H "Accept: application/json" --noproxy "*"

# 6. 测试除零错误（安全测试）
curl -X GET "http://127.0.0.1:8000/api/test/errors/division-by-zero" \
  -H "Accept: application/json" --noproxy "*"

# ⚠️ 危险测试（可能导致服务中断，请谨慎使用）
# 7. 测试未捕获异常
curl -X GET "http://127.0.0.1:8000/api/test/errors/exception" \
  -H "Accept: application/json" --noproxy "*"

# 8. 测试类型错误
curl -X GET "http://127.0.0.1:8000/api/test/errors/type-error" \
  -H "Accept: application/json" --noproxy "*"

# 9. 测试数组访问错误
curl -X GET "http://127.0.0.1:8000/api/test/errors/array-error" \
  -H "Accept: application/json" --noproxy "*"

# 🚨 极危险测试（可能导致服务崩溃，仅在隔离环境使用）
# 10. 测试 Fatal Error
# curl -X GET "http://127.0.0.1:8000/api/test/errors/fatal-error" \
#   -H "Accept: application/json" --noproxy "*"

# 11. 测试 Parse Error
# curl -X GET "http://127.0.0.1:8000/api/test/errors/parse-error" \
#   -H "Accept: application/json" --noproxy "*"

# 12. 测试内存溢出
# curl -X GET "http://127.0.0.1:8000/api/test/errors/memory-error" \
#   -H "Accept: application/json" --noproxy "*"

# 13. 测试栈溢出
# curl -X GET "http://127.0.0.1:8000/api/test/errors/stack-overflow" \
#   -H "Accept: application/json" --noproxy "*"
```

### 3. Telescope 监控测试流程

```bash
# 1. 测试查询监控
curl -X GET "http://127.0.0.1:8000/api/test/telescope/queries" \
  -H "Accept: application/json" --noproxy "*"

# 2. 测试性能监控
curl -X GET "http://127.0.0.1:8000/api/test/telescope/performance" \
  -H "Accept: application/json" --noproxy "*"

# 3. 访问 Telescope 面板查看结果
# 浏览器访问: http://127.0.0.1:8000/telescope
```

## 使用建议

### 1. 开发阶段
- 使用 Redis 测试验证缓存功能
- 使用错误测试验证异常处理
- 使用 Telescope 测试监控性能问题

### 2. 调试阶段
- 通过错误测试重现问题场景
- 使用 Telescope 测试分析查询性能
- 验证错误处理机制的完整性

### 3. 部署前测试
- 执行 Redis 综合测试确保缓存服务正常
- 验证错误处理在不同环境下的表现
- 确认监控系统正常工作

## 安全注意事项

1. **环境隔离**: 这些接口仅在开发环境启用
2. **数据安全**: 测试数据会自动清理，但请避免使用敏感信息
3. **服务稳定**: 某些错误测试可能影响服务稳定性
4. **访问控制**: 生产环境必须禁用这些接口

## 故障排查

### Redis 测试失败
1. 检查 Redis 服务是否启动
2. 验证 Redis 配置是否正确
3. 确认网络连接是否正常

### 错误测试异常
1. 检查错误处理中间件配置
2. 验证日志系统是否正常
3. 确认异常处理器是否正确注册

### 错误测试分类

#### 安全测试（推荐使用）
- `normal` - 正常响应测试
- `business-exception` - 业务异常测试
- `warning` - PHP Warning 测试
- `notice` - PHP Notice 测试
- `division-by-zero` - 除零错误测试

#### 中等风险测试（谨慎使用）
- `exception` - 未捕获异常测试
- `type-error` - 类型错误测试
- `array-error` - 数组访问错误测试

#### 高风险测试（仅隔离环境）
- `fatal-error` - PHP Fatal Error 测试
- `parse-error` - PHP Parse Error 测试
- `memory-error` - 内存溢出测试
- `stack-overflow` - 栈溢出测试

### 错误测试最佳实践

#### 1. 测试环境隔离
- 在独立的开发环境中进行测试
- 避免在共享开发环境中运行危险测试
- 使用容器或虚拟机进行隔离测试

#### 2. 测试顺序建议
1. 先运行安全测试验证基础功能
2. 再运行中等风险测试验证异常处理
3. 最后在隔离环境运行高风险测试

#### 3. 监控和日志
- 测试前检查日志系统状态
- 测试过程中监控系统资源使用
- 测试后检查错误日志和异常记录

#### 4. 恢复准备
- 准备服务重启脚本
- 备份重要数据和配置
- 准备回滚方案

### Telescope 无数据
1. 确认 Telescope 是否已安装
2. 检查 Telescope 配置是否正确
3. 验证数据库连接是否正常

## 更新日志

- **v1.0.0** (2025-07-31): 初始版本，包含 Redis、错误处理、Telescope 测试功能

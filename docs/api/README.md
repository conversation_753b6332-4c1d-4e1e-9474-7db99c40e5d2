# CRM API 接口文档

## 概述

本文档提供了 CRM 系统所有 API 接口的详细说明，包括请求参数、响应格式、状态码说明和完整的 cURL 测试示例。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据内容
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "错误描述",
    "errors": {
        "field_name": ["具体错误信息"]
    },
    "trace_id": "唯一追踪ID"
}
```

## 通用状态码

| 状态码 | 含义 | 描述 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权 |
| 403 | Forbidden | 禁止访问 |
| 404 | Not Found | 资源不存在 |
| 422 | Unprocessable Entity | 数据验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 分页格式

所有列表接口都支持分页，响应格式如下：

```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "data": [
            // 数据列表
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7,
            "from": 1,
            "to": 15,
            "has_more_pages": true
        }
    }
}
```

### 分页参数
| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| page | integer | 1 | 页码 |
| per_page | integer | 15 | 每页数量 (1-100) |

## 排序格式

支持排序的接口通用参数：

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| sort_by | string | created_at | 排序字段 |
| sort_direction | string | desc | 排序方向 (asc/desc) |

## 模块文档

### 1. 线索模块
- **文档**: [线索模块 API 文档](./lead-api.md)
- **功能**: 线索的创建、查询、更新、删除、批量操作
- **主要接口**:
  - `GET /leads` - 获取线索列表
  - `POST /leads` - 创建线索
  - `GET /leads/{id}` - 获取线索详情
  - `PUT /leads/{id}` - 更新线索
  - `DELETE /leads/{id}` - 删除线索
  - `PATCH /leads/batch-status` - 批量更新状态

### 2. 联系人模块 (计划中)
- **文档**: [联系人模块 API 文档](./contact-api.md)
- **状态**: 后端逻辑已实现，HTTP 接口层待开发
- **功能**: 联系人的创建、查询、更新、删除、搜索、批量导入
- **计划接口**:
  - `GET /contacts` - 获取联系人列表
  - `POST /contacts` - 创建联系人
  - `GET /contacts/{id}` - 获取联系人详情
  - `PUT /contacts/{id}` - 更新联系人
  - `DELETE /contacts/{id}` - 删除联系人
  - `GET /contacts/search` - 搜索联系人
  - `POST /contacts/batch-import` - 批量导入联系人

### 3. 系统监控模块
- **文档**: [系统监控模块 API 文档](./system-api.md)
- **功能**: 日志系统健康检查、容错服务监控、系统状态管理
- **主要接口**:
  - `GET /system/log-health` - 获取系统健康状态
  - `GET /system/log-health/resilient-status` - 获取容错服务状态
  - `GET /system/log-health/history` - 获取健康历史
  - `POST /system/log-health/check` - 手动触发健康检查
  - `POST /system/log-health/reset-circuit-breaker` - 重置熔断器
  - `POST /system/log-health/test-logging` - 测试日志记录

### 4. 用户认证模块
- **文档**: [用户认证模块 API 文档](./auth-api.md)
- **功能**: 基于 Laravel Sanctum 的用户身份验证
- **主要接口**:
  - `GET /user` - 获取当前用户信息 (需认证)
- **计划接口**:
  - `POST /auth/login` - 用户登录
  - `POST /auth/register` - 用户注册
  - `POST /auth/logout` - 用户登出
  - `POST /auth/refresh` - 刷新令牌

### 5. 开发测试模块 (仅非生产环境)
- **文档**: [开发测试模块 API 文档](./test-api.md)
- **功能**: Redis 测试、错误处理测试、Telescope 监控测试
- **接口统计**: 20个接口 (Redis 6个 + 错误测试 12个 + Telescope 2个)
- **主要接口**:
  - `GET /test/redis/*` - Redis 服务测试 (6个接口)
  - `GET /test/errors/*` - 错误处理测试 (12个接口)
  - `GET /test/telescope/*` - Telescope 监控测试 (2个接口)

## 快速开始

### 1. 环境准备
```bash
# 启动开发服务器
php artisan serve

# 检查数据库状态
php artisan migrate:status

# 创建测试数据
php artisan db:seed
```

### 2. 基础测试
```bash
# 测试服务器连接
curl -X GET "http://127.0.0.1:8000/api/leads" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

### 3. 创建测试数据
```bash
# 创建测试线索
curl -X POST "http://127.0.0.1:8000/api/leads" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "company_full_name": "测试公司",
    "company_short_name": "测试",
    "region": 1,
    "source": 1,
    "industry": 1,
    "creator_id": 1
  }'
```

## 错误处理

### 常见错误类型

#### 1. 参数验证错误 (422)
```json
{
    "code": 422,
    "message": "数据验证失败",
    "errors": {
        "company_full_name": ["公司全称不能为空"],
        "region": ["所属区域值无效"]
    }
}
```

#### 2. 资源不存在 (404)
```json
{
    "code": 404,
    "message": "线索不存在"
}
```

#### 3. 服务器错误 (500)
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "exception_class": "Exception",
        "message": "具体错误信息",
        "file": "错误文件路径",
        "line": 错误行号
    }
}
```

### 错误处理建议

1. **客户端重试**: 对于 5xx 错误，建议实现指数退避重试
2. **参数验证**: 在发送请求前进行客户端验证
3. **错误日志**: 记录所有 API 错误响应用于调试
4. **用户友好**: 将技术错误转换为用户友好的提示信息

## 性能优化

### 1. 查询优化
- 使用分页避免大量数据传输
- 合理使用筛选条件减少数据量
- 利用排序参数获取所需数据

### 2. 缓存机制
- 列表查询支持缓存，重复查询响应更快
- 详情查询支持缓存，减少数据库压力
- 缓存自动失效，确保数据一致性

### 3. 批量操作
- 使用批量接口处理多条记录
- 批量操作支持事务，确保数据一致性
- 批量操作支持死锁重试，提高成功率

## 安全注意事项

### 1. 数据验证
- 所有输入数据都经过严格验证
- 防止 SQL 注入和 XSS 攻击
- 敏感数据加密存储

### 2. 访问控制
- 生产环境需要添加认证机制
- 实现基于角色的访问控制
- 记录所有 API 访问日志

### 3. 速率限制
- 默认限制每分钟 60 次请求
- 可根据用户类型调整限制
- 超出限制返回 429 状态码

## 开发工具

### 1. API 测试工具
- **cURL**: 命令行测试工具
- **Postman**: 图形化 API 测试工具
- **Insomnia**: 轻量级 API 测试工具

### 2. 调试工具
- **Laravel Telescope**: 应用监控和调试
- **日志系统**: 详细的请求和错误日志
- **性能分析**: 查询性能和响应时间分析

### 3. 文档工具
- **API 文档**: 详细的接口说明文档
- **代码示例**: 完整的 cURL 请求示例
- **测试用例**: 覆盖各种场景的测试案例

## 版本信息

- **当前版本**: v1.1.0
- **发布日期**: 2025-07-31
- **兼容性**: 向后兼容 v1.0.x

## 联系支持

如有问题或建议，请通过以下方式联系：

- **技术文档**: 查看详细的模块文档
- **错误报告**: 提供完整的错误信息和重现步骤
- **功能建议**: 描述具体的业务需求和期望功能

---

**注意**: 本文档基于开发环境编写，生产环境部署时请注意安全配置和性能优化。

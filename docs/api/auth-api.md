# 用户认证模块 API 接口文档

## 概述

用户认证模块提供了基于 Laravel Sanctum 的用户身份验证功能。目前实现了获取当前用户信息的接口，后续可扩展登录、注册、登出等功能。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`
- **认证方式**: Bearer Token (Laravel Sanctum)

## 认证说明

### Token 获取
目前项目使用 Laravel Sanctum 进行认证，需要先通过其他方式获取访问令牌：

1. **数据库直接创建** (开发测试用)
```php
// 在 tinker 中创建测试令牌
$user = User::first();
$token = $user->createToken('test-token')->plainTextToken;
echo $token;
```

2. **登录接口** (待实现)
- 用户名密码登录获取令牌
- 第三方登录获取令牌

### Token 使用
在请求头中添加认证信息：
```
Authorization: Bearer {your-token-here}
```

## 接口列表

### 1. 获取当前用户信息

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/user`
- **接口描述**: 获取当前认证用户的详细信息
- **认证要求**: 必须提供有效的访问令牌

#### 请求参数
无需参数，用户信息通过认证令牌获取

#### 请求头
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "email_verified_at": "2025-07-29T14:30:00Z",
    "created_at": "2025-07-29T14:30:00Z",
    "updated_at": "2025-07-30T10:30:00Z"
}
```

##### 错误响应 (401 Unauthorized) - 未认证
```json
{
    "message": "Unauthenticated."
}
```

##### 错误响应 (401 Unauthorized) - Token 无效
```json
{
    "message": "Unauthenticated."
}
```

#### 状态码说明
- **200**: 获取用户信息成功
- **401**: 未认证或令牌无效
- **500**: 服务器内部错误

#### cURL 请求示例

##### 有效令牌请求
```bash
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 1|your-actual-token-here" \
  --noproxy "*"
```

##### 无令牌请求（测试认证）
```bash
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

## 计划中的接口

以下接口是认证模块的扩展计划，目前尚未实现：

### 2. 用户登录 (计划中)

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/auth/login`
- **接口描述**: 用户登录获取访问令牌

#### 请求参数
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### 响应格式
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1,
            "name": "张三",
            "email": "<EMAIL>"
        },
        "token": "1|your-access-token-here",
        "expires_at": "2025-08-30T16:30:00Z"
    }
}
```

### 3. 用户注册 (计划中)

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/auth/register`
- **接口描述**: 用户注册新账户

#### 请求参数
```json
{
    "name": "新用户",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
}
```

### 4. 用户登出 (计划中)

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/auth/logout`
- **接口描述**: 用户登出，撤销当前令牌

#### 请求头
```
Authorization: Bearer {token}
```

### 5. 刷新令牌 (计划中)

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/auth/refresh`
- **接口描述**: 刷新访问令牌

### 6. 修改密码 (计划中)

#### 接口信息
- **HTTP 方法**: PUT
- **URL 路径**: `/auth/password`
- **接口描述**: 修改用户密码

---

## 接口测试

### 测试环境准备

1. **启动本地服务器**
```bash
php artisan serve
```

2. **创建测试用户和令牌**
```bash
# 进入 tinker
php artisan tinker

# 创建测试用户（如果不存在）
$user = User::create([
    'name' => '测试用户',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'email_verified_at' => now()
]);

# 创建访问令牌
$token = $user->createToken('test-token')->plainTextToken;
echo "Token: " . $token;
```

### 完整测试流程

#### 1. 测试无认证访问
```bash
# 不提供令牌的请求
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回401状态码，提示未认证

#### 2. 测试有效令牌访问
```bash
# 使用有效令牌的请求（替换为实际令牌）
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 1|your-actual-token-here" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含用户详细信息

#### 3. 测试无效令牌访问
```bash
# 使用无效令牌的请求
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid-token" \
  --noproxy "*"
```

**预期结果**: 返回401状态码，提示未认证

#### 4. 测试令牌格式错误
```bash
# 错误的令牌格式
curl -X GET "http://127.0.0.1:8000/api/user" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: InvalidFormat" \
  --noproxy "*"
```

**预期结果**: 返回401状态码，提示未认证

### 令牌管理测试

#### 1. 创建多个令牌
```bash
# 在 tinker 中为同一用户创建多个令牌
$user = User::first();
$token1 = $user->createToken('web-token')->plainTextToken;
$token2 = $user->createToken('mobile-token')->plainTextToken;
```

#### 2. 查看用户令牌
```bash
# 在 tinker 中查看用户的所有令牌
$user = User::first();
$tokens = $user->tokens;
foreach($tokens as $token) {
    echo "Token ID: " . $token->id . ", Name: " . $token->name . "\n";
}
```

#### 3. 撤销令牌
```bash
# 在 tinker 中撤销特定令牌
$user = User::first();
$user->tokens()->where('name', 'test-token')->delete();
```

## 安全注意事项

### 1. 令牌安全
- **安全存储**: 客户端应安全存储访问令牌
- **传输安全**: 仅在 HTTPS 环境下传输令牌
- **令牌泄露**: 如发现令牌泄露，立即撤销相关令牌

### 2. 令牌管理
- **有效期**: 设置合理的令牌有效期
- **定期清理**: 定期清理过期和无用的令牌
- **权限控制**: 根据令牌类型控制访问权限

### 3. 认证策略
- **多因素认证**: 考虑实现多因素认证
- **设备管理**: 跟踪和管理用户设备
- **异常检测**: 监控异常登录行为

## 开发建议

### 1. 扩展认证功能
建议实现以下功能完善认证模块：

```php
// 登录控制器示例
class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        // 验证用户凭据
        // 创建访问令牌
        // 返回用户信息和令牌
    }
    
    public function logout(Request $request)
    {
        // 撤销当前令牌
        // 返回成功响应
    }
}
```

### 2. 中间件配置
```php
// 在路由中使用认证中间件
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    // 其他需要认证的路由
});
```

### 3. 令牌能力控制
```php
// 创建具有特定能力的令牌
$token = $user->createToken('api-token', ['read', 'write']);

// 在中间件中检查令牌能力
Route::middleware(['auth:sanctum', 'ability:read'])->get('/data', ...);
```

## 错误处理

### 常见错误场景

#### 1. 认证失败
- **原因**: 未提供令牌或令牌无效
- **解决**: 检查令牌格式和有效性
- **状态码**: 401

#### 2. 令牌过期
- **原因**: 令牌超过有效期
- **解决**: 重新登录获取新令牌
- **状态码**: 401

#### 3. 权限不足
- **原因**: 令牌缺少必要的权限
- **解决**: 使用具有相应权限的令牌
- **状态码**: 403

## 更新日志

- **v1.0.0** (2025-07-31): 初始版本，包含获取用户信息接口
- **v1.1.0** (计划中): 添加登录、注册、登出等完整认证功能

# 线索模块 API 接口文档

## 概述

线索模块提供了完整的线索管理功能，包括线索的创建、查询、更新、删除以及批量操作等。所有接口都经过数据库操作优化，支持查询缓存和事务管理。

## 基础信息

- **Base URL**: `http://127.0.0.1:8000/api`
- **Content-Type**: `application/json`
- **Accept**: `application/json`

## 数据字典

### 线索状态 (status)
| 值 | 标签 | 描述 |
|----|------|------|
| 1 | 新建 | 新创建的线索 |
| 2 | 已联系 | 已经联系过的线索 |
| 3 | 已确认 | 已确认有效的线索 |
| 4 | 未确认 | 未确认的线索 |
| 5 | 已转化 | 已转化为客户的线索 |
| 6 | 已丢失 | 已丢失的线索 |

### 所属区域 (region)
| 值 | 标签 |
|----|------|
| 1 | 华北 |
| 2 | 东北 |
| 3 | 华东 |
| 4 | 华南 |
| 5 | 西南 |
| 6 | 西北 |
| 7 | 华中 |

### 线索来源 (source)
| 值 | 标签 |
|----|------|
| 1 | 官网 |
| 2 | 电话 |
| 3 | 邮件 |
| 4 | 展会 |
| 5 | 推荐 |
| 6 | 广告 |
| 7 | 社交媒体 |
| 8 | 其他 |

### 所属行业 (industry)
| 值 | 标签 |
|----|------|
| 1 | 科技 |
| 2 | 金融 |
| 3 | 制造业 |
| 4 | 零售 |
| 5 | 医疗 |
| 6 | 教育 |
| 7 | 房地产 |
| 8 | 物流 |
| 9 | 能源 |
| 10 | 农业 |
| 11 | 娱乐 |
| 12 | 其他 |

### 线索阶段 (stage)
| 值 | 标签 |
|----|------|
| 1 | 初始接触 |
| 2 | 产生兴趣 |
| 3 | 考虑阶段 |
| 4 | 购买意向 |
| 5 | 评估阶段 |
| 6 | 购买阶段 |

## 接口列表

### 1. 获取线索列表

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/leads`
- **接口描述**: 获取线索分页列表，支持多种筛选条件和排序

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 15 | 每页数量 (1-100) |
| company_name | string | 否 | - | 公司名称模糊搜索 |
| status | integer | 否 | - | 线索状态筛选 |
| region | integer | 否 | - | 所属区域筛选 |
| source | integer | 否 | - | 线索来源筛选 |
| industry | integer | 否 | - | 所属行业筛选 |
| stage | integer | 否 | - | 线索阶段筛选 |
| creator_id | integer | 否 | - | 创建人ID筛选 |
| sort_by | string | 否 | created_at | 排序字段 |
| sort_direction | string | 否 | desc | 排序方向 (asc/desc) |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "data": [
            {
                "id": 1,
                "company_full_name": "北京科技有限公司",
                "company_short_name": "北京科技",
                "internal_name": "BJ科技",
                "region": 1,
                "region_label": "华北",
                "source": 1,
                "source_label": "官网",
                "industry": 1,
                "industry_label": "科技",
                "status": 1,
                "status_label": "新建",
                "stage": 1,
                "stage_label": "初始接触",
                "address": "北京市朝阳区xxx路xxx号",
                "creator_id": 1,
                "last_followed_at": "2025-07-30 10:30:00",
                "remark": "重要客户",
                "created_at": "2025-07-29 14:30:00",
                "updated_at": "2025-07-30 10:30:00",
                "creator": {
                    "id": 1,
                    "name": "张三"
                },
                "contacts": [
                    {
                        "id": 1,
                        "name": "李四",
                        "mobile": "13800138000"
                    }
                ],
                "users": [
                    {
                        "id": 1,
                        "name": "张三"
                    }
                ]
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7,
            "from": 1,
            "to": 15
        }
    }
}
```

##### 错误响应 (400 Bad Request)
```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": {
        "per_page": ["每页数量不能超过100"]
    }
}
```

#### 状态码说明
- **200**: 获取成功
- **400**: 请求参数错误
- **500**: 服务器内部错误

#### cURL 请求示例

##### 基础查询
```bash
curl -X GET "http://127.0.0.1:8000/api/leads?page=1&per_page=15" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 带筛选条件的查询
```bash
curl -X GET "http://127.0.0.1:8000/api/leads?page=1&per_page=10&status=1&region=1&company_name=科技" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 自定义排序查询
```bash
curl -X GET "http://127.0.0.1:8000/api/leads?sort_by=last_followed_at&sort_direction=desc" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 2. 获取线索详情

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/leads/{id}`
- **接口描述**: 根据线索ID获取线索详细信息

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 线索ID |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "company_full_name": "北京科技有限公司",
        "company_short_name": "北京科技",
        "internal_name": "BJ科技",
        "region": 1,
        "region_label": "华北",
        "source": 1,
        "source_label": "官网",
        "industry": 1,
        "industry_label": "科技",
        "status": 1,
        "status_label": "新建",
        "stage": 1,
        "stage_label": "初始接触",
        "address": "北京市朝阳区xxx路xxx号",
        "creator_id": 1,
        "last_followed_at": "2025-07-30 10:30:00",
        "remark": "重要客户",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-30 10:30:00",
        "creator": {
            "id": 1,
            "name": "张三"
        },
        "contacts": [
            {
                "id": 1,
                "name": "李四",
                "mobile": "13800138000"
            }
        ],
        "users": [
            {
                "id": 1,
                "name": "张三"
            }
        ]
    }
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "线索不存在"
}
```

#### 状态码说明
- **200**: 获取成功
- **404**: 线索不存在
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X GET "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 3. 创建线索

#### 接口信息
- **HTTP 方法**: POST
- **URL 路径**: `/leads`
- **接口描述**: 创建新的线索记录

#### 请求参数

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| company_full_name | string | 是 | 公司全称 (最大255字符，唯一) | "北京科技有限公司" |
| company_short_name | string | 是 | 公司简称 (最大100字符) | "北京科技" |
| internal_name | string | 否 | 内部统称 (最大100字符) | "BJ科技" |
| region | integer | 是 | 所属区域 (1-7) | 1 |
| source | integer | 是 | 线索来源 (1-8) | 1 |
| industry | integer | 是 | 所属行业 (1-12) | 1 |
| status | integer | 否 | 线索状态 (1-6，默认1) | 1 |
| stage | integer | 否 | 线索阶段 (1-6，默认1) | 1 |
| address | string | 否 | 详细地址 (最大255字符) | "北京市朝阳区xxx路xxx号" |
| creator_id | integer | 是 | 创建人ID | 1 |
| last_followed_at | string | 否 | 最近跟进时间 (YYYY-MM-DD HH:mm:ss) | "2025-07-30 10:30:00" |
| remark | string | 否 | 备注 (最大1000字符) | "重要客户" |

#### 响应格式

##### 成功响应 (201 Created)
```json
{
    "code": 201,
    "message": "创建成功",
    "data": {
        "id": 1,
        "company_full_name": "北京科技有限公司",
        "company_short_name": "北京科技",
        "internal_name": "BJ科技",
        "region": 1,
        "region_label": "华北",
        "source": 1,
        "source_label": "官网",
        "industry": 1,
        "industry_label": "科技",
        "status": 1,
        "status_label": "新建",
        "stage": 1,
        "stage_label": "初始接触",
        "address": "北京市朝阳区xxx路xxx号",
        "creator_id": 1,
        "last_followed_at": "2025-07-30 10:30:00",
        "remark": "重要客户",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-29 14:30:00"
    }
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "company_full_name": ["该公司已存在线索记录"],
        "region": ["所属区域值无效"]
    }
}
```

#### 状态码说明
- **201**: 创建成功
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/leads" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "company_full_name": "北京科技有限公司",
    "company_short_name": "北京科技",
    "internal_name": "BJ科技",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "address": "北京市朝阳区xxx路xxx号",
    "creator_id": 1,
    "last_followed_at": "2025-07-30 10:30:00",
    "remark": "重要客户"
  }'
```

---

### 4. 更新线索

#### 接口信息
- **HTTP 方法**: PUT
- **URL 路径**: `/leads/{id}`
- **接口描述**: 更新指定线索的信息

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 线索ID |

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| company_full_name | string | 否 | 公司全称 (最大255字符，唯一) | "北京科技有限公司" |
| company_short_name | string | 否 | 公司简称 (最大100字符) | "北京科技" |
| internal_name | string | 否 | 内部统称 (最大100字符) | "BJ科技" |
| region | integer | 否 | 所属区域 (1-7) | 1 |
| source | integer | 否 | 线索来源 (1-8) | 1 |
| industry | integer | 否 | 所属行业 (1-12) | 1 |
| status | integer | 否 | 线索状态 (1-6) | 2 |
| stage | integer | 否 | 线索阶段 (1-6) | 2 |
| address | string | 否 | 详细地址 (最大255字符) | "北京市朝阳区xxx路xxx号" |
| last_followed_at | string | 否 | 最近跟进时间 (YYYY-MM-DD HH:mm:ss) | "2025-07-31 15:30:00" |
| remark | string | 否 | 备注 (最大1000字符) | "已联系，有合作意向" |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "company_full_name": "北京科技有限公司",
        "company_short_name": "北京科技",
        "internal_name": "BJ科技",
        "region": 1,
        "region_label": "华北",
        "source": 1,
        "source_label": "官网",
        "industry": 1,
        "industry_label": "科技",
        "status": 2,
        "status_label": "已联系",
        "stage": 2,
        "stage_label": "产生兴趣",
        "address": "北京市朝阳区xxx路xxx号",
        "creator_id": 1,
        "last_followed_at": "2025-07-31 15:30:00",
        "remark": "已联系，有合作意向",
        "created_at": "2025-07-29 14:30:00",
        "updated_at": "2025-07-31 15:30:00"
    }
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "线索不存在"
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "company_full_name": ["该公司名称已被其他线索使用"],
        "status": ["线索状态值无效"]
    }
}
```

#### 状态码说明
- **200**: 更新成功
- **404**: 线索不存在
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X PUT "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "status": 2,
    "stage": 2,
    "last_followed_at": "2025-07-31 15:30:00",
    "remark": "已联系，有合作意向"
  }'
```

---

### 5. 删除线索

#### 接口信息
- **HTTP 方法**: DELETE
- **URL 路径**: `/leads/{id}`
- **接口描述**: 删除指定的线索记录（软删除）

#### 请求参数

##### 路径参数 (Path Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 线索ID |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "删除成功"
}
```

##### 错误响应 (404 Not Found)
```json
{
    "code": 404,
    "message": "线索不存在"
}
```

##### 错误响应 (403 Forbidden)
```json
{
    "code": 403,
    "message": "当前状态的线索不允许删除"
}
```

#### 状态码说明
- **200**: 删除成功
- **403**: 线索状态不允许删除
- **404**: 线索不存在
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X DELETE "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

### 6. 批量更新线索状态

#### 接口信息
- **HTTP 方法**: PATCH
- **URL 路径**: `/leads/batch-status`
- **接口描述**: 批量更新多个线索的状态

#### 请求参数

##### 请求体参数 (Request Body)
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| ids | array | 是 | 线索ID数组 (最多100个) | [1, 2, 3, 4, 5] |
| status | integer | 是 | 新的线索状态 (1-6) | 2 |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "批量更新成功",
    "data": {
        "updated_count": 5,
        "total_count": 5,
        "success_rate": "100%"
    }
}
```

##### 错误响应 (422 Unprocessable Entity)
```json
{
    "code": 422,
    "message": "验证失败",
    "errors": {
        "ids": ["线索ID数组不能为空"],
        "status": ["线索状态值无效"]
    }
}
```

#### 状态码说明
- **200**: 批量更新成功
- **422**: 验证失败
- **500**: 服务器内部错误

#### cURL 请求示例
```bash
curl -X PATCH "http://127.0.0.1:8000/api/leads/batch-status" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "ids": [1, 2, 3, 4, 5],
    "status": 2
  }'
```

---

### 7. 获取线索统计信息

#### 接口信息
- **HTTP 方法**: GET
- **URL 路径**: `/leads/statistics`
- **接口描述**: 获取线索的统计信息，包括总数、状态分布、区域分布等

#### 请求参数

##### 查询参数 (Query Parameters)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| status | integer | 否 | 按状态筛选统计 |
| region | integer | 否 | 按区域筛选统计 |
| source | integer | 否 | 按来源筛选统计 |
| industry | integer | 否 | 按行业筛选统计 |
| creator_id | integer | 否 | 按创建人筛选统计 |
| date_range | string | 否 | 日期范围筛选 (格式: YYYY-MM-DD,YYYY-MM-DD) |

#### 响应格式

##### 成功响应 (200 OK)
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_count": 1250,
        "status_distribution": {
            "1": 320,
            "2": 280,
            "3": 180,
            "4": 150,
            "5": 220,
            "6": 100
        },
        "region_distribution": {
            "1": 200,
            "2": 150,
            "3": 300,
            "4": 250,
            "5": 180,
            "6": 120,
            "7": 50
        },
        "source_distribution": {
            "1": 400,
            "2": 200,
            "3": 180,
            "4": 150,
            "5": 120,
            "6": 100,
            "7": 80,
            "8": 20
        },
        "industry_distribution": {
            "1": 300,
            "2": 200,
            "3": 180,
            "4": 150,
            "5": 120,
            "6": 100,
            "7": 80,
            "8": 60,
            "9": 40,
            "10": 20,
            "11": 10,
            "12": 30
        },
        "monthly_trend": {
            "2025-01": 80,
            "2025-02": 95,
            "2025-03": 120,
            "2025-04": 110,
            "2025-05": 135,
            "2025-06": 150,
            "2025-07": 180
        },
        "cached_at": "2025-07-31T15:30:00Z"
    }
}
```

#### 状态码说明
- **200**: 获取成功
- **400**: 请求参数错误
- **500**: 服务器内部错误

#### cURL 请求示例

##### 基础统计查询
```bash
curl -X GET "http://127.0.0.1:8000/api/leads/statistics" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

##### 带筛选条件的统计查询
```bash
curl -X GET "http://127.0.0.1:8000/api/leads/statistics?status=1&region=1&date_range=2025-01-01,2025-07-31" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

---

## 接口测试

### 测试环境准备

1. **启动本地服务器**
```bash
php artisan serve
```

2. **确保数据库连接正常**
```bash
php artisan migrate:status
```

3. **创建测试数据**
```bash
php artisan db:seed --class=LeadSeeder
```

### 完整测试流程

#### 1. 测试创建线索
```bash
# 创建测试线索
curl -X POST "http://127.0.0.1:8000/api/leads" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "company_full_name": "测试科技有限公司",
    "company_short_name": "测试科技",
    "internal_name": "测试",
    "region": 1,
    "source": 1,
    "industry": 1,
    "status": 1,
    "stage": 1,
    "address": "北京市海淀区中关村大街1号",
    "creator_id": 1,
    "remark": "API测试创建的线索"
  }'
```

**预期结果**: 返回201状态码，包含新创建线索的完整信息

#### 2. 测试获取线索列表
```bash
# 获取线索列表
curl -X GET "http://127.0.0.1:8000/api/leads?page=1&per_page=5" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含分页的线索列表

#### 3. 测试获取线索详情
```bash
# 获取线索详情（假设线索ID为1）
curl -X GET "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含线索的详细信息

#### 4. 测试更新线索
```bash
# 更新线索状态
curl -X PUT "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "status": 2,
    "stage": 2,
    "remark": "已联系客户，表示有兴趣"
  }'
```

**预期结果**: 返回200状态码，包含更新后的线索信息

#### 5. 测试批量更新状态
```bash
# 批量更新线索状态
curl -X PATCH "http://127.0.0.1:8000/api/leads/batch-status" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "ids": [1, 2, 3],
    "status": 3
  }'
```

**预期结果**: 返回200状态码，包含批量更新的结果统计

#### 6. 测试获取统计信息
```bash
# 获取线索统计信息
curl -X GET "http://127.0.0.1:8000/api/leads/statistics" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码，包含各种统计数据

#### 7. 测试删除线索
```bash
# 删除线索（注意：某些状态的线索不允许删除）
curl -X DELETE "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回200状态码表示删除成功，或403状态码表示不允许删除

### 错误场景测试

#### 1. 测试参数验证
```bash
# 测试无效的分页参数
curl -X GET "http://127.0.0.1:8000/api/leads?page=0&per_page=200" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回400状态码，包含参数验证错误信息

#### 2. 测试重复公司名称
```bash
# 尝试创建重复公司名称的线索
curl -X POST "http://127.0.0.1:8000/api/leads" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "company_full_name": "测试科技有限公司",
    "company_short_name": "测试科技",
    "region": 1,
    "source": 1,
    "industry": 1,
    "creator_id": 1
  }'
```

**预期结果**: 返回422状态码，包含公司名称重复的错误信息

#### 3. 测试不存在的线索
```bash
# 尝试获取不存在的线索
curl -X GET "http://127.0.0.1:8000/api/leads/99999" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 返回404状态码，包含线索不存在的错误信息

### 性能测试

#### 1. 缓存效果测试
```bash
# 第一次查询（缓存未命中）
time curl -X GET "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"

# 第二次查询（缓存命中）
time curl -X GET "http://127.0.0.1:8000/api/leads/1" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*"
```

**预期结果**: 第二次查询的响应时间明显快于第一次

#### 2. 批量操作性能测试
```bash
# 批量更新大量线索
curl -X PATCH "http://127.0.0.1:8000/api/leads/batch-status" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  --noproxy "*" \
  -d '{
    "ids": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],
    "status": 2
  }'
```

**预期结果**: 批量操作应该在合理时间内完成（通常 < 1秒）

## 注意事项

1. **认证授权**: 实际生产环境中需要添加认证头信息
2. **速率限制**: 注意API调用频率限制
3. **数据一致性**: 批量操作可能存在部分成功的情况
4. **缓存策略**: 统计数据可能存在缓存延迟
5. **软删除**: 删除操作为软删除，数据仍保留在数据库中

## 更新日志

- **v1.0.0** (2025-07-31): 初始版本，包含基础CRUD和批量操作接口
- **v1.1.0** (2025-07-31): 添加统计接口和性能优化

# 错误日志使用指南

## 概述

当前系统已配置独立的 `error.log` 文件，专门用于记录错误级别及以上的日志信息。本指南将详细介绍如何使用和管理错误日志。

## 配置信息

### 日志通道配置

在 `config/logging.php` 中已配置 `error` 通道：

```php
'error' => [
    'driver'    => 'daily',
    'path'      => storage_path('logs/error.log'),
    'level'     => 'error',
    'days'      => 30,
],
```

**配置说明：**
- **driver**: `daily` - 按天轮转日志文件
- **path**: `storage/logs/error.log` - 日志文件路径
- **level**: `error` - 只记录 error 级别及以上的日志
- **days**: `30` - 保留30天的日志文件

### 日志文件命名

- **格式**: `error-YYYY-MM-DD.log`
- **示例**: `error-2025-07-31.log`
- **位置**: `storage/logs/` 目录

## 使用方法

### 1. 基本用法

#### 直接使用 Log 门面

```php
use Illuminate\Support\Facades\Log;

// 记录错误
Log::channel('error')->error('用户登录失败', [
    'user_id' => 123,
    'ip_address' => '*************',
    'attempt_count' => 3
]);

// 记录严重错误
Log::channel('error')->critical('数据库连接失败', [
    'database' => 'mysql',
    'host' => 'localhost',
    'error' => 'Connection refused'
]);

// 记录紧急错误
Log::channel('error')->emergency('服务器宕机', [
    'server' => 'web-01',
    'status' => 'down',
    'last_response' => '2024-01-15 10:30:00'
]);
```

### 2. 使用 ErrorLogService（推荐）

#### 服务注入使用

```php
use App\Services\ErrorLogService;

class UserController extends Controller
{
    protected ErrorLogService $errorLog;

    public function __construct(ErrorLogService $errorLog)
    {
        $this->errorLog = $errorLog;
    }

    public function login(Request $request)
    {
        try {
            // 登录逻辑
        } catch (\Exception $e) {
            $this->errorLog->logException($e, [
                'user_email' => $request->email,
                'ip_address' => $request->ip()
            ]);
            
            return response()->json(['error' => '登录失败'], 500);
        }
    }
}
```

#### 使用 ErrorLog Facade

```php
use App\Facades\ErrorLog;

// 记录一般错误
ErrorLog::logError('用户操作失败', [
    'user_id' => 123,
    'action' => 'update_profile'
]);

// 记录数据库错误
ErrorLog::logDatabaseError('INSERT', 'Duplicate entry', [
    'table' => 'users',
    'data' => ['email' => '<EMAIL>']
]);

// 记录API错误
ErrorLog::logApiError('/api/users', 500, 'Internal server error', [
    'request_id' => 'req_123456'
]);

// 记录业务错误
ErrorLog::logBusinessError('订单处理', '库存不足', [
    'order_id' => 'ORD_001',
    'product_id' => 'PROD_123'
]);

// 记录系统错误
ErrorLog::logSystemError('缓存系统', 'Redis连接失败', [
    'redis_host' => 'localhost:6379'
]);

// 记录异常
try {
    // 一些可能抛出异常的代码
} catch (\Exception $e) {
    ErrorLog::logException($e, ['additional_context' => 'value']);
}
```

## 日志级别

错误日志支持以下级别（按严重程度排序）：

1. **ERROR** - 一般错误，不影响系统运行
2. **CRITICAL** - 严重错误，可能影响系统功能
3. **ALERT** - 需要立即关注的错误
4. **EMERGENCY** - 系统不可用的紧急错误

## 日志格式

### 标准格式

```
[2025-07-31 10:20:27] local.ERROR: 错误消息 {"context":"data","trace_id":"uuid"}
```

### JSON格式（使用 ErrorLogService）

```json
{
    "timestamp": "2025-07-31T10:20:27.123Z",
    "level": "ERROR",
    "trace_id": "550e8400-e29b-41d4-a716-************",
    "message": "错误消息",
    "context": {
        "error_type": "database",
        "operation": "SELECT",
        "database_error": "Connection timeout"
    },
    "server": "web-01",
    "environment": "production"
}
```

## 日志管理

### 查看错误日志

```bash
# 查看今天的错误日志
tail -f storage/logs/error-$(date +%Y-%m-%d).log

# 查看最近的错误日志
ls -la storage/logs/error-*.log

# 搜索特定错误
grep "数据库" storage/logs/error-*.log

# 统计错误数量
wc -l storage/logs/error-$(date +%Y-%m-%d).log
```

### 日志轮转

系统自动按天轮转日志文件：
- 每天生成新的日志文件
- 自动删除30天前的日志文件
- 文件命名格式：`error-YYYY-MM-DD.log`

### 手动清理日志

```bash
# 删除7天前的错误日志
find storage/logs -name "error-*.log" -mtime +7 -delete

# 压缩旧的日志文件
gzip storage/logs/error-$(date -d "yesterday" +%Y-%m-%d).log
```

## 最佳实践

### 1. 错误分类

```php
// 按错误类型分类记录
ErrorLog::logDatabaseError('SELECT', $error, $context);
ErrorLog::logApiError($endpoint, $statusCode, $error, $context);
ErrorLog::logBusinessError($process, $error, $context);
ErrorLog::logSystemError($component, $error, $context);
```

### 2. 包含足够的上下文

```php
ErrorLog::logError('用户操作失败', [
    'user_id' => $user->id,
    'action' => 'update_profile',
    'input_data' => $request->validated(),
    'ip_address' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'timestamp' => now()->toISOString()
]);
```

### 3. 使用 trace_id 追踪请求

```php
// ErrorLogService 会自动生成 trace_id
ErrorLog::logError('错误消息', $context);

// 或者手动指定 trace_id
$traceId = request()->header('X-Request-ID');
ErrorLog::logError('错误消息', $context, $traceId);
```

### 4. 敏感信息处理

```php
// 避免记录敏感信息
ErrorLog::logError('登录失败', [
    'user_email' => $email,
    // 不要记录密码
    'attempt_count' => 3
]);
```

## 监控和告警

### 1. 日志监控脚本

```bash
#!/bin/bash
# 监控错误日志数量
ERROR_COUNT=$(grep -c "ERROR\|CRITICAL\|EMERGENCY" storage/logs/error-$(date +%Y-%m-%d).log)

if [ $ERROR_COUNT -gt 100 ]; then
    echo "错误日志数量过多: $ERROR_COUNT"
    # 发送告警
fi
```

### 2. 实时监控

```bash
# 实时监控错误日志
tail -f storage/logs/error-$(date +%Y-%m-%d).log | grep --color=always "CRITICAL\|EMERGENCY"
```

## 故障排除

### 1. 日志文件不存在

**检查步骤：**
```bash
# 检查目录权限
ls -la storage/logs/

# 检查配置
php artisan config:show logging.channels.error

# 手动创建日志
php artisan tinker
>>> Log::channel('error')->error('测试错误');
```

### 2. 日志写入失败

**检查步骤：**
```bash
# 检查磁盘空间
df -h

# 检查文件权限
chmod 755 storage/logs/
chown -R www-data:www-data storage/logs/

# 检查 SELinux（如果适用）
setsebool -P httpd_can_network_connect 1
```

### 3. 日志格式异常

**检查步骤：**
```bash
# 验证 JSON 格式
tail -n 1 storage/logs/error-$(date +%Y-%m-%d).log | jq .

# 检查编码
file storage/logs/error-$(date +%Y-%m-%d).log
```

## 性能考虑

### 1. 日志写入性能

- 使用异步日志写入（队列）
- 批量写入日志
- 定期清理旧日志文件

### 2. 存储优化

```php
// 配置日志压缩
'error' => [
    'driver' => 'daily',
    'path' => storage_path('logs/error.log'),
    'level' => 'error',
    'days' => 30,
    'tap' => [App\Logging\CompressLogFormatter::class],
],
```

## 总结

当前系统的错误日志功能提供了：

- ✅ **独立的错误日志文件**
- ✅ **按天轮转和自动清理**
- ✅ **多种使用方式**
- ✅ **丰富的上下文信息**
- ✅ **trace_id 追踪支持**
- ✅ **敏感信息过滤**

通过合理使用错误日志，可以有效监控系统健康状态，快速定位和解决问题。

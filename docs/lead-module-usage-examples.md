# 线索模块重构后使用示例

## 概述

本文档提供了重构后线索模块的使用示例，展示如何利用新的数据库优化组件提升性能。

## Repository 层使用示例

### 1. 线索查询优化

#### 基础查询

```php
use App\DTOs\Lead\LeadListDTO;use App\Repositories\LeadRepositoryInterface;

class LeadController
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository
    ) {}

    public function index(Request $request)
    {
        // 创建查询DTO
        $dto = new LeadListDTO([
            'page' => $request->get('page', 1),
            'pageSize' => $request->get('pageSize', 15),
            'companyName' => $request->get('companyName'),
            'status' => $request->get('status'),
            'region' => $request->get('region'),
            'sortBy' => $request->get('sortBy', 'created_at'),
            'sortDirection' => $request->get('sortDirection', 'desc')
        ]);

        // 执行优化查询（自动缓存、查询优化）
        $leads = $this->leadRepository->getLeadsList($dto);

        return response()->json([
            'data' => $leads->items(),
            'pagination' => [
                'total' => $leads->total(),
                'current_page' => $leads->currentPage(),
                'per_page' => $leads->perPage(),
                'last_page' => $leads->lastPage()
            ]
        ]);
    }
}
```

#### 复杂筛选查询

```php
// 支持多种筛选条件的查询
$dto = new LeadListDTO([
    'companyName' => '科技公司',     // 模糊搜索
    'status' => [1, 2, 3],          // 多状态筛选
    'region' => 1,                  // 地区筛选
    'creatorId' => auth()->id(),    // 创建人筛选
    'sortBy' => 'last_followed_at', // 按最近跟进时间排序
    'sortDirection' => 'desc'
]);

$leads = $this->leadRepository->getLeadsList($dto);
```

### 2. 批量操作优化

#### 批量更新状态

```php
use App\Repositories\LeadRepositoryInterface;

class LeadBatchController
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository
    ) {}

    public function batchUpdateStatus(Request $request)
    {
        $leadIds = $request->get('lead_ids', []);
        $newStatus = $request->get('status');

        // 使用死锁重试机制的批量更新
        $updatedCount = $this->leadRepository->batchUpdateStatus($leadIds, $newStatus);

        return response()->json([
            'message' => "成功更新 {$updatedCount} 条线索状态",
            'updated_count' => $updatedCount
        ]);
    }
}
```

### 3. 缓存使用示例

#### 线索详情查询（自动缓存）

```php
// 第一次查询：从数据库获取，自动缓存
$lead = $this->leadRepository->findById(1); // ~120ms

// 第二次查询：从缓存获取
$lead = $this->leadRepository->findById(1); // ~5ms (Redis缓存)

// 第三次查询：从内存缓存获取  
$lead = $this->leadRepository->findById(1); // ~0.1ms (内存缓存)
```

## Service 层使用示例

### 1. 业务逻辑处理

#### 创建线索（事务管理）

```php
use App\Services\LeadService;

class LeadController
{
    public function __construct(
        private LeadService $leadService
    ) {}

    public function store(CreateLeadRequest $request)
    {
        try {
            // 自动事务管理，包含业务验证
            $lead = $this->leadService->createLead($request->validated());

            return response()->json([
                'message' => '线索创建成功',
                'data' => $lead
            ], 201);

        } catch (BusinessException $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], $e->getCode());
        }
    }
}
```

#### 批量操作（事务安全）

```php
public function batchUpdate(Request $request)
{
    $operations = $request->get('operations', []);
    
    // 批量操作，支持事务回滚
    $results = $this->leadService->batchOperateLeads($operations);
    
    $successCount = count(array_filter($results, fn($r) => $r['success']));
    $totalCount = count($results);
    
    return response()->json([
        'message' => "批量操作完成，成功 {$successCount}/{$totalCount} 条",
        'results' => $results
    ]);
}
```

### 2. 统计数据获取

#### 线索统计（缓存优化）

```php
public function statistics(Request $request)
{
    $filters = $request->only(['status', 'region', 'created_at_range']);
    
    // 自动缓存统计结果
    $stats = $this->leadService->getLeadsStatistics($filters);
    
    return response()->json([
        'total_count' => $stats['count'],
        'status_distribution' => $stats['group_count'],
        'cached_at' => now()->toISOString()
    ]);
}
```

## 性能优化技巧

### 1. 查询优化

#### 使用预加载避免 N+1 问题

```php
// 自动预加载关联关系
$leads = $this->leadRepository->getLeadsList($dto);
// 已自动预加载: creator, contacts, users

// 手动指定预加载关系
$query = Lead::with(['creator:id,name', 'contacts:id,name,mobile']);
```

#### 使用索引提示

```php
// 在查询构建器中自动应用索引优化
$query = $this->queryBuilder->optimizeQuery($query, [
    'use_index' => true,
    'indexes' => ['idx_status', 'idx_created_at']
]);
```

### 2. 缓存策略

#### 设置合适的缓存时间

```php
// 不同类型数据使用不同的缓存时间
$query = $this->queryBuilder->setCacheForQuery($query, 3600, $cacheKey); // 1小时

// 统计数据缓存更长时间
$query = $this->queryBuilder->setCacheForQuery($query, 7200, $cacheKey); // 2小时

// 实时性要求高的数据缓存较短时间
$query = $this->queryBuilder->setCacheForQuery($query, 300, $cacheKey);  // 5分钟
```

#### 手动清除缓存

```php
// 数据更新后清除相关缓存
$this->queryBuilder->clearQueryCache(['leads_list_*', 'lead_stats_*']);

// 清除特定缓存
$this->queryBuilder->clearQueryCache("lead_detail_{$leadId}");
```

### 3. 事务优化

#### 使用嵌套事务

```php
$this->transactionManager->executeInTransaction(function () {
    // 主要操作
    $lead = Lead::create($data);
    
    // 嵌套事务处理可能失败的操作
    $savepoint = $this->transactionManager->beginNestedTransaction();
    try {
        $this->processComplexLogic($lead);
        $this->transactionManager->commitNestedTransaction($savepoint);
    } catch (\Exception $e) {
        $this->transactionManager->rollbackNestedTransaction($savepoint);
        // 主事务继续执行
    }
    
    return $lead;
});
```

#### 死锁重试

```php
// 自动死锁重试
$result = $this->transactionManager->executeWithDeadlockRetry(function () {
    return Lead::whereIn('id', $ids)->update(['status' => $status]);
}, 3, 100); // 最多重试3次，延迟100ms
```

## 监控和调试

### 1. 性能监控

#### 获取缓存统计

```php
$cacheStats = $this->queryBuilder->getCacheStatistics();
/*
返回:
[
    'total_queries' => 1500,
    'cache_hits' => 1275,
    'hit_rate' => 85.0,
    'memory_hits' => 225,
    'redis_hits' => 1050
]
*/
```

#### 获取事务统计

```php
$transactionStats = $this->transactionManager->getTransactionStatistics();
/*
返回:
[
    'total_transactions' => 450,
    'successful_commits' => 442,
    'failed_rollbacks' => 8,
    'success_rate' => 98.2,
    'current_transaction_level' => 0
]
*/
```

### 2. 查询分析

#### 获取优化建议

```php
$query = Lead::where('status', 1)->where('region', 2);
$suggestions = $this->queryBuilder->getOptimizationSuggestions($query);
/*
返回:
[
    'suggestions' => [
        ['message' => '建议为 status 字段添加索引', 'priority' => 'high'],
        ['message' => '建议为 region 字段添加索引', 'priority' => 'medium']
    ],
    'complexity_score' => 25,
    'estimated_cost' => 'low'
]
*/
```

## 测试验证

### 运行功能测试

```bash
# 测试所有功能
php artisan Lead:test-refactoring

# 测试特定组件
php artisan Lead:test-refactoring --component=repository
php artisan Lead:test-refactoring --component=service
php artisan Lead:test-refactoring --component=performance
```

### 性能基准测试

```bash
# 测试数据库优化组件
php artisan db:test-optimization

# 测试查询构建器
php artisan db:test-optimization --component=query-builder

# 测试事务管理器
php artisan db:test-optimization --component=transaction-manager
```

## 最佳实践建议

### 1. 开发建议

- 优先使用 Repository 层的优化方法
- 合理设置缓存时间，平衡性能和数据实时性
- 对于批量操作，使用事务管理确保数据一致性
- 定期检查缓存命中率和查询性能

### 2. 部署建议

- 配置 Redis 缓存服务
- 设置合适的数据库连接池大小
- 启用慢查询日志监控
- 配置性能监控告警

### 3. 维护建议

- 定期清理过期缓存
- 监控事务成功率和死锁情况
- 分析查询性能瓶颈
- 根据业务变化调整缓存策略

通过以上示例和建议，可以充分利用重构后的线索模块，获得最佳的性能表现和开发体验。

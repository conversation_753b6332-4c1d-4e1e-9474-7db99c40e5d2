# 数据库操作优化组件使用指南

## 概述

数据库操作优化组件为 Laravel CRM 项目提供了增强的查询构建和事务管理功能，旨在提升数据库操作的性能、安全性和可维护性。

## 组件架构

### 核心组件
- **QueryBuilderInterface**: 查询构建器接口
- **TransactionManagerInterface**: 事务管理器接口
- **EnhancedQueryBuilder**: 增强查询构建器实现
- **TransactionManager**: 事务管理器实现
- **DatabaseServiceProvider**: 服务提供者

### 设计原则
- 接口抽象，便于扩展和测试
- 依赖注入，松耦合设计
- 异常处理，完善的错误处理机制
- 性能优化，查询优化和缓存支持

## 快速开始

### 1. 依赖注入

在需要使用数据库优化功能的类中注入相应接口：

```php
use App\Contracts\QueryBuilderInterface;
use App\Contracts\TransactionManagerInterface;

class YourService
{
    public function __construct(
        private QueryBuilderInterface $queryBuilder,
        private TransactionManagerInterface $transactionManager
    ) {}
}
```

### 2. Repository 层集成

```php
class OptimizedRepository
{
    public function __construct(
        private Model $model,
        private QueryBuilderInterface $queryBuilder,
        private TransactionManagerInterface $transactionManager
    ) {}

    public function getList(array $filters): LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        
        // 使用增强查询构建器
        $query = $this->queryBuilder->buildComplexQuery($filters, $query);
        
        // 添加排序
        $sortRules = ['created_at:desc'];
        $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);
        
        // 优化查询
        $query = $this->queryBuilder->optimizeQuery($query);
        
        return $query->paginate(15);
    }
}
```

## 查询构建器功能

### 1. 复杂查询条件构建

```php
$conditions = [
    // 简单等值条件
    'status' => 1,
    
    // 复杂操作符条件
    'region' => [
        'operator' => 'in',
        'value' => [1, 2, 3]
    ],
    
    // 范围查询
    'created_at_range' => [
        'start' => '2024-01-01',
        'end' => '2024-12-31',
        'type' => 'date_range'
    ],
    
    // 搜索条件
    'search' => [
        'keyword' => '测试公司',
        'fields' => ['company_full_name', 'company_short_name'],
        'match_type' => 'like'
    ],
    
    // 关联查询条件
    'relations' => [
        'contacts' => [
            'status' => 1
        ]
    ]
];

$query = $this->queryBuilder->buildComplexQuery($conditions, $baseQuery);
```

### 2. 动态排序

```php
// 简单排序规则
$sortRules = [
    'created_at:desc',
    'status:asc'
];

// 复杂排序规则
$sortRules = [
    [
        'field' => 'priority',
        'direction' => 'desc',
        'nulls' => 'last'  // NULL 值排序位置
    ],
    [
        'field' => 'created_at',
        'direction' => 'asc'
    ]
];

$query = $this->queryBuilder->addDynamicSorting($query, $sortRules);
```

### 3. 范围查询

```php
// 日期范围查询
$query = $this->queryBuilder->addRangeCondition(
    $query,
    'created_at',
    '2024-01-01',
    '2024-12-31',
    'date_range'
);

// 数值范围查询
$query = $this->queryBuilder->addRangeCondition(
    $query,
    'price',
    100,
    500,
    'number_range'
);

// 标准 BETWEEN 查询
$query = $this->queryBuilder->addRangeCondition(
    $query,
    'score',
    60,
    100,
    'between'
);
```

### 4. 搜索条件

```php
// 模糊搜索
$query = $this->queryBuilder->addSearchCondition(
    $query,
    ['name', 'description'],
    '关键词',
    'like'
);

// 精确匹配
$query = $this->queryBuilder->addSearchCondition(
    $query,
    ['code'],
    'EXACT001',
    'exact'
);

// 前缀匹配
$query = $this->queryBuilder->addSearchCondition(
    $query,
    ['phone'],
    '138',
    'starts_with'
);
```

### 5. 查询优化

```php
// 基础优化
$query = $this->queryBuilder->optimizeQuery($query);

// 带选项的优化
$options = [
    'optimize_select' => true,
    'optimize_joins' => true,
    'use_index' => true,
    'indexes' => ['idx_status', 'idx_created_at'],
    'cache' => true,
    'cache_ttl' => 1800
];
$query = $this->queryBuilder->optimizeQuery($query, $options);
```

### 6. 聚合查询

```php
$aggregations = [
    'count' => ['field' => '*'],
    'sum' => ['field' => 'amount'],
    'avg' => ['field' => 'score'],
    'max' => ['field' => 'price'],
    'min' => ['field' => 'price'],
    'group_count' => ['field' => 'status']
];

$results = $this->queryBuilder->buildAggregateQuery($query, $aggregations);
```

### 7. 查询分析

```php
// 获取优化建议
$suggestions = $this->queryBuilder->getOptimizationSuggestions($query);

// 分析执行计划
$analysis = $this->queryBuilder->analyzeQueryPlan($query);

// 获取查询统计
$stats = $this->queryBuilder->getQueryStatistics($query);
```

## 事务管理器功能

### 1. 基础事务操作

```php
// 简单事务执行
$result = $this->transactionManager->executeInTransaction(function () {
    // 数据库操作
    $user = User::create($userData);
    $profile = Profile::create($profileData);
    return $user;
});

// 带重试的事务执行
$result = $this->transactionManager->executeInTransaction(
    function () {
        // 可能失败的操作
        return $this->performComplexOperation();
    },
    3,  // 最多重试3次
    60  // 超时时间60秒
);
```

### 2. 嵌套事务

```php
$this->transactionManager->executeInTransaction(function () {
    // 主事务操作
    $order = Order::create($orderData);
    
    // 创建嵌套事务
    $savepoint = $this->transactionManager->beginNestedTransaction();
    
    try {
        // 嵌套事务操作
        foreach ($items as $item) {
            OrderItem::create($item);
        }
        
        // 提交嵌套事务
        $this->transactionManager->commitNestedTransaction($savepoint);
        
    } catch (\Exception $e) {
        // 回滚嵌套事务
        $this->transactionManager->rollbackNestedTransaction($savepoint);
        throw $e;
    }
    
    return $order;
});
```

### 3. 死锁重试

```php
// 自动死锁重试
$result = $this->transactionManager->executeWithDeadlockRetry(
    function () {
        // 可能产生死锁的操作
        return $this->updateInventory($productId, $quantity);
    },
    5,    // 最大重试次数
    200   // 重试延迟（毫秒）
);
```

### 4. 批量事务

```php
$callbacks = [
    function () { return $this->createUser($userData1); },
    function () { return $this->createUser($userData2); },
    function () { return $this->createUser($userData3); },
];

// 遇错停止
$results = $this->transactionManager->executeBatchTransactions($callbacks, true);

// 遇错继续
$results = $this->transactionManager->executeBatchTransactions($callbacks, false);
```

### 5. 事务配置

```php
// 设置隔离级别
$this->transactionManager->setIsolationLevel('READ_COMMITTED');

// 设置超时时间
$this->transactionManager->setTimeout(120);

// 注册事务回调
$this->transactionManager->registerCallback('after_commit', function () {
    // 事务提交后执行
    $this->clearCache();
});
```

### 6. 事务监控

```php
// 获取事务统计
$stats = $this->transactionManager->getTransactionStatistics();

// 获取活动保存点
$savepoints = $this->transactionManager->getActiveSavepoints();

// 获取事务历史
$history = $this->transactionManager->getTransactionHistory(50);

// 清理过期保存点
$cleaned = $this->transactionManager->cleanupExpiredSavepoints(300);
```

## 实际应用示例

### 线索管理优化示例

```php
class OptimizedLeadRepository
{
    public function getLeadsList(LeadListDTO $dto): LengthAwarePaginator
    {
        $query = $this->model->newQuery()->with(['creator', 'contacts']);
        
        // 构建查询条件
        $conditions = [
            'status' => $dto->status,
            'region' => $dto->region,
            'search' => [
                'keyword' => $dto->companyName,
                'fields' => ['company_full_name', 'company_short_name'],
                'match_type' => 'like'
            ]
        ];
        
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
        
        // 添加排序
        $sortRules = [
            [
                'field' => $dto->sortBy ?? 'created_at',
                'direction' => $dto->sortDirection ?? 'desc'
            ]
        ];
        $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);
        
        // 优化查询
        $query = $this->queryBuilder->optimizeQuery($query, [
            'cache' => true,
            'cache_ttl' => 1800
        ]);
        
        return $query->paginate($dto->pageSize);
    }
    
    public function batchUpdateStatus(array $ids, int $status): int
    {
        return $this->transactionManager->executeWithDeadlockRetry(
            function () use ($ids, $status) {
                return $this->model->whereIn('id', $ids)->update(['status' => $status]);
            },
            3,
            100
        );
    }
}
```

## 配置选项

### 查询构建器配置

```php
// config/database-optimization.php
'query_builder' => [
    'max_complexity' => 100,
    'cache' => [
        'enabled' => true,
        'default_ttl' => 3600,
    ],
    'optimization' => [
        'auto_optimize' => true,
        'use_index_hints' => false,
    ],
],
```

### 事务管理器配置

```php
'transaction_manager' => [
    'default_timeout' => 30,
    'max_retries' => 3,
    'retry_delay' => 100,
    'savepoints' => [
        'max_age' => 300,
        'auto_cleanup' => true,
    ],
],
```

## 测试和验证

### 运行测试命令

```bash
# 测试所有组件
php artisan db:test-optimization

# 测试查询构建器
php artisan db:test-optimization --component=query-builder

# 测试事务管理器
php artisan db:test-optimization --component=transaction-manager
```

### 单元测试

```bash
# 运行单元测试
php artisan test tests/Unit/Services/Database/

# 运行特定测试
php artisan test tests/Unit/Services/Database/EnhancedQueryBuilderTest.php
```

## 性能优化建议

1. **查询优化**
   - 合理使用索引提示
   - 避免 SELECT *，明确指定字段
   - 使用查询缓存减少重复查询

2. **事务管理**
   - 保持事务尽可能短
   - 避免在事务中执行长时间操作
   - 合理设置超时时间

3. **监控和调试**
   - 启用慢查询监控
   - 定期分析查询执行计划
   - 监控事务统计信息

## 故障排除

### 常见问题

1. **查询复杂度过高**
   - 检查查询条件是否过于复杂
   - 调整 `max_complexity` 配置
   - 优化查询逻辑

2. **事务超时**
   - 检查事务中的操作是否耗时过长
   - 调整超时时间配置
   - 优化事务内的操作

3. **死锁问题**
   - 检查并发操作的锁顺序
   - 调整重试策略
   - 优化业务逻辑

### 调试技巧

1. 启用查询日志查看生成的 SQL
2. 使用 `getOptimizationSuggestions` 获取优化建议
3. 监控事务统计信息定位问题
4. 使用 `analyzeQueryPlan` 分析查询性能

# 数据库查询优化指南

## 概述

本文档总结了 CRM API 项目中数据库查询优化的最佳实践，特别是针对重复查询问题的识别和解决方案。

## 重复查询问题分析

### 常见重复查询场景

#### 1. Eloquent 关联数据重复加载

**问题描述**: 在同一个请求中多次加载相同的关联数据

**典型代码模式**:
```php
// 问题代码
$lead = $this->leadRepository->findById($id); // 第一次加载关联数据
// ... 业务逻辑处理
$lead->load(['creator', 'contacts', 'users']); // 重复加载相同数据
```

**解决方案**:
```php
// 优化代码
$lead = $this->leadRepository->findById($id); // 一次性加载所有需要的关联数据
// ... 业务逻辑处理
// 移除不必要的 load() 调用
```

#### 2. 嵌套事务导致的重复查询

**问题描述**: Service 层和 Repository 层都使用事务管理，导致嵌套事务和重复查询

**解决方案**: 事务管理统一在 Service 层处理，Repository 层专注数据访问

### 识别重复查询的方法

#### 1. Laravel Telescope 监控

**查看查询详情**:
- 访问 `/telescope/queries`
- 查看 "duplicated" 标记
- 分析查询执行时间和频率

**关键指标**:
- 查询总数
- 重复查询数量
- 总执行时间

#### 2. 日志分析

**查询日志格式**:
```
[2025-08-01 15:59:08] local.DEBUG: Database Query: select * from `crm_lead` where `id` = 1 and `crm_lead`.`deleted_at` is null limit 1 [1.73ms]
```

**分析要点**:
- 相同 SQL 语句的重复执行
- 查询执行时间分布
- 查询执行顺序

## 优化策略

### 1. Eloquent 关联预加载优化

#### 使用 with() 预加载

```php
// ✅ 正确示例
public function findById(int $id): ?Lead
{
    return $this->model->with([
        'creator:id,name',
        'contacts:id,name,mobile',
        'users:id,name'
    ])->find($id);
}
```

#### 避免不必要的 load() 调用

```php
// ❌ 错误示例
$lead = $this->leadRepository->findById($id);
$lead->load(['creator', 'contacts']); // 数据已经预加载

// ✅ 正确示例
$lead = $this->leadRepository->findById($id);
// 直接使用预加载的数据，无需再次加载
```

### 2. 查询缓存策略

#### 模型级缓存

```php
public function findById(int $id): ?Lead
{
    return Cache::remember("lead:{$id}", 300, function () use ($id) {
        return $this->model->with(['creator', 'contacts', 'users'])->find($id);
    });
}
```

#### 查询结果缓存

```php
public function getActiveLeads(): Collection
{
    return Cache::remember('leads:active', 600, function () {
        return $this->model->where('status', Lead::STATUS_ACTIVE)->get();
    });
}
```

### 3. 事务管理优化

#### 统一事务边界

```php
// Service 层管理事务
public function updateLead(int $id, LeadUpdateDTO $dto): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
        // 业务逻辑
        $lead = $this->leadRepository->findById($id);
        $this->leadRepository->update($id, $dto->toArray());
        return $lead;
    });
}

// Repository 层不使用事务
public function update(int $id, array $data): bool
{
    return $this->model->where('id', $id)->update($data) > 0;
}
```

## 性能监控

### 1. 关键性能指标

- **查询数量**: 单个请求的数据库查询总数
- **重复查询**: 相同 SQL 语句的重复执行次数
- **查询时间**: 数据库查询总耗时
- **慢查询**: 超过阈值的查询语句

### 2. 监控工具

#### Laravel Telescope

**优势**:
- 实时查询监控
- 重复查询检测
- 查询性能分析

**使用方法**:
```php
// 在 TelescopeServiceProvider 中配置
Telescope::filter(function (IncomingEntry $entry) {
    return $entry->type === 'query' && $entry->content['time'] > 100;
});
```

#### 数据库查询日志

**配置**:
```php
// config/database.php
'connections' => [
    'mysql' => [
        'options' => [
            PDO::ATTR_EMULATE_PREPARES => false,
        ],
        'dump' => [
            'dump_binary_columns' => true,
        ],
    ],
],
```

### 3. 性能基准

#### 查询数量基准

- **简单查询**: ≤ 5 个查询
- **复杂业务**: ≤ 10 个查询
- **重复查询**: 0 个

#### 响应时间基准

- **数据库查询**: ≤ 50ms
- **单个查询**: ≤ 10ms
- **API 响应**: ≤ 200ms

## 最佳实践

### 1. 代码编写规范

#### Repository 层

```php
interface LeadRepositoryInterface
{
    public function findById(int $id): ?Lead;
    public function findWithRelations(int $id, array $relations): ?Lead;
}

class LeadRepository implements LeadRepositoryInterface
{
    public function findById(int $id): ?Lead
    {
        return $this->model->with([
            'creator:id,name',
            'contacts:id,name,mobile',
            'users:id,name'
        ])->find($id);
    }
}
```

#### Service 层

```php
class LeadService
{
    public function updateLead(int $id, LeadUpdateDTO $dto): Lead
    {
        return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
            $lead = $this->leadRepository->findById($id);
            
            if (!$lead) {
                throw BusinessException::fromErrorCode('Lead.not_found');
            }
            
            $this->leadRepository->update($id, $dto->toArray());
            
            // 直接在现有模型上应用更新，避免重复查询
            foreach ($dto->toArray() as $key => $value) {
                $lead->setAttribute($key, $value);
            }
            
            return $lead;
        });
    }
}
```

### 2. 代码审查要点

#### 查询优化检查

- [ ] 是否存在 N+1 查询问题
- [ ] 是否有不必要的重复查询
- [ ] 是否正确使用了预加载
- [ ] 是否有慢查询风险

#### 事务管理检查

- [ ] 事务边界是否合理
- [ ] 是否存在嵌套事务
- [ ] 是否有死锁风险

### 3. 持续优化

#### 定期性能审查

- **频率**: 每月进行一次性能审查
- **内容**: 分析慢查询日志，识别优化机会
- **行动**: 制定优化计划并实施

#### 自动化监控

```php
// 在 AppServiceProvider 中添加查询监控
DB::listen(function ($query) {
    if ($query->time > 100) {
        Log::warning('Slow query detected', [
            'sql' => $query->sql,
            'time' => $query->time,
            'bindings' => $query->bindings,
        ]);
    }
});
```

## 案例研究：线索更新接口优化

### 优化前

- **查询数量**: 10个
- **重复查询**: 3组（6个重复查询）
- **总耗时**: 10.52ms

### 优化后

- **查询数量**: 7个（减少30%）
- **重复查询**: 0个（完全消除）
- **总耗时**: 12.63ms（查询效率提升）

### 关键优化点

1. **移除不必要的 load() 调用**
2. **充分利用预加载机制**
3. **统一事务管理边界**

## 总结

数据库查询优化是一个持续的过程，需要：

1. **建立监控机制**: 使用工具持续监控查询性能
2. **遵循最佳实践**: 正确使用 Eloquent ORM 特性
3. **定期审查优化**: 识别和解决性能瓶颈
4. **团队协作**: 在代码审查中关注性能问题

通过系统性的优化方法，可以显著提升应用性能和用户体验。

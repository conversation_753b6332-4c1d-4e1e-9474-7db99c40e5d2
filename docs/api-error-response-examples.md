# API 错误响应格式示例

## 概述

本文档展示了 API 的标准错误响应格式，所有错误响应都使用统一的结构，包含 `code`、`message` 和 `errors` 字段。

## 响应格式规范

### 基本结构
```json
{
    "code": 错误状态码,
    "message": "用户友好的错误描述",
    "errors": {
        // 详细错误信息（开发环境）或空对象（生产环境）
        "trace_id": "请求追踪ID（可选）"
    }
}
```

## 常见错误响应示例

### 1. 验证错误 (422)
```json
{
    "code": 422,
    "message": "数据验证失败",
    "errors": {
        "email": [
            "邮箱格式不正确"
        ],
        "password": [
            "密码长度至少6位",
            "密码必须包含字母和数字"
        ],
        "age": [
            "年龄必须是数字"
        ],
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 2. 认证错误 (401)
```json
{
    "code": 401,
    "message": "未授权，请先登录",
    "errors": {
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 3. 权限错误 (403)
```json
{
    "code": 403,
    "message": "您没有权限执行此操作",
    "errors": {
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 4. 资源未找到 (404)
```json
{
    "code": 404,
    "message": "您访问的资源不存在",
    "errors": {
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 5. 业务逻辑错误 (400)
```json
{
    "code": 400,
    "message": "用户名已存在",
    "errors": {
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 6. 服务器内部错误 (500) - 开发环境
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "error_type": "PHP_ERROR",
        "severity": "E_ERROR",
        "severity_name": "E_ERROR",
        "message": "Call to undefined function nonExistentFunction()",
        "file": "/var/www/app/Http/Controllers/TestController.php",
        "line": 25,
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 7. 服务器内部错误 (500) - 生产环境
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 8. PHP 致命错误 (500) - 开发环境
```json
{
    "code": 500,
    "message": "服务器发生致命错误",
    "errors": {
        "error_type": "FATAL_ERROR",
        "severity": "E_ERROR",
        "message": "Maximum execution time of 30 seconds exceeded",
        "file": "/var/www/app/Http/Controllers/TestController.php",
        "line": 45,
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 9. 异常错误 (500) - 开发环境
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "exception_class": "InvalidArgumentException",
        "message": "Invalid argument provided",
        "file": "/var/www/app/Services/UserService.php",
        "line": 123,
        "trace": [
            {
                "file": "/var/www/app/Http/Controllers/UserController.php",
                "line": 45,
                "function": "createUser",
                "class": "App\\Services\\UserService"
            }
        ],
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 10. 复杂验证错误 (422)
```json
{
    "code": 422,
    "message": "数据验证失败",
    "errors": {
        "user": {
            "name": ["姓名不能为空"],
            "email": ["邮箱格式不正确"]
        },
        "profile": {
            "phone": ["手机号格式不正确"],
            "address": {
                "city": ["城市不能为空"],
                "street": ["街道地址不能为空"]
            }
        },
        "trace_id": "550e8400-e29b-41d4-a716-************"
    }
}
```

## 成功响应格式（对比）

### 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 123,
        "name": "张三",
        "email": "<EMAIL>",
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

## 字段说明

### 必需字段

#### code
- **类型**: Integer
- **说明**: HTTP 状态码，与响应的 HTTP 状态码保持一致
- **示例**: 200, 400, 401, 403, 404, 422, 500

#### message
- **类型**: String
- **说明**: 用户友好的错误描述，适合直接展示给用户
- **示例**: "数据验证失败", "服务器内部错误"

#### errors
- **类型**: Object
- **说明**: 详细的错误信息，始终为对象类型
- **开发环境**: 包含详细的调试信息
- **生产环境**: 通常为空对象 `{}`
- **trace_id 字段**:
  - **类型**: String (UUID)
  - **说明**: 请求追踪ID，位于 errors 对象内部，用于日志关联和问题排查
  - **示例**: "550e8400-e29b-41d4-a716-************"

## 错误类型分类

### 客户端错误 (4xx)
- **400 Bad Request**: 请求参数错误、业务逻辑错误
- **401 Unauthorized**: 未认证、token 过期
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **422 Unprocessable Entity**: 数据验证失败

### 服务器错误 (5xx)
- **500 Internal Server Error**: 服务器内部错误
- **502 Bad Gateway**: 网关错误
- **503 Service Unavailable**: 服务不可用
- **504 Gateway Timeout**: 网关超时

## 前端处理建议

### JavaScript 示例
```javascript
// 处理 API 响应
async function handleApiResponse(response) {
    const data = await response.json();
    
    if (response.ok) {
        // 成功响应
        return data.data;
    } else {
        // 错误响应
        const error = new Error(data.message);
        error.code = data.code;
        error.errors = data.errors;
        error.traceId = data.trace_id;
        throw error;
    }
}

// 处理验证错误
function handleValidationErrors(errors) {
    Object.keys(errors).forEach(field => {
        const messages = Array.isArray(errors[field]) 
            ? errors[field] 
            : [errors[field]];
        
        // 显示字段错误
        showFieldError(field, messages);
    });
}
```

### 错误处理最佳实践
1. **检查 HTTP 状态码**: 首先检查响应的 HTTP 状态码
2. **解析错误信息**: 从 `message` 字段获取用户友好的错误描述
3. **处理详细错误**: 从 `errors` 字段获取详细的错误信息
4. **记录追踪ID**: 保存 `trace_id` 用于问题报告
5. **用户体验**: 向用户显示 `message`，向开发者显示 `errors`

## 测试验证

### 使用命令行工具验证
```bash
# 验证错误响应格式
php artisan error:validate-format

# 测试所有错误类型
php artisan error:test --all

# 测试特定错误
php artisan error:test exception
```

### 手动测试
```bash
# 测试验证错误
curl -X POST http://localhost:8000/api/users \
     -H "Content-Type: application/json" \
     -d '{"email": "invalid-email"}'

# 测试认证错误
curl -X GET http://localhost:8000/api/protected-resource

# 测试服务器错误
curl -X GET http://localhost:8000/api/test/errors/exception
```

通过这套标准化的错误响应格式，可以确保 API 的一致性和可预测性，为前端开发和 API 集成提供良好的开发体验。

# UnitFormatter 数值单位格式化工具使用指南

## 概述

UnitFormatter 是一个专门用于数值单位格式化的工具类，提供统一的格式化标准，自动选择合适的单位，并保持良好的可读性。

## 核心功能

### 1. 内存大小格式化

自动选择合适的内存单位（B、KB、MB、GB），保留2位小数，移除无意义的尾随零。

```php
use App\Utils\UnitFormatter;

// 基本用法
echo UnitFormatter::formatMemory(1024);        // "1 KB"
echo UnitFormatter::formatMemory(1536000);     // "1.46 MB"
echo UnitFormatter::formatMemory(2147483648);  // "2 GB"
echo UnitFormatter::formatMemory(512);         // "512 B"

// 实际应用
$memoryUsage = memory_get_usage(true);
Log::info('内存使用情况', [
    'memory_usage' => UnitFormatter::formatMemory($memoryUsage)
]);
```

### 2. 时间格式化

支持简单格式和复合格式，自动选择合适的时间单位（μs、ms、s、min、h）。

```php
// 简单格式（单一单位）
echo UnitFormatter::formatTime(0.001234, false);  // "1.23 ms"
echo UnitFormatter::formatTime(65.5, false);      // "65.5 s"
echo UnitFormatter::formatTime(3665.5, false);    // "1.02 h"

// 复合格式（多单位组合）
echo UnitFormatter::formatTime(65.5, true);       // "1 min 5.5 s"
echo UnitFormatter::formatTime(3665.5, true);     // "1 h 1 min 5.5 s"
echo UnitFormatter::formatTime(7325.75, true);    // "2 h 2 min 5.75 s"

// 实际应用
$startTime = microtime(true);
// ... 执行业务逻辑
$duration = microtime(true) - $startTime;
Log::info('操作完成', [
    'duration' => UnitFormatter::formatTime($duration)
]);
```

### 3. 数据传输速率格式化

格式化网络传输速率，单位为 /s（每秒）。

```php
echo UnitFormatter::formatDataRate(1024);      // "1 KB/s"
echo UnitFormatter::formatDataRate(1536000);   // "1.46 MB/s"
echo UnitFormatter::formatDataRate(1073741824); // "1 GB/s"
```

### 4. 百分比格式化

格式化百分比数值，支持自定义精度。

```php
echo UnitFormatter::formatPercentage(85.6789);     // "85.68%"
echo UnitFormatter::formatPercentage(100.0);       // "100%"
echo UnitFormatter::formatPercentage(85.6789, 1);  // "85.7%"
echo UnitFormatter::formatPercentage(85.6789, 3);  // "85.679%"
```

### 5. 数量格式化

为大数字添加千分位分隔符，提高可读性。

```php
echo UnitFormatter::formatCount(1234567);    // "1,234,567"
echo UnitFormatter::formatCount(1000);       // "1,000"
echo UnitFormatter::formatCount(999);        // "999"
```

## 集成示例

### 在事务日志中使用

```php
// SimpleTransactionLogger 中的使用
$context = [
    'duration' => UnitFormatter::formatTime(microtime(true) - $startTime),
    'memory_usage' => UnitFormatter::formatMemory(memory_get_usage(true)),
    'memory_peak' => UnitFormatter::formatMemory(memory_get_peak_usage(true)),
    'memory_diff' => UnitFormatter::formatMemory(memory_get_usage(true) - $startMemory),
];
```

### 在性能监控中使用

```php
// 查询性能监控
public function monitorQueryPerformance(callable $queryCallback): array
{
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);

    $result = $queryCallback();

    $executionTime = microtime(true) - $startTime;
    $memoryUsage = memory_get_usage(true) - $startMemory;

    return [
        'result' => $result,
        'performance' => [
            'execution_time' => UnitFormatter::formatTime($executionTime),
            'memory_usage' => UnitFormatter::formatMemory($memoryUsage),
            'result_count' => UnitFormatter::formatCount(count($result))
        ]
    ];
}
```

### 在健康检查中使用

```php
// LogHealthMonitorService 中的使用
$durationSeconds = microtime(true) - $startTime;
$formattedDuration = UnitFormatter::formatTime($durationSeconds);

return [
    'healthy' => $durationMs < $threshold,
    'message' => "Log write performance OK: {$formattedDuration}",
    'details' => [
        'duration' => $formattedDuration,
        'memory_usage' => UnitFormatter::formatMemory(memory_get_usage(true))
    ]
];
```

## 技术特性

### 自动单位选择

- **内存**: 根据字节数自动选择 B、KB、MB、GB
- **时间**: 根据秒数自动选择 μs、ms、s、min、h
- **智能阈值**: 使用合理的阈值确保数值在 1-1024 范围内

### 精度控制

- **内存**: 固定2位小数精度
- **时间**: 根据单位和数值大小动态调整精度
- **百分比**: 支持自定义精度（默认2位）

### 尾随零处理

- 智能移除小数点后的无意义尾随零
- 保留整数部分的有效数字
- 移除空的小数点

### 复合时间格式

- 支持多单位组合显示（如 "1 h 30 min 5.5 s"）
- 自动省略为零的单位
- 保持适当的精度

## 最佳实践

### 1. 日志记录

```php
// ✅ 推荐：使用格式化后的值提高可读性
Log::info('操作完成', [
    'duration' => UnitFormatter::formatTime($duration),
    'memory_usage' => UnitFormatter::formatMemory($memoryUsage)
]);

// ❌ 不推荐：使用原始数值
Log::info('操作完成', [
    'duration_ms' => round($duration * 1000, 2),
    'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2)
]);
```

### 2. 性能监控

```php
// ✅ 推荐：同时保留原始值和格式化值
$performanceMetrics = [
    'execution_time' => UnitFormatter::formatTime($executionTime),
    'execution_time_raw' => $executionTime, // 用于数值比较
    'memory_usage' => UnitFormatter::formatMemory($memoryUsage),
    'memory_usage_raw' => $memoryUsage // 用于数值比较
];
```

### 3. API 响应

```php
// ✅ 推荐：在用户界面显示中使用格式化值
return [
    'statistics' => [
        'total_records' => UnitFormatter::formatCount($totalRecords),
        'processing_time' => UnitFormatter::formatTime($processingTime),
        'memory_peak' => UnitFormatter::formatMemory($memoryPeak)
    ]
];
```

## 注意事项

1. **向后兼容性**: 现有代码中的原始数值字段保持不变，新增格式化字段
2. **性能考虑**: 格式化操作轻量级，适合在日志记录中频繁使用
3. **数值比较**: 需要数值比较时使用原始值，显示时使用格式化值
4. **国际化**: 当前使用英文单位，未来可考虑支持多语言

## 测试覆盖

UnitFormatter 包含完整的单元测试套件：
- 52 个测试用例
- 70 个断言
- 覆盖所有格式化方法
- 包含边界值和异常情况测试

运行测试：
```bash
./vendor/bin/phpunit tests/Unit/Utils/UnitFormatterTest.php
```

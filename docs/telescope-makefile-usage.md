# Telescope Makefile 使用指南

本文档介绍如何使用 Makefile 中的 Telescope 相关命令来管理 Laravel Telescope 监控工具。

## 安装和配置

### 1. 安装 Telescope

```bash
make telescope-install
```

这个命令会：
- 通过 Composer 安装 Laravel Telescope
- 发布 Telescope 资源文件
- 运行数据库迁移
- 创建必要的数据库表

### 2. 发布配置文件

```bash
make telescope-publish
```

发布 Telescope 的配置文件和迁移文件，用于自定义配置。

### 3. 配置环境变量

```bash
make telescope-config
```

显示推荐的环境变量配置，然后手动添加到 `.env` 文件中。

或者使用快捷命令：

```bash
# 启用 Telescope
make telescope-enable

# 禁用 Telescope  
make telescope-disable
```

## 日常管理

### 检查状态

```bash
make telescope-status
```

显示：
- Telescope 启用状态
- 慢查询阈值配置
- 访问地址
- 数据库表状态

### 清理数据

```bash
# 清除所有 Telescope 数据
make telescope-clear

# 清理 48 小时前的数据
make telescope-prune
```

### 测试功能

```bash
make telescope-test
```

显示测试端点信息，用于验证 Telescope 监控功能。

## 推荐工作流程

### 开发环境设置

```bash
# 1. 安装 Telescope
make telescope-install

# 2. 启用监控
make telescope-enable

# 3. 清除缓存
make cache-clear

# 4. 启动开发服务器
make serve
```

### 定期维护

```bash
# 每周清理过期数据
make telescope-prune

# 检查状态
make telescope-status
```

### 生产环境

```bash
# 生产环境应禁用 Telescope
make telescope-disable
```

## 访问 Telescope

安装完成后，可以通过以下地址访问 Telescope 界面：

```
http://127.0.0.1:8000/telescope
```

## 环境变量说明

```bash
# 启用/禁用 Telescope
TELESCOPE_ENABLED=true

# 慢查询阈值（毫秒）
TELESCOPE_SLOW_QUERY_THRESHOLD=100
```

## 注意事项

1. **生产环境安全**：生产环境中应该禁用 Telescope 或限制访问权限
2. **性能影响**：Telescope 会记录大量数据，可能影响应用性能
3. **存储空间**：定期清理 Telescope 数据以节省存储空间
4. **数据敏感性**：Telescope 会记录请求数据，注意敏感信息的处理

## 故障排除

### Telescope 无法访问

```bash
# 检查状态
make telescope-status

# 清除缓存
make cache-clear

# 重新启动服务器
make stop
make serve
```

### 数据库表不存在

```bash
# 重新运行迁移
php artisan migrate

# 或重新安装
make telescope-install
```

### 性能问题

```bash
# 清理过期数据
make telescope-prune

# 调整慢查询阈值
# 编辑 .env 文件中的 TELESCOPE_SLOW_QUERY_THRESHOLD
```

# 线索功能 DTO 架构设计文档

## 概述

本文档描述了 Laravel CRM API 项目中线索功能采用的 DTO（Data Transfer Object）架构设计，包括设计原理、实现方案和使用指南。

## 设计原理

### 1. DTO 模式的优势

#### 1.1 类型安全性

- **编译时检查**：通过 PHP 类型声明和 PHPStan 静态分析，在编译时发现类型错误
- **强类型约束**：避免运行时的类型转换错误和数据格式问题
- **IDE 支持**：提供完整的类型提示和自动补全功能

#### 1.2 代码可读性

- **明确的属性定义**：使用对象属性替代数组键值访问
- **业务语义清晰**：属性名直接反映业务含义
- **结构化数据**：清晰的数据结构定义

#### 1.3 维护性

- **单一数据源**：数据结构定义集中在 DTO 类中
- **变更影响可控**：结构变更时影响范围明确
- **业务逻辑封装**：相关的数据处理逻辑集中管理

### 2. 架构层次

```
┌─────────────────┐
│   Controller    │ ← HTTP 请求处理
├─────────────────┤
│   Request       │ ← 数据验证
├─────────────────┤
│     DTO         │ ← 数据传输对象 (新增层)
├─────────────────┤
│   Service       │ ← 业务逻辑处理
├─────────────────┤
│  Repository     │ ← 数据访问
├─────────────────┤
│    Model        │ ← 数据模型
└─────────────────┘
```

## 实现方案

### 1. 基础架构

#### 1.1 BaseDTO 抽象类

```php
abstract class BaseDTO implements JsonSerializable
{
    // 通用的数据转换方法
    public static function fromArray(array $data): static;
    abstract public function toArray(): array;
    public function jsonSerialize(): array;
    
    // 数据类型转换工具方法
    protected function toInt(mixed $value): ?int;
    protected function toString(mixed $value): ?string;
    protected function toDateTime(mixed $value): ?string;
}
```

#### 1.2 LeadCreateDTO 实现

```php
class LeadCreateDTO extends BaseDTO
{
    // 使用 readonly 属性确保不可变性
    public readonly string $companyFullName;
    public readonly string $companyShortName;
    public readonly ?string $internalName;
    // ... 其他属性
    
    // 从 Request 创建 DTO
    public static function fromRequest(CreateLeadRequest $request): static;
    
    // 业务逻辑方法
    public function getDisplayName(): string;
    public function hasFollowUpTime(): bool;
    public function validateBusinessRules(): array;
}
```

### 2. 数据流设计

#### 2.1 创建流程

```
CreateLeadRequest → validated() → LeadCreateDTO → LeadService → Repository
```

#### 2.2 更新流程

```
UpdateLeadRequest → validated() → LeadUpdateDTO → LeadService → Repository
```

### 3. 核心特性

#### 3.1 不可变性

- 使用 `readonly` 属性确保 DTO 对象创建后不可修改
- 提供数据一致性保证
- 避免意外的数据变更

#### 3.2 类型转换

- 自动处理数据类型转换（字符串、整数、日期时间）
- 提供空值安全处理
- 统一的数据格式化逻辑

#### 3.3 业务逻辑封装

- 默认值处理逻辑
- 数据验证方法
- 业务规则检查
- 标签获取方法

#### 3.4 向下兼容

- 提供 `toArray()` 方法与现有 Repository 层兼容
- 保持现有 Request 验证机制
- 不影响现有的异常处理和事务管理

## 使用指南

### 1. Controller 层使用

```php
public function store(CreateLeadRequest $request): JsonResponse
{
    // 创建 DTO 对象
    $dto = LeadCreateDTO::fromRequest($request);
    
    // 调用服务层
    $lead = $this->leadService->createLead($dto);
    
    return ApiResponse::success(new LeadResource($lead), '创建线索成功');
}
```

### 2. Service 层使用

```php
public function createLead(LeadCreateDTO $dto): mixed
{
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        // 业务规则验证
        $validationErrors = $dto->validateBusinessRules();
        if (!empty($validationErrors)) {
            throw BusinessException::fromErrorCode('Lead.validation_failed');
        }
        
        // 业务逻辑处理
        if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
            throw BusinessException::fromErrorCode('Lead.company_already_exists');
        }
        
        // 数据持久化
        return $this->leadRepository->create($dto->toArray());
    });
}
```

### 3. 业务逻辑方法

```php
// 获取显示名称
$displayName = $dto->getDisplayName();

// 检查是否有跟进时间
if ($dto->hasFollowUpTime()) {
    // 处理跟进逻辑
}

// 获取状态标签
$statusLabel = $dto->getStatusLabel();

// 验证业务规则
$errors = $dto->validateBusinessRules();
```

## 最佳实践

### 1. DTO 设计原则

#### 1.1 单一职责

- 每个 DTO 只负责特定的数据传输场景
- 避免在一个 DTO 中混合多种业务逻辑

#### 1.2 不可变性

- 使用 `readonly` 属性
- 避免提供 setter 方法
- 通过构造函数初始化所有属性

#### 1.3 类型安全

- 明确定义所有属性的类型
- 使用联合类型处理可选字段
- 提供类型转换方法

### 2. 命名约定

#### 2.1 类命名

- 创建：`{Entity}CreateDTO`
- 更新：`{Entity}UpdateDTO`
- 查询：`{Entity}ListDTO`

#### 2.2 方法命名

- 工厂方法：`fromRequest()`, `fromArray()`
- 转换方法：`toArray()`, `jsonSerialize()`
- 业务方法：`get{Property}()`, `has{Property}()`, `is{Property}()`

### 3. 性能考虑

#### 3.1 对象创建开销

- DTO 对象创建有一定开销，但相比数组访问的类型安全收益是值得的
- 在高频调用场景中可以考虑对象池或缓存机制

#### 3.2 内存使用

- `readonly` 属性有助于 PHP 优化内存使用
- 避免在 DTO 中存储大量数据或复杂对象

## 扩展指南

### 1. 新增 DTO

创建新的 DTO 类时，请遵循以下步骤：

1. 继承 `BaseDTO` 抽象类
2. 定义 `readonly` 属性
3. 实现构造函数和数据转换逻辑
4. 添加 `fromRequest()` 静态方法
5. 实现 `toArray()` 方法
6. 添加必要的业务逻辑方法
7. 编写单元测试

### 2. 复杂场景处理

#### 2.1 嵌套 DTO

```php
class OrderCreateDTO extends BaseDTO
{
    public readonly CustomerCreateDTO $customer;
    public readonly array $items; // OrderItemDTO[]
}
```

#### 2.2 条件验证

```php
public function validateBusinessRules(): array
{
    $errors = [];
    
    if ($this->type === 'enterprise' && $this->contactCount < 2) {
        $errors[] = '企业客户至少需要2个联系人';
    }
    
    return $errors;
}
```

## 总结

DTO 模式的引入显著提升了线索功能的代码质量和开发体验：

1. **类型安全**：从运行时检查提升到编译时检查
2. **代码可读性**：明确的属性定义和业务语义
3. **维护性**：集中的数据结构定义和业务逻辑
4. **开发体验**：完整的 IDE 支持和自动补全
5. **扩展性**：为其他模块提供了可复用的架构模式

建议在后续的功能开发中继续采用 DTO 模式，逐步提升整个项目的代码质量和架构一致性。

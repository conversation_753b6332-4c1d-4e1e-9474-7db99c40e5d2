# Redis 服务使用指南

## 概述

Redis 服务组件为 Laravel CRM 项目提供了统一的 Redis 操作接口，支持数据序列化、过期时间设置、批量操作等功能。

## 基本使用

### 依赖注入

在控制器、服务类或其他需要使用 Redis 的地方，通过依赖注入获取 Redis 服务实例：

```php
<?php

namespace App\Http\Controllers;

use App\Contracts\RedisServiceInterface;
use Illuminate\Http\JsonResponse;

class ExampleController extends Controller
{
    protected RedisServiceInterface $redisService;

    public function __construct(RedisServiceInterface $redisService)
    {
        $this->redisService = $redisService;
    }

    public function store(): JsonResponse
    {
        // 使用 Redis 服务
        $this->redisService->set('user:1', ['name' => '张三', 'age' => 25], 3600);
        
        return response()->json(['message' => '数据已缓存']);
    }
}
```

### 基础操作

#### 设置和获取数据

```php
// 设置键值对（永不过期）
$redisService->set('user:profile', ['name' => '李四', 'email' => '<EMAIL>']);

// 设置键值对（1小时后过期）
$redisService->set('temp:token', 'abc123', 3600);

// 获取数据
$profile = $redisService->get('user:profile');
$token = $redisService->get('temp:token', 'default_value');
```

#### 检查和删除

```php
// 检查键是否存在
if ($redisService->exists('user:profile')) {
    echo "用户资料存在";
}

// 删除键
$redisService->delete('temp:token');
```

#### 过期时间操作

```php
// 设置过期时间（秒）
$redisService->expire('user:session', 1800);

// 获取剩余过期时间
$ttl = $redisService->ttl('user:session');
if ($ttl > 0) {
    echo "还有 {$ttl} 秒过期";
}
```

### 批量操作

#### 批量设置和获取

```php
// 批量设置
$data = [
    'user:1' => ['name' => '张三', 'age' => 25],
    'user:2' => ['name' => '李四', 'age' => 30],
    'user:3' => ['name' => '王五', 'age' => 28],
];
$redisService->setMultiple($data, 3600);

// 批量获取
$keys = ['user:1', 'user:2', 'user:3'];
$users = $redisService->getMultiple($keys);
```

#### 批量删除

```php
// 批量删除
$keys = ['temp:1', 'temp:2', 'temp:3'];
$deletedCount = $redisService->deleteMultiple($keys);
echo "删除了 {$deletedCount} 个键";
```

### 计数器操作

```php
// 递增计数器
$views = $redisService->increment('page:views');
$redisService->increment('user:points', 10); // 增加10点

// 递减计数器
$stock = $redisService->decrement('product:stock');
$redisService->decrement('user:credits', 5); // 减少5个积分
```

### 模式匹配

```php
// 获取所有用户相关的键
$userKeys = $redisService->keys('user:*');

// 获取所有临时数据键
$tempKeys = $redisService->keys('temp:*');
```

## 高级用法

### 使用特定连接

如果需要使用特定的 Redis 连接，可以通过服务容器获取：

```php
// 使用缓存连接
$cacheRedis = app('redis.service.cache');
$cacheRedis->set('cache:key', 'value');

// 使用默认连接
$defaultRedis = app('redis.service.default');
$defaultRedis->set('default:key', 'value');
```

### 在服务类中使用

```php
<?php

namespace App\Services;

use App\Contracts\RedisServiceInterface;

class UserCacheService
{
    protected RedisServiceInterface $redisService;

    public function __construct(RedisServiceInterface $redisService)
    {
        $this->redisService = $redisService;
    }

    public function cacheUserProfile(int $userId, array $profile): void
    {
        $key = "user:profile:{$userId}";
        $this->redisService->set($key, $profile, 3600); // 缓存1小时
    }

    public function getUserProfile(int $userId): ?array
    {
        $key = "user:profile:{$userId}";
        return $this->redisService->get($key);
    }

    public function clearUserCache(int $userId): void
    {
        $keys = $this->redisService->keys("user:*:{$userId}");
        if (!empty($keys)) {
            $this->redisService->deleteMultiple($keys);
        }
    }
}
```

## 实际应用场景

### 1. 队列任务数据存储

```php
// 存储队列任务数据
$taskData = [
    'type' => 'send_email',
    'recipient' => '<EMAIL>',
    'template' => 'welcome',
    'data' => ['name' => '张三']
];
$redisService->set("queue:task:{$taskId}", $taskData, 3600);

// 获取任务数据
$task = $redisService->get("queue:task:{$taskId}");
```

### 2. 临时数据缓存

```php
// 缓存API响应数据
$apiResponse = [
    'status' => 'success',
    'data' => $expensiveApiCall(),
    'timestamp' => time()
];
$redisService->set('api:response:user_list', $apiResponse, 300); // 缓存5分钟

// 获取缓存的API响应
$cachedResponse = $redisService->get('api:response:user_list');
if ($cachedResponse) {
    return $cachedResponse;
}
```

### 3. 用户会话管理

```php
// 存储用户会话信息
$sessionData = [
    'user_id' => 123,
    'login_time' => time(),
    'permissions' => ['read', 'write'],
    'last_activity' => time()
];
$redisService->set("session:{$sessionId}", $sessionData, 7200); // 2小时过期

// 更新最后活动时间
$session = $redisService->get("session:{$sessionId}");
if ($session) {
    $session['last_activity'] = time();
    $redisService->set("session:{$sessionId}", $session, 7200);
}
```

### 4. 访问计数和限流

```php
// 页面访问计数
$redisService->increment('page:views:home');
$redisService->increment("user:page_views:{$userId}");

// API 限流
$key = "rate_limit:api:{$userId}:" . date('Y-m-d-H-i');
$requests = $redisService->increment($key);
$redisService->expire($key, 60); // 1分钟过期

if ($requests > 100) {
    throw new Exception('API 请求频率超限');
}
```

## 错误处理

Redis 服务内置了完善的错误处理机制：

- 连接失败时会记录错误日志并返回默认值
- 序列化/反序列化错误会被捕获并记录
- 所有操作都有异常保护，确保应用不会因 Redis 问题而崩溃

## 监控和调试

### 连接测试

```php
// 测试 Redis 连接
if ($redisService->ping()) {
    echo "Redis 连接正常";
} else {
    echo "Redis 连接失败";
}

// 获取连接信息
$info = $redisService->getConnectionInfo();
print_r($info);
```

### 性能监控

通过配置文件可以启用性能监控，记录慢查询和操作统计信息。

## 配置说明

Redis 服务的配置位于 `config/redis.php` 文件中，主要配置项包括：

- `default_connection`: 默认连接名称
- `serialization`: 序列化相关配置
- `error_handling`: 错误处理配置
- `performance`: 性能相关配置
- `key_prefix`: 键名前缀配置
- `monitoring`: 监控配置

详细配置说明请参考配置文件中的注释。

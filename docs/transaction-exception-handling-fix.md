# 事务管理器异常处理修复文档

## 概述

本文档记录了对 `TransactionManager` 异常处理逻辑的重要修复，解决了业务异常被错误包装导致 HTTP 状态码不正确的问题。

## 问题背景

### 问题描述

在线索更新接口中，当线索不存在时：
- **期望行为**: 返回 404 状态码，错误消息 "线索不存在"
- **实际行为**: 返回 500 状态码，错误消息 "事务回滚失败：线索不存在"

### 技术原因

`TransactionManager::executeInTransaction()` 方法的异常处理逻辑存在缺陷：

```php
// 原始代码（有问题）
catch (\Exception $e) {
    // ... 其他逻辑 ...
    
    if ($e instanceof TransactionException) {
        throw $e;
    }
    
    // 问题：所有非 TransactionException 都被包装
    throw TransactionException::rollbackError(
        $e->getMessage(),
        ['original_exception' => get_class($e), 'attempts' => $attempt]
    );
}
```

这导致 `BusinessException`（404）被包装成 `TransactionException`（500），破坏了异常传播链。

## 解决方案

### 修复代码

**文件**: `app/Services/Database/TransactionManager.php`

**1. 添加必要的导入**
```php
use App\Exceptions\BusinessException;
```

**2. 修改异常处理逻辑**
```php
// 修复后的代码
catch (\Exception $e) {
    // ... 其他逻辑 ...
    
    if ($e instanceof TransactionException) {
        throw $e;
    }
    
    // 新增：业务异常直接传播，不包装
    if ($e instanceof BusinessException) {
        throw $e;
    }
    
    // 只有系统异常才包装成事务异常
    throw TransactionException::rollbackError(
        $e->getMessage(),
        ['original_exception' => get_class($e), 'attempts' => $attempt]
    );
}
```

### 设计原理

**异常分类处理策略**:

| 异常类型 | 处理方式 | 原因 |
|---------|---------|------|
| `BusinessException` | 直接传播 | 包含正确的 HTTP 状态码，应该到达 Handler |
| `TransactionException` | 直接传播 | 已经是事务相关异常 |
| 其他异常 | 包装为 `TransactionException` | 系统异常，需要统一处理 |

## 技术细节

### 异常传播链

**修复前（错误）**:
```
Service Layer: BusinessException(404)
    ↓ (被包装)
TransactionManager: TransactionException(500)
    ↓
Handler: 兜底处理器 (500)
    ↓
Response: {"code": 500, "message": "事务回滚失败：线索不存在"}
```

**修复后（正确）**:
```
Service Layer: BusinessException(404)
    ↓ (直接传播)
TransactionManager: BusinessException(404)
    ↓ (直接传播)
Handler: BusinessException 处理器 (404)
    ↓
Response: {"code": 404, "message": "线索不存在"}
```

### 配置文件支持

错误配置 `config/errors.php`:
```php
'Lead' => [
    'not_found' => [
        'message' => '线索不存在',
        'code' => 404,  // 正确的状态码
    ],
],
```

### Handler 处理逻辑

`app/Exceptions/Handler.php` 中的 BusinessException 处理器：
```php
$this->renderable(function (BusinessException $e, Request $request) {
    if ($request->wantsJson() || $request->is('api/*')) {
        return ApiResponse::systemError(
            $e->getMessage(),
            $e->getCode(),  // 使用异常中的状态码
            [],
            $request->header('X-Request-ID')
        );
    }
});
```

## 验证测试

### 测试用例

```php
// 测试代码
try {
    $service = app(\App\Services\LeadService::class);
    $dto = new \App\DTOs\Lead\LeadUpdateDTO(['company_full_name' => '测试公司']);
    $result = $service->updateLead(1111, $dto);
} catch (\App\Exceptions\BusinessException $e) {
    echo 'BusinessException: ' . $e->getMessage() . ' (Code: ' . $e->getCode() . ')';
} catch (\App\Exceptions\TransactionException $e) {
    echo 'TransactionException: ' . $e->getMessage() . ' (Code: ' . $e->getCode() . ')';
}
```

### 测试结果

**修复前**:
```
TransactionException: 事务回滚失败：线索不存在 (Code: 1006)
```

**修复后**:
```
BusinessException: 线索不存在 (Code: 404)
```

## 影响分析

### 正面影响

1. **API 响应准确性**: 返回正确的 HTTP 状态码
2. **前端错误处理**: 前端可以根据状态码进行不同的错误处理
3. **日志分析**: 业务异常和系统异常分离，便于监控分析
4. **用户体验**: 错误消息更加友好和准确

### 兼容性

- ✅ 不影响现有的 TransactionException 处理逻辑
- ✅ 不影响系统异常的包装和处理
- ✅ 不影响死锁重试机制
- ✅ 向后兼容，不需要修改调用方代码

## 最佳实践

### 异常设计原则

1. **业务异常不包装**: 业务异常包含了重要的业务语义，不应该被技术层包装
2. **异常传播透明**: 每一层都应该明确异常的传播规则
3. **状态码准确性**: HTTP 状态码应该准确反映业务状态

### 代码规范

```php
// ✅ 正确：业务异常直接抛出
if (!$entity) {
    throw BusinessException::fromErrorCode('Entity.not_found');
}

// ❌ 错误：包装业务异常
if (!$entity) {
    throw new SystemException('Entity not found');
}
```

## 总结

这次修复解决了一个重要的架构问题，确保了业务异常能够正确传播到 API 响应层，提高了系统的可用性和可维护性。修复遵循了异常处理的最佳实践，保持了代码的清晰性和一致性。

# Laravel Telescope 数据库查询监控配置文档

## 概述

本文档记录了在 CRM API 项目中配置 Laravel Telescope 进行数据库查询监控的详细过程，包括安装、配置和使用方法。

## 实施内容

### 1. 安装 Laravel Telescope

通过 Composer 安装 Telescope 包（仅在开发环境）：

```bash
composer require laravel/telescope --dev
```

### 2. 发布配置和资源

运行 Telescope 安装命令：

```bash
php artisan telescope:install
php artisan migrate
```

这会创建以下文件：
- `config/telescope.php` - Telescope 配置文件
- `app/Providers/TelescopeServiceProvider.php` - 服务提供者
- 数据库迁移文件和相关数据表

### 3. 配置优化

#### 环境变量配置

在 `.env` 文件中添加：

```env
# Laravel Telescope Configuration
TELESCOPE_ENABLED=true
TELESCOPE_SLOW_QUERY_THRESHOLD=50
```

#### 查询监控配置

在 `config/telescope.php` 中优化查询监控器：

```php
Watchers\QueryWatcher::class => [
    'enabled' => env('TELESCOPE_QUERY_WATCHER', true),
    'ignore_packages' => true,
    'ignore_paths' => [],
    'slow' => env('TELESCOPE_SLOW_QUERY_THRESHOLD', 50), // 慢查询阈值（毫秒）
],
```

#### 服务提供者增强

在 `TelescopeServiceProvider` 中添加了查询标签功能：

```php
// 在开发环境中启用更详细的监控
if ($isLocal) {
    Telescope::tag(function (IncomingEntry $entry) {
        $tags = [];
        
        // 为查询添加标签
        if ($entry->type === 'query') {
            if ($entry->content['slow']) {
                $tags[] = 'slow-query';
            }
            if ($entry->content['time'] > 100) {
                $tags[] = 'very-slow-query';
            }
        }
        
        return $tags;
    });
}
```

### 4. 测试路由

添加了测试路由来验证 Telescope 功能：

```php
// Telescope 查询监控测试路由
Route::prefix('test/telescope')->group(function () {
    Route::get('/queries', function () {
        // 模拟多个查询和 N+1 查询问题
    });
    
    Route::get('/performance', function () {
        // 模拟性能测试
    });
});
```

## 使用方法

### 1. 访问 Telescope 面板

启动开发服务器后，访问：
```
http://127.0.0.1:8001/telescope
```

### 2. 查看数据库查询

在 Telescope 面板中：

1. **Queries 选项卡**：查看所有数据库查询
   - 查询 SQL 语句
   - 执行时间
   - 绑定参数
   - 调用堆栈

2. **慢查询识别**：
   - 超过阈值的查询会被标记为慢查询
   - 可以通过标签筛选 `slow-query` 和 `very-slow-query`

3. **N+1 查询检测**：
   - 识别重复的查询模式
   - 显示查询频率和总耗时

### 3. 性能分析功能

- **查询统计**：每个请求的查询数量和总耗时
- **内存使用**：请求的内存消耗情况
- **响应时间**：完整的请求响应时间分析

### 4. 使用 Makefile 命令

```bash
# 检查 Telescope 状态
make telescope-status

# 清除监控数据
make telescope-clear

# 清理过期数据
make telescope-prune

# 运行测试
make telescope-test
```

## 测试验证

### 1. 基础功能测试

访问测试端点：
```bash
curl http://127.0.0.1:8001/api/test/telescope/queries
curl http://127.0.0.1:8001/api/test/telescope/performance
```

### 2. 查看监控数据

1. 执行测试请求后，访问 Telescope 面板
2. 在 "Queries" 选项卡中查看：
   - 查询数量和执行时间
   - 慢查询标记
   - N+1 查询问题

### 3. 性能分析

在 "Requests" 选项卡中查看：
- 请求总耗时
- 数据库查询占比
- 内存使用情况

## 最佳实践

### 1. 开发环境使用

- 仅在开发环境启用 Telescope
- 定期清理监控数据避免数据库膨胀
- 关注慢查询和 N+1 查询问题

### 2. 查询优化指导

- 使用 Eager Loading 解决 N+1 查询
- 为慢查询添加索引
- 优化复杂查询的 SQL 语句

### 3. 数据管理

- 设置合理的数据保留策略
- 定期运行 `telescope:prune` 清理过期数据
- 监控 Telescope 数据表大小

## 注意事项

1. **性能影响**：Telescope 会对应用性能产生轻微影响，仅在开发环境使用
2. **数据安全**：确保生产环境不启用 Telescope 或正确配置访问权限
3. **存储空间**：监控数据会占用数据库空间，需要定期清理

## 故障排除

### 常见问题

1. **无法访问 Telescope 面板**
   - 检查 `TELESCOPE_ENABLED` 环境变量
   - 确认数据库迁移已执行

2. **查询监控不工作**
   - 检查 `TELESCOPE_QUERY_WATCHER` 配置
   - 确认服务提供者已注册

3. **数据库空间不足**
   - 运行 `make telescope-clear` 清理数据
   - 设置自动清理任务

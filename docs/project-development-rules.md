# Laravel CRM API 项目开发规则

## 概述

本文档是基于当前 Laravel CRM API 项目的架构特点、技术栈和编码规范生成的完整开发规则集，用于指导后续功能开发，确保代码质量和架构一致性。

## 规则体系结构

### 1. 规则文件组织

```
.augment/rules/
├── technical-standards.md    # 技术规范规则
├── architecture-design.md    # 架构设计规则
├── code-quality.md          # 代码质量规则
├── development-workflow.md   # 开发流程规则
├── coding.md                # 基础编码规范
└── philosophy.md            # 开发哲学
```

### 2. 规则覆盖范围

#### 2.1 技术规范规则
- **技术栈要求**: PHP 8.2+, Laravel 10.x, MySQL 5.7.28+, Redis 7.2.4+
- **开发工具**: PHPStan Level 8, Laravel Pint, PHPUnit ^10.1
- **依赖管理**: Composer 包管理和版本约束规则
- **环境配置**: 开发和生产环境配置要求
- **性能要求**: 内存限制、查询优化、批量操作
- **安全要求**: 认证授权、数据验证、防护措施
- **监控日志**: 应用监控、结构化日志、错误追踪
- **API 设计**: RESTful 设计、统一响应格式
- **版本控制**: Git 规范、代码审查流程
- **部署要求**: 生产环境优化和健康检查

#### 2.2 架构设计规则
- **分层架构**: Controller → Request → DTO → Service → Repository → Model → Resource
- **SOLID 原则**: 单一职责、开闭、里氏替换、接口隔离、依赖倒置
- **Repository 模式**: 接口定义、实现规范、依赖注入
- **Service 层规范**: 业务逻辑封装、事务管理、异常处理
- **DTO 模式规范**: 不可变性、类型安全、业务逻辑封装
- **异常处理规范**: 业务异常、系统异常、统一处理
- **架构验证规则**: 依赖方向检查、接口使用、职责边界

#### 2.3 代码质量规则
- **命名约定**: 类、方法、变量、常量的命名规范
- **类型注解**: PHPDoc 注释要求、PHP 类型声明
- **异常处理**: 业务异常定义、处理策略
- **测试要求**: 覆盖率要求、测试类型、命名规范
- **代码格式**: PSR-12 标准、额外规范、注释规范

#### 2.4 开发流程规则
- **任务管理**: 任务文档创建、分支策略、提交规范
- **代码质量检查**: 静态分析、代码格式、测试验证
- **文档管理**: 文档位置、命名约定、内容要求
- **性能安全**: 查询优化、缓存策略、安全检查
- **环境管理**: 开发环境配置、生产环境准备
- **错误处理**: 日志记录、异常处理、监控告警
- **持续改进**: 代码审查、技术债务管理

## 核心设计理念

### 1. 渐进式开发
- **先实现，再优化**: 优先实现可用功能，再进行架构优化
- **每步可部署**: 每个提交都是可运行、可测试、可部署的
- **逐步演进**: 根据业务需求逐步演进代码结构

### 2. 类型安全优先
- **强类型约束**: 使用 PHP 8.2+ 类型系统和 PHPStan Level 8
- **DTO 模式**: 用类型安全的对象替代数组传递
- **编译时检查**: 通过静态分析在编译时发现问题

### 3. 架构一致性
- **分层架构**: 严格的层次划分和职责边界
- **SOLID 原则**: 面向对象设计原则的具体应用
- **接口抽象**: 依赖接口而非具体实现

### 4. 代码质量
- **命名规范**: 清晰、描述性的命名约定
- **文档完整**: 完整的 PHPDoc 注释和技术文档
- **测试覆盖**: 核心业务逻辑 >= 80% 覆盖率

## 实施指南

### 1. 新功能开发流程

#### 1.1 开发前准备
1. 创建任务文档在 `.cursor/tasks/` 目录
2. 分析业务需求和技术方案
3. 设计 DTO 和接口定义

#### 1.2 开发实施
1. 创建或更新 Model（不创建 Migration）
2. 定义 Repository 接口和实现
3. 创建 DTO 类处理数据传输
4. 创建 Service 类处理业务逻辑
5. 创建 Request 类验证输入
6. 创建 Controller 处理 HTTP 请求
7. 创建 Resource 格式化响应
8. 定义路由

#### 1.3 质量检查
1. 运行 PHPStan Level 8 静态分析：`make analyze`
2. 运行 Laravel Pint 代码格式检查：`make lint`
3. 运行相关测试：`make test`
4. 更新相关文档

#### 1.4 提交和文档
1. 使用清晰的中文提交信息
2. 创建技术文档在 `docs/` 目录
3. 更新 README.md（如需要）

### 2. 代码审查要点

#### 2.1 架构合规性
- [ ] 是否遵循分层架构
- [ ] 是否正确使用 Repository 接口
- [ ] 是否使用 DTO 模式传递数据
- [ ] 是否遵循 SOLID 原则

#### 2.2 代码质量
- [ ] 命名是否符合约定
- [ ] 类型注解是否完整
- [ ] 异常处理是否合理
- [ ] 测试覆盖是否充分

#### 2.3 性能和安全
- [ ] 是否存在 N+1 查询问题
- [ ] 是否正确使用缓存
- [ ] 输入验证是否完整
- [ ] 敏感信息是否安全处理

### 3. 工具和命令

#### 3.1 开发工具
```bash
# 代码质量检查
make analyze          # PHPStan 静态分析
make lint            # Laravel Pint 格式检查
make test            # 运行测试

# 开发环境
make install         # 安装依赖
make serve          # 启动开发服务器
make tinker         # 启动 Tinker REPL

# 缓存管理
make cache-clear    # 清除缓存
make optimize       # 优化性能
```

#### 3.2 提交前检查
```bash
# 完整检查流程
make analyze && make lint && make test
```

## 规则执行和监督

### 1. 自动化检查
- **CI/CD 集成**: 在持续集成中自动执行规则检查
- **Git Hooks**: 配置提交前自动检查
- **IDE 集成**: 实时显示规则违反情况

### 2. 人工审查
- **代码审查**: 每个 Pull Request 必须经过代码审查
- **架构审查**: 定期审查整体架构设计
- **规则更新**: 根据项目发展更新规则内容

### 3. 持续改进
- **问题收集**: 收集规则执行中的问题和建议
- **最佳实践**: 总结和分享开发最佳实践
- **团队培训**: 定期组织规则培训和技术分享

## 总结

这套开发规则基于项目的实际架构和技术栈制定，具有以下特点：

1. **全面性**: 覆盖技术规范、架构设计、代码质量、开发流程等各个方面
2. **实用性**: 每个规则都有具体的操作指导和验证方法
3. **一致性**: 与项目现有的技术栈和架构模式完全匹配
4. **可执行性**: 提供具体的工具和命令支持规则执行
5. **可扩展性**: 可以根据项目发展需要进行调整和扩展

通过严格执行这套规则，可以确保项目代码质量的一致性，提高开发效率，降低维护成本，为项目的长期发展奠定坚实的基础。

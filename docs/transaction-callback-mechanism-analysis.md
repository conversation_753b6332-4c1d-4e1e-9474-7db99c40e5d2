# TransactionManager 事务回调机制详细分析

## 1. 回调函数注册机制

### 1.1 回调函数定义

在 `TransactionManager` 中定义了三个事务回调函数数组：

```php
protected array $callbacks = [
    'before_begin' => [],    // 事务开始前回调
    'after_commit' => [],    // 事务提交后回调
    'after_rollback' => [],  // 事务回滚后回调
];
```

### 1.2 注册方法实现

```php
public function registerCallback(string $event, callable $callback): bool
{
    if (!isset($this->callbacks[$event])) {
        return false;  // 无效的事件类型
    }

    $this->callbacks[$event][] = $callback;  // 添加到对应事件的回调数组
    return true;
}
```

### 1.3 当前注册情况

**搜索结果显示**：
- ✅ 在测试文件中有使用示例 (`tests/Unit/Services/Database/TransactionManagerTest.php`)
- ✅ 在示例文件中有完整的使用说明 (`examples/`)
- ❌ **在实际业务代码中未发现使用**

**原因分析**：
1. 功能相对较新，业务代码尚未采用
2. 开发团队可能不了解此功能
3. 当前业务场景相对简单，暂未需要复杂的事务回调

## 2. 回调函数触发时机

### 2.1 触发机制

通过 `triggerCallbacks()` 方法触发回调：

```php
protected function triggerCallbacks(string $event): void
{
    if (isset($this->callbacks[$event])) {
        foreach ($this->callbacks[$event] as $callback) {
            try {
                $callback();
            } catch (Exception $e) {
                Log::warning('事务回调执行失败', [
                    'event' => $event,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
```

### 2.2 具体触发时机

#### 2.2.1 before_begin 回调

**触发位置**：`executeInTransaction()` 方法开始时
```php
// 触发开始前回调
$this->triggerCallbacks('before_begin');

// 设置事务超时
$this->setTimeout($timeout);

$result = DB::transaction(function () use ($callback, $timeout, $startTime) {
    // 事务逻辑
});
```

**作用**：
- 在事务开始前执行准备工作
- 可用于资源预分配、状态检查等
- 如果回调失败，不会影响事务执行（仅记录警告日志）

#### 2.2.2 after_commit 回调

**触发位置**：事务成功提交后
```php
$result = DB::transaction(function () use ($callback, $timeout, $startTime) {
    return $callback();
});

// 触发提交后回调
$this->triggerCallbacks('after_commit');
```

**作用**：
- 在事务成功提交后执行后续处理
- 适用于缓存清理、事件通知、审计日志等
- 回调失败不会影响已提交的事务

#### 2.2.3 after_rollback 回调

**触发位置**：事务发生异常回滚时
```php
} catch (Exception $e) {
    $lastException = $e;
    // 触发回滚后回调
    $this->triggerCallbacks('after_rollback');
    
    // 异常处理逻辑
}
```

**作用**：
- 在事务回滚后执行清理或补偿操作
- 适用于错误通知、数据恢复、告警等
- 回调失败不会影响异常的传播

## 3. 实际使用情况

### 3.1 测试代码中的使用

在 `TransactionManagerTest.php` 中有基本的回调测试：

```php
public function test_register_callback(): void
{
    $callbackExecuted = false;

    $callback = function () use (&$callbackExecuted) {
        $callbackExecuted = true;
    };

    $result = $this->transactionManager->registerCallback('after_commit', $callback);
    $this->assertTrue($result);

    // 执行事务以触发回调
    $this->transactionManager->executeInTransaction(function () {
        return 'test';
    });

    $this->assertTrue($callbackExecuted);
}
```

### 3.2 示例代码中的使用

在 `examples/` 目录中有完整的使用示例，但这些都是**示例代码**，不是实际业务代码。

### 3.3 业务代码中的使用情况

**当前状态**：在实际的业务 Service 层代码中未发现使用事务回调的地方。

**原因分析**：
1. 功能相对较新，业务代码尚未采用
2. 开发团队可能不了解此功能
3. 当前业务场景相对简单，暂未需要复杂的事务回调

## 4. 扩展建议

基于线索管理的业务场景，以下是事务回调的实际应用建议：

### 4.1 缓存管理

**场景**：线索创建/更新后清理相关缓存

```php
// 在 LeadService 构造函数中注册回调
public function __construct(
    private LeadRepositoryInterface $leadRepository,
    private TransactionManagerInterface $transactionManager,
    private CacheManager $cacheManager
) {
    // 注册事务提交后的缓存清理回调
    $this->transactionManager->registerCallback('after_commit', function () {
        $this->cacheManager->forget([
            'leads:list:*',
            'leads:stats:*',
            'leads:regions:*'
        ]);
    });
}
```

### 4.2 审计日志

**场景**：记录所有线索操作的审计日志

```php
public function __construct(
    private LeadRepositoryInterface $leadRepository,
    private TransactionManagerInterface $transactionManager,
    private AuditLogService $auditLogService
) {
    // 注册审计日志回调
    $this->transactionManager->registerCallback('after_commit', function () {
        $this->auditLogService->recordLeadOperation([
            'action' => 'create',
            'entity_type' => 'Lead',
            'operator_id' => auth()->id(),
            'timestamp' => now(),
        ]);
    });
}
```

### 4.3 事件通知

**场景**：线索状态变更时发送通知

```php
public function updateLeadStatus(int $leadId, int $newStatus): Lead
{
    $lead = $this->leadRepository->findById($leadId);
    $oldStatus = $lead->status;

    // 注册通知回调
    $this->transactionManager->registerCallback('after_commit', function () use ($lead, $oldStatus, $newStatus) {
        // 发送内部通知
        $this->notificationService->sendLeadStatusChangeNotification([
            'lead_id' => $lead->id,
            'company_name' => $lead->company_full_name,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'operator_id' => auth()->id(),
            'changed_at' => now(),
        ]);
    });

    return $this->transactionManager->executeInTransaction(function () use ($leadId, $newStatus) {
        return $this->leadRepository->updateStatus($leadId, $newStatus);
    });
}
```

### 4.4 性能监控

**场景**：监控事务执行性能和成功率

```php
public function __construct(
    private LeadRepositoryInterface $leadRepository,
    private TransactionManagerInterface $transactionManager,
    private MetricsCollector $metricsCollector
) {
    // 注册性能监控回调
    $this->transactionManager->registerCallback('before_begin', function () {
        $this->metricsCollector->startTimer('transaction.lead.duration');
    });

    $this->transactionManager->registerCallback('after_commit', function () {
        $this->metricsCollector->endTimer('transaction.lead.duration');
        $this->metricsCollector->increment('transaction.lead.success');
    });

    $this->transactionManager->registerCallback('after_rollback', function () {
        $this->metricsCollector->endTimer('transaction.lead.duration');
        $this->metricsCollector->increment('transaction.lead.failure');
    });
}
```

## 5. 实施建议

### 5.1 创建回调管理器

建议创建专门的回调管理器来统一管理事务回调：

```php
<?php

namespace App\Services\Transaction;

use App\Contracts\TransactionManagerInterface;

/**
 * 事务回调管理服务
 */
class TransactionCallbackManager
{
    public function __construct(
        private TransactionManagerInterface $transactionManager
    ) {}

    /**
     * 注册线索相关的事务回调
     */
    public function registerLeadCallbacks(): void
    {
        // 缓存清理回调
        $this->transactionManager->registerCallback('after_commit', 
            $this->createCacheCleanupCallback()
        );

        // 审计日志回调
        $this->transactionManager->registerCallback('after_commit', 
            $this->createAuditLogCallback()
        );

        // 错误通知回调
        $this->transactionManager->registerCallback('after_rollback', 
            $this->createErrorNotificationCallback()
        );
    }

    private function createCacheCleanupCallback(): callable
    {
        return function () {
            // 缓存清理逻辑
        };
    }

    private function createAuditLogCallback(): callable
    {
        return function () {
            // 审计日志逻辑
        };
    }

    private function createErrorNotificationCallback(): callable
    {
        return function () {
            // 错误通知逻辑
        };
    }
}
```

### 5.2 在 ServiceProvider 中注册

```php
<?php

namespace App\Providers;

use App\Services\Transaction\TransactionCallbackManager;
use Illuminate\Support\ServiceProvider;

class TransactionServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $callbackManager = $this->app->make(TransactionCallbackManager::class);
        $callbackManager->registerLeadCallbacks();
    }
}
```

## 6. 总结

### 6.1 当前状态

- ✅ **机制完善**：TransactionManager 提供了完整的回调机制
- ✅ **接口清晰**：registerCallback/removeCallback 方法设计合理
- ✅ **异常安全**：回调执行失败不会影响事务本身
- ❌ **使用不足**：实际业务代码中未使用此功能

### 6.2 建议行动

1. **立即实施**：在 LeadService 中添加基本的缓存清理回调
2. **逐步扩展**：根据业务需要添加审计日志、通知等回调
3. **统一管理**：创建 TransactionCallbackManager 统一管理回调
4. **文档完善**：为开发团队提供使用指南和最佳实践

### 6.3 优先级建议

**高优先级**：
- 缓存清理回调（提升性能）
- 错误通知回调（提升运维效率）

**中优先级**：
- 审计日志回调（合规要求）
- 性能监控回调（运维监控）

**低优先级**：
- 外部系统同步回调（业务扩展）
- 复杂的补偿机制（高级功能）

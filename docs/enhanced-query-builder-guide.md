# EnhancedQueryBuilder 复杂查询实例指南

## 概述

EnhancedQueryBuilder 是一个增强的查询构建器，提供了比 Laravel 原生查询构建器更强大的功能，包括复杂条件构建、动态排序、查询优化、性能监控和智能缓存等功能。

## 基础查询功能说明

### 1. 核心功能和设计理念

EnhancedQueryBuilder 基于以下设计理念：

- **性能优先**: 内置查询优化和缓存机制
- **灵活扩展**: 支持复杂的动态查询条件构建
- **智能分析**: 提供查询执行计划分析和优化建议
- **多层缓存**: 实现内存缓存 + Redis 缓存的多层缓存策略
- **业务友好**: 专为复杂业务查询场景设计

### 2. 与 Laravel 原生查询构建器的区别和优势

#### 2.1 功能对比

| 功能 | Laravel 原生 | EnhancedQueryBuilder |
|------|-------------|---------------------|
| 基础查询 | ✅ | ✅ |
| 复杂条件构建 | 手动编写 | ✅ 自动化构建 |
| 动态排序 | 手动处理 | ✅ 智能排序规则 |
| 查询优化 | 无 | ✅ 自动优化建议 |
| 性能监控 | 无 | ✅ 详细性能分析 |
| 智能缓存 | 手动实现 | ✅ 多层自动缓存 |
| 执行计划分析 | 手动 EXPLAIN | ✅ 自动分析和建议 |

#### 2.2 核心优势

```php
// Laravel 原生方式：手动构建复杂查询
$query = Lead::query()
    ->where('status', 1)
    ->where(function ($q) use ($keyword) {
        $q->where('company_full_name', 'like', "%{$keyword}%")
          ->orWhere('company_short_name', 'like', "%{$keyword}%");
    })
    ->whereHas('contacts', function ($q) use ($mobile) {
        $q->where('mobile', $mobile);
    })
    ->orderBy('created_at', 'desc');

// EnhancedQueryBuilder 方式：配置化构建
$conditions = [
    'status' => 1,
    'search' => [
        'keyword' => $keyword,
        'fields' => ['company_full_name', 'company_short_name'],
        'match_type' => 'like'
    ],
    'relations' => [
        'contacts' => ['mobile' => $mobile]
    ]
];

$sortRules = ['created_at:desc'];
$query = $this->queryBuilder->buildComplexQuery($conditions, Lead::query());
$query = $this->queryBuilder->addDynamicSorting($query, $sortRules);
```

### 3. 依赖注入配置和服务绑定

#### 3.1 服务绑定配置

在 `DatabaseServiceProvider` 中的绑定：

```php
// 单例绑定
$this->app->singleton(QueryBuilderInterface::class, function ($app) {
    return new EnhancedQueryBuilder;
});

// 别名注册
$this->app->alias(QueryBuilderInterface::class, EnhancedQueryBuilder::class);
$this->app->alias(QueryBuilderInterface::class, 'query.builder');
```

#### 3.2 在 Repository 中使用

```php
class LeadRepository implements LeadRepositoryInterface
{
    public function __construct(
        Lead $model,
        QueryBuilderInterface $queryBuilder,
        TransactionManagerInterface $transactionManager
    ) {
        $this->model = $model;
        $this->queryBuilder = $queryBuilder;
        $this->transactionManager = $transactionManager;
    }
}
```

### 4. 基本查询方法使用示例

#### 4.1 简单条件查询

```php
public function findActiveLeads(): Collection
{
    $conditions = [
        'status' => Lead::STATUS_ACTIVE,
        'deleted_at' => ['operator' => 'null']
    ];
    
    $query = $this->queryBuilder->buildComplexQuery($conditions, $this->model->newQuery());
    return $query->get();
}
```

#### 4.2 范围查询

```php
public function findLeadsByDateRange(string $startDate, string $endDate): Collection
{
    $conditions = [
        'created_at_range' => [
            'start' => $startDate,
            'end' => $endDate,
            'type' => 'date_range'
        ]
    ];
    
    $query = $this->queryBuilder->buildComplexQuery($conditions, $this->model->newQuery());
    return $query->get();
}
```

#### 4.3 搜索查询

```php
public function searchLeads(string $keyword): Collection
{
    $conditions = [
        'search' => [
            'keyword' => $keyword,
            'fields' => ['company_full_name', 'company_short_name', 'internal_name'],
            'match_type' => 'like'
        ]
    ];
    
    $query = $this->queryBuilder->buildComplexQuery($conditions, $this->model->newQuery());
    return $query->get();
}
```

## 复杂查询场景实例

### 1. 多表关联查询的构建和优化

#### 1.1 线索与联系人关联查询

```php
public function findLeadsWithContactInfo(array $filters): Collection
{
    $conditions = [
        // 基础线索条件
        'status' => Lead::STATUS_ACTIVE,
        'region' => $filters['region'] ?? null,
        
        // 关联联系人条件
        'relations' => [
            'contacts' => [
                'mobile' => ['operator' => 'not null'],
                'department' => $filters['department'] ?? null
            ]
        ]
    ];
    
    // 构建查询
    $query = $this->model->newQuery()
        ->with(['contacts' => function ($q) {
            $q->select('id', 'name', 'mobile', 'department', 'position');
        }]);
    
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
    
    // 优化查询
    $optimizationOptions = [
        'optimize_select' => true,
        'optimize_joins' => true,
        'use_index' => true,
        'indexes' => ['idx_status', 'idx_region']
    ];
    
    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);
    
    return $query->get();
}
```

#### 1.2 三表关联的复杂查询

```php
public function findLeadsWithUserAndContactInfo(array $filters): Collection
{
    $conditions = [
        'status' => ['operator' => 'in', 'value' => [1, 2, 3]],
        
        // 用户关联条件
        'relations' => [
            'users' => [
                'role_type' => Lead::ROLE_TYPE_OWNER,
                'is_primary' => 1
            ],
            'contacts' => [
                'mobile' => ['operator' => 'not null']
            ]
        ],
        
        // 搜索条件
        'search' => [
            'keyword' => $filters['keyword'] ?? '',
            'fields' => ['company_full_name', 'company_short_name'],
            'match_type' => 'like'
        ]
    ];
    
    $query = $this->model->newQuery()
        ->with([
            'users' => function ($q) {
                $q->where('role_type', Lead::ROLE_TYPE_OWNER)
                  ->select('id', 'name', 'email');
            },
            'contacts:id,name,mobile,position'
        ]);
    
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
    
    return $query->get();
}
```

### 2. 动态条件查询的实现方法

#### 2.1 灵活的筛选条件

```php
public function buildDynamicLeadQuery(array $filters): Builder
{
    $conditions = [];
    
    // 动态构建基础条件
    foreach (['status', 'region', 'source', 'industry', 'stage'] as $field) {
        if (isset($filters[$field]) && $filters[$field] !== null) {
            if (is_array($filters[$field])) {
                $conditions[$field] = ['operator' => 'in', 'value' => $filters[$field]];
            } else {
                $conditions[$field] = $filters[$field];
            }
        }
    }
    
    // 动态日期范围
    if (isset($filters['date_range'])) {
        $dateField = $filters['date_field'] ?? 'created_at';
        $conditions[$dateField . '_range'] = [
            'start' => $filters['date_range']['start'],
            'end' => $filters['date_range']['end'],
            'type' => 'date_range'
        ];
    }
    
    // 动态搜索条件
    if (!empty($filters['keyword'])) {
        $searchFields = $filters['search_fields'] ?? [
            'company_full_name', 'company_short_name', 'internal_name'
        ];
        
        $conditions['search'] = [
            'keyword' => $filters['keyword'],
            'fields' => $searchFields,
            'match_type' => $filters['match_type'] ?? 'like'
        ];
    }
    
    // 动态关联条件
    if (isset($filters['contact_mobile'])) {
        $conditions['relations']['contacts'] = [
            'mobile' => $filters['contact_mobile']
        ];
    }
    
    if (isset($filters['assigned_user'])) {
        $conditions['relations']['users'] = [
            'user_id' => $filters['assigned_user'],
            'role_type' => Lead::ROLE_TYPE_OWNER
        ];
    }
    
    // 构建查询
    $query = $this->model->newQuery();
    return $this->queryBuilder->buildComplexQuery($conditions, $query);
}
```

#### 2.2 条件组合查询

```php
public function findLeadsWithComplexConditions(array $filters): Collection
{
    $conditions = [
        // OR 条件组合
        'status' => ['operator' => 'in', 'value' => [1, 2]],

        // 范围条件
        'created_at_range' => [
            'start' => $filters['start_date'],
            'end' => $filters['end_date'],
            'type' => 'date_range'
        ],

        // 多字段搜索
        'search' => [
            'keyword' => $filters['keyword'],
            'fields' => ['company_full_name', 'company_short_name', 'address'],
            'match_type' => 'like'
        ],

        // 嵌套关联条件
        'relations' => [
            'contacts' => [
                'department' => ['operator' => 'in', 'value' => ['销售部', '市场部']],
                'position' => ['operator' => 'like', 'value' => '%经理%']
            ],
            'users' => [
                'role_type' => Lead::ROLE_TYPE_OWNER,
                'is_primary' => 1
            ]
        ]
    ];

    $query = $this->model->newQuery()
        ->with(['contacts:id,name,department,position', 'users:id,name']);

    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    return $query->get();
}
```

### 3. 子查询和联合查询的使用技巧

#### 3.1 子查询实现

```php
public function findLeadsWithSubquery(): Collection
{
    // 使用原生 Laravel 查询构建器构建子查询
    $subQuery = DB::table('crm_lead_contact_relation')
        ->select('lead_id')
        ->join('crm_contact', 'crm_contact.id', '=', 'crm_lead_contact_relation.contact_id')
        ->where('crm_contact.department', '销售部')
        ->groupBy('lead_id')
        ->havingRaw('COUNT(*) > 2'); // 有超过2个销售部联系人的线索

    $conditions = [
        'status' => Lead::STATUS_ACTIVE
    ];

    $query = $this->model->newQuery()
        ->whereIn('id', $subQuery);

    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    return $query->get();
}
```

#### 3.2 联合查询实现

```php
public function findLeadsUnionQuery(array $filters): Collection
{
    // 第一个查询：活跃线索
    $activeQuery = $this->model->newQuery()
        ->where('status', Lead::STATUS_ACTIVE)
        ->where('region', $filters['region'] ?? 1);

    // 第二个查询：最近更新的线索
    $recentQuery = $this->model->newQuery()
        ->where('updated_at', '>=', now()->subDays(7))
        ->where('region', $filters['region'] ?? 1);

    // 使用 UNION 合并查询
    $unionQuery = $activeQuery->union($recentQuery);

    // 对联合查询结果应用额外条件
    $conditions = [
        'search' => [
            'keyword' => $filters['keyword'] ?? '',
            'fields' => ['company_full_name', 'company_short_name'],
            'match_type' => 'like'
        ]
    ];

    // 注意：UNION 查询需要特殊处理
    if (!empty($conditions['search']['keyword'])) {
        $keyword = $conditions['search']['keyword'];
        $unionQuery->where(function ($q) use ($keyword) {
            $q->where('company_full_name', 'like', "%{$keyword}%")
              ->orWhere('company_short_name', 'like', "%{$keyword}%");
        });
    }

    return $unionQuery->distinct()->get();
}
```

### 4. 聚合查询和分组统计的最佳实践

#### 4.1 基础聚合查询

```php
public function getLeadStatistics(array $filters = []): array
{
    $query = $this->model->newQuery();

    // 应用筛选条件
    if (!empty($filters)) {
        $conditions = $this->buildQueryConditions($filters);
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
    }

    // 构建聚合查询
    $aggregations = [
        'count' => ['field' => '*'],
        'avg' => ['field' => 'stage'],
        'max' => ['field' => 'created_at'],
        'min' => ['field' => 'created_at']
    ];

    return $this->queryBuilder->buildAggregateQuery($query, $aggregations);
}
```

#### 4.2 分组统计查询

```php
public function getLeadStatsByRegion(): array
{
    $query = $this->model->newQuery()
        ->where('status', Lead::STATUS_ACTIVE);

    $aggregations = [
        'group_count' => ['field' => 'region']
    ];

    $results = $this->queryBuilder->buildAggregateQuery($query, $aggregations);

    // 格式化结果
    $formattedResults = [];
    foreach ($results['group_count'] as $item) {
        $formattedResults[] = [
            'region' => $item['region'],
            'count' => $item['count'],
            'region_name' => $this->getRegionName($item['region'])
        ];
    }

    return $formattedResults;
}
```

#### 4.3 复杂统计查询

```php
public function getAdvancedLeadStatistics(): array
{
    // 按状态和区域分组统计
    $statusRegionStats = $this->model->newQuery()
        ->select('status', 'region', DB::raw('COUNT(*) as count'))
        ->groupBy('status', 'region')
        ->get()
        ->groupBy('status')
        ->map(function ($items) {
            return $items->groupBy('region')->map(function ($regionItems) {
                return $regionItems->sum('count');
            });
        });

    // 月度趋势统计
    $monthlyTrend = $this->model->newQuery()
        ->select(DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'), DB::raw('COUNT(*) as count'))
        ->where('created_at', '>=', now()->subMonths(12))
        ->groupBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'))
        ->orderBy('month')
        ->get();

    // 来源效果统计
    $sourceEffectiveness = $this->model->newQuery()
        ->select('source',
            DB::raw('COUNT(*) as total_leads'),
            DB::raw('SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as converted_leads'),
            DB::raw('ROUND(SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) as conversion_rate')
        )
        ->groupBy('source')
        ->having('total_leads', '>', 10) // 至少10个线索才统计转化率
        ->get();

    return [
        'status_region_stats' => $statusRegionStats,
        'monthly_trend' => $monthlyTrend,
        'source_effectiveness' => $sourceEffectiveness
    ];
}
```

### 5. 全文搜索和模糊匹配的实现

#### 5.1 MySQL 全文搜索

```php
public function fullTextSearchLeads(string $keyword): Collection
{
    // 确保表有全文索引：ALTER TABLE crm_lead ADD FULLTEXT(company_full_name, company_short_name, address)

    $conditions = [
        'search' => [
            'keyword' => $keyword,
            'fields' => ['company_full_name'], // 全文搜索通常针对单个字段
            'match_type' => 'full_text'
        ]
    ];

    $query = $this->model->newQuery();
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 添加相关性排序
    $query->selectRaw('*, MATCH(company_full_name) AGAINST(? IN BOOLEAN MODE) as relevance', [$keyword])
          ->orderBy('relevance', 'desc');

    return $query->get();
}
```

#### 5.2 智能模糊匹配

```php
public function intelligentSearchLeads(string $keyword): Collection
{
    $searchStrategies = [
        // 策略1：精确匹配
        [
            'keyword' => $keyword,
            'fields' => ['company_full_name', 'company_short_name'],
            'match_type' => 'exact'
        ],
        // 策略2：前缀匹配
        [
            'keyword' => $keyword,
            'fields' => ['company_full_name', 'company_short_name'],
            'match_type' => 'starts_with'
        ],
        // 策略3：模糊匹配
        [
            'keyword' => $keyword,
            'fields' => ['company_full_name', 'company_short_name', 'internal_name', 'address'],
            'match_type' => 'like'
        ]
    ];

    $results = collect();

    foreach ($searchStrategies as $strategy) {
        $conditions = ['search' => $strategy];
        $query = $this->model->newQuery();
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        $strategyResults = $query->get();

        if ($strategyResults->isNotEmpty()) {
            $results = $results->merge($strategyResults);

            // 如果精确匹配或前缀匹配有结果，优先返回
            if (in_array($strategy['match_type'], ['exact', 'starts_with'])) {
                break;
            }
        }
    }

    return $results->unique('id');
}
```

### 6. 分页查询的性能优化策略

#### 6.1 高效分页查询

```php
public function getOptimizedPaginatedLeads(LeadListDTO $dto): LengthAwarePaginator
{
    // 构建基础查询
    $query = $this->model->newQuery();

    // 只选择必要的字段，避免加载大字段
    $query->select([
        'id', 'company_full_name', 'company_short_name',
        'status', 'region', 'creator_id', 'created_at'
    ]);

    // 构建查询条件
    $conditions = $this->buildQueryConditions($dto);
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 优化分页查询
    $optimizationOptions = [
        'optimize_select' => true,
        'select_fields' => ['id', 'company_full_name', 'company_short_name', 'status', 'region'],
        'use_index' => true,
        'indexes' => ['idx_status', 'idx_creator_id'],
        'cache' => true,
        'cache_ttl' => 300, // 5分钟缓存
        'cache_key' => "leads_page_{$dto->page}_{$dto->pageSize}_" . md5(serialize($dto))
    ];

    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

    // 添加排序
    $sortRules = $this->buildSortRules($dto);
    $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);

    return $this->queryBuilder->buildPaginatedQuery($query, $dto->page, $dto->pageSize);
}
```

#### 6.2 大数据量分页优化

```php
public function getLargeDatasetPagination(int $page, int $perPage, array $filters = []): LengthAwarePaginator
{
    // 对于大数据量，使用游标分页而不是 OFFSET
    $query = $this->model->newQuery();

    // 构建条件
    $conditions = [];
    if (isset($filters['status'])) {
        $conditions['status'] = $filters['status'];
    }

    if (isset($filters['region'])) {
        $conditions['region'] = $filters['region'];
    }

    // 使用 ID 作为游标，避免 OFFSET 性能问题
    if ($page > 1) {
        $lastId = $this->getLastIdFromPreviousPage($page, $perPage, $filters);
        if ($lastId) {
            $conditions['id'] = ['operator' => '>', 'value' => $lastId];
        }
    }

    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 强制使用主键索引
    $optimizationOptions = [
        'use_index' => true,
        'indexes' => ['PRIMARY'],
        'optimize_select' => true,
        'select_fields' => ['id', 'company_full_name', 'status', 'created_at']
    ];

    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

    // 按 ID 排序确保一致性
    $query->orderBy('id', 'asc')->limit($perPage);

    $results = $query->get();

    // 手动构建分页对象
    return new LengthAwarePaginator(
        $results,
        $this->getTotalCount($filters),
        $perPage,
        $page,
        ['path' => request()->url()]
    );
}

private function getLastIdFromPreviousPage(int $page, int $perPage, array $filters): ?int
{
    $offset = ($page - 1) * $perPage - 1;

    if ($offset < 0) {
        return null;
    }

    $query = $this->model->newQuery();
    $conditions = array_intersect_key($filters, array_flip(['status', 'region']));

    if (!empty($conditions)) {
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
    }

    $lastRecord = $query->orderBy('id', 'asc')
        ->offset($offset)
        ->limit(1)
        ->first(['id']);

    return $lastRecord?->id;
}
```

## 查询优化功能

### 1. 查询性能监控和分析

#### 1.1 查询统计信息获取

```php
public function analyzeQueryPerformance(array $conditions): array
{
    $query = $this->model->newQuery();
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 获取查询统计信息
    $statistics = $this->queryBuilder->getQueryStatistics($query);

    // 获取优化建议
    $suggestions = $this->queryBuilder->getOptimizationSuggestions($query);

    // 分析执行计划
    $executionPlan = $this->queryBuilder->analyzeQueryPlan($query);

    return [
        'query_info' => [
            'sql' => $statistics['sql'],
            'bindings_count' => $statistics['bindings_count'],
            'complexity_score' => $statistics['complexity_score'],
            'estimated_cost' => $statistics['estimated_cost'],
            'query_type' => $statistics['query_type'],
            'table_count' => $statistics['table_count']
        ],
        'performance_analysis' => [
            'has_joins' => $statistics['has_joins'],
            'has_subqueries' => $statistics['has_subqueries'],
            'has_aggregations' => $statistics['has_aggregations']
        ],
        'optimization_suggestions' => $suggestions['suggestions'],
        'execution_plan' => $executionPlan['explain_result'],
        'performance_issues' => $executionPlan['performance_issues']
    ];
}
```

#### 1.2 实时性能监控

```php
use App\Utils\UnitFormatter;

public function monitorQueryPerformance(callable $queryCallback): array
{
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);

    // 执行查询
    $result = $queryCallback();

    $endTime = microtime(true);
    $endMemory = memory_get_usage(true);

    $executionTime = $endTime - $startTime;
    $memoryUsage = $endMemory - $startMemory;

    $performanceMetrics = [
        'execution_time' => UnitFormatter::formatTime($executionTime),
        'execution_time_raw' => $executionTime, // 保留原始值用于比较
        'memory_usage' => UnitFormatter::formatMemory($memoryUsage),
        'memory_usage_raw' => $memoryUsage, // 保留原始值用于比较
        'peak_memory' => UnitFormatter::formatMemory(memory_get_peak_usage(true)),
        'result_count' => UnitFormatter::formatCount(is_countable($result) ? count($result) : 1)
    ];

    // 记录性能日志（使用原始值进行比较）
    if ($executionTime > 1.0) { // 超过1秒
        Log::warning('慢查询检测', $performanceMetrics);
    }

    return [
        'result' => $result,
        'performance' => $performanceMetrics
    ];
}
```

### 2. 慢查询检测和优化建议

#### 2.1 慢查询自动检测

```php
public function detectSlowQueries(array $conditions): array
{
    $query = $this->model->newQuery();
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 获取优化建议
    $suggestions = $this->queryBuilder->getOptimizationSuggestions($query);

    $slowQueryIndicators = [];

    // 检查复杂度分数
    if ($suggestions['complexity_score'] > 50) {
        $slowQueryIndicators[] = [
            'type' => 'high_complexity',
            'message' => '查询复杂度过高，建议简化查询条件',
            'score' => $suggestions['complexity_score'],
            'recommendation' => '考虑拆分为多个简单查询或添加适当索引'
        ];
    }

    // 检查预估成本
    if ($suggestions['estimated_cost'] > 100) {
        $slowQueryIndicators[] = [
            'type' => 'high_cost',
            'message' => '查询预估成本过高',
            'cost' => $suggestions['estimated_cost'],
            'recommendation' => '检查是否缺少索引或考虑查询重写'
        ];
    }

    // 分析执行计划
    $executionPlan = $this->queryBuilder->analyzeQueryPlan($query);
    foreach ($executionPlan['performance_issues'] as $issue) {
        $slowQueryIndicators[] = [
            'type' => $issue['type'],
            'message' => $issue['message'],
            'table' => $issue['table'],
            'recommendation' => $this->getRecommendationForIssue($issue['type'])
        ];
    }

    return [
        'is_slow_query' => !empty($slowQueryIndicators),
        'indicators' => $slowQueryIndicators,
        'optimization_suggestions' => $suggestions['suggestions']
    ];
}

private function getRecommendationForIssue(string $issueType): string
{
    return match ($issueType) {
        'no_index' => '为查询字段添加适当的索引',
        'high_row_scan' => '优化查询条件，减少扫描行数',
        'temporary_table' => '避免使用临时表，考虑查询重写或添加索引',
        default => '请检查查询逻辑和数据库设计'
    };
}
```

### 3. 索引使用情况分析

#### 3.1 索引效果分析

```php
public function analyzeIndexUsage(array $conditions): array
{
    $query = $this->model->newQuery();
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 分析执行计划
    $executionPlan = $this->queryBuilder->analyzeQueryPlan($query);

    $indexAnalysis = [
        'used_indexes' => [],
        'missing_indexes' => [],
        'index_effectiveness' => []
    ];

    foreach ($executionPlan['explain_result'] as $step) {
        if (isset($step['key']) && $step['key'] !== null) {
            $indexAnalysis['used_indexes'][] = [
                'table' => $step['table'],
                'index_name' => $step['key'],
                'index_type' => $step['type'],
                'rows_examined' => $step['rows'],
                'effectiveness' => $this->calculateIndexEffectiveness($step)
            ];
        } else {
            $indexAnalysis['missing_indexes'][] = [
                'table' => $step['table'],
                'scan_type' => $step['type'],
                'rows_scanned' => $step['rows'],
                'recommendation' => $this->suggestIndexForTable($step['table'], $conditions)
            ];
        }
    }

    return $indexAnalysis;
}

private function calculateIndexEffectiveness(array $explainStep): string
{
    $rows = $explainStep['rows'] ?? 0;

    return match (true) {
        $rows <= 10 => 'excellent',
        $rows <= 100 => 'good',
        $rows <= 1000 => 'fair',
        default => 'poor'
    };
}

private function suggestIndexForTable(string $table, array $conditions): string
{
    // 根据查询条件建议索引
    $suggestions = [];

    if ($table === 'crm_lead') {
        foreach ($conditions as $field => $value) {
            if (in_array($field, ['status', 'region', 'source', 'industry', 'creator_id'])) {
                $suggestions[] = "CREATE INDEX idx_{$field} ON {$table} ({$field})";
            }
        }

        // 复合索引建议
        if (isset($conditions['status']) && isset($conditions['region'])) {
            $suggestions[] = "CREATE INDEX idx_status_region ON {$table} (status, region)";
        }
    }

    return implode('; ', $suggestions) ?: '建议分析具体查询模式后创建合适的索引';
}
```

### 4. 查询缓存策略和实现

#### 4.1 智能缓存策略

```php
public function getCachedLeadsList(LeadListDTO $dto): LengthAwarePaginator
{
    // 生成缓存键
    $cacheKey = $this->generateLeadListCacheKey($dto);

    // 尝试从缓存获取
    $cachedResult = $this->queryBuilder->setCacheForQuery(
        $this->model->newQuery(),
        $this->calculateCacheTtl($dto),
        $cacheKey
    );

    // 检查缓存
    $cacheManager = app(CacheManager::class);
    $cachedData = $cacheManager->get($cacheKey);

    if ($cachedData !== null) {
        Log::info('线索列表缓存命中', [
            'cache_key' => $cacheKey,
            'page' => $dto->page,
            'filters' => $dto->toArray()
        ]);

        return $cachedData;
    }

    // 缓存未命中，执行查询
    $result = $this->getLeadsList($dto);

    // 存储到缓存
    $cacheManager->set($cacheKey, $result, $this->calculateCacheTtl($dto));

    return $result;
}

private function generateLeadListCacheKey(LeadListDTO $dto): string
{
    $keyComponents = [
        'leads_list',
        'page_' . $dto->page,
        'size_' . $dto->pageSize,
        'filters_' . md5(serialize($dto->toArray())),
        'user_' . (auth()->id() ?? 'guest')
    ];

    return implode(':', $keyComponents);
}

private function calculateCacheTtl(LeadListDTO $dto): int
{
    // 根据查询复杂度和数据变化频率计算缓存时间
    $baseTtl = 300; // 5分钟基础缓存

    // 如果有实时性要求的筛选条件，缩短缓存时间
    if ($dto->status || $dto->assignedToMe) {
        return $baseTtl / 2; // 2.5分钟
    }

    // 如果是历史数据查询，延长缓存时间
    if ($dto->dateRange && $dto->dateRange['end'] < now()->subDays(7)->toDateString()) {
        return $baseTtl * 4; // 20分钟
    }

    return $baseTtl;
}
```

#### 4.2 缓存失效策略

```php
public function invalidateLeadCaches(int $leadId): void
{
    $cacheManager = app(CacheManager::class);

    // 失效相关的缓存模式
    $cachePatterns = [
        "leads_list:*",
        "lead_detail:{$leadId}:*",
        "lead_stats:*",
        "user_leads:*"
    ];

    foreach ($cachePatterns as $pattern) {
        $invalidatedCount = $cacheManager->forgetByPattern($pattern);

        if ($invalidatedCount > 0) {
            Log::info('线索缓存失效', [
                'lead_id' => $leadId,
                'pattern' => $pattern,
                'invalidated_count' => $invalidatedCount
            ]);
        }
    }

    // 按表失效缓存
    $cacheManager->invalidateByTable('crm_lead');
}
```

### 5. N+1 查询问题的检测和解决

#### 5.1 N+1 查询检测

```php
public function detectN1Queries(): array
{
    $detectedIssues = [];

    // 启用查询日志
    DB::enableQueryLog();

    // 执行可能存在 N+1 问题的查询
    $leads = $this->model->newQuery()
        ->where('status', Lead::STATUS_ACTIVE)
        ->limit(10)
        ->get();

    // 模拟访问关联数据（这会触发 N+1 查询）
    foreach ($leads as $lead) {
        $contacts = $lead->contacts; // 潜在的 N+1 查询
        $users = $lead->users; // 潜在的 N+1 查询
    }

    $queries = DB::getQueryLog();
    DB::disableQueryLog();

    // 分析查询模式
    $queryPatterns = [];
    foreach ($queries as $query) {
        $pattern = $this->normalizeQueryPattern($query['query']);
        $queryPatterns[$pattern] = ($queryPatterns[$pattern] ?? 0) + 1;
    }

    // 检测重复查询模式
    foreach ($queryPatterns as $pattern => $count) {
        if ($count > 5) { // 超过5次相同模式的查询
            $detectedIssues[] = [
                'type' => 'n_plus_1',
                'pattern' => $pattern,
                'count' => $count,
                'recommendation' => '使用 with() 方法预加载关联数据'
            ];
        }
    }

    return $detectedIssues;
}

private function normalizeQueryPattern(string $sql): string
{
    // 将具体的 ID 值替换为占位符，识别查询模式
    return preg_replace('/\b\d+\b/', '?', $sql);
}
```

#### 5.2 N+1 查询解决方案

```php
public function getLeadsWithOptimizedRelations(array $filters): Collection
{
    $query = $this->model->newQuery();

    // 预加载所有需要的关联关系
    $query->with([
        'contacts' => function ($q) {
            $q->select('id', 'name', 'mobile', 'department', 'position')
              ->where('deleted_at', null);
        },
        'users' => function ($q) {
            $q->select('id', 'name', 'email')
              ->where('role_type', Lead::ROLE_TYPE_OWNER);
        },
        'creator:id,name'
    ]);

    // 构建查询条件
    $conditions = $this->buildQueryConditions($filters);
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    return $query->get();
}
```

## 高级功能详解

### 1. 查询构建器的扩展机制

#### 1.1 自定义查询方法扩展

EnhancedQueryBuilder 支持通过继承或组合方式扩展功能：

```php
class CustomLeadQueryBuilder extends EnhancedQueryBuilder
{
    /**
     * 添加线索特定的查询方法
     */
    public function addLeadSpecificConditions(Builder $query, array $conditions): Builder
    {
        // 线索状态智能筛选
        if (isset($conditions['smart_status'])) {
            $query = $this->addSmartStatusCondition($query, $conditions['smart_status']);
        }

        // 线索质量评分筛选
        if (isset($conditions['quality_score'])) {
            $query = $this->addQualityScoreCondition($query, $conditions['quality_score']);
        }

        // 跟进时效性筛选
        if (isset($conditions['follow_up_urgency'])) {
            $query = $this->addFollowUpUrgencyCondition($query, $conditions['follow_up_urgency']);
        }

        return $query;
    }

    private function addSmartStatusCondition(Builder $query, string $smartStatus): Builder
    {
        return match ($smartStatus) {
            'active' => $query->whereIn('status', [1, 2, 3]),
            'pending' => $query->where('status', 2)->where('last_followed_at', '<', now()->subDays(7)),
            'hot' => $query->where('status', 1)->where('stage', '>=', 3),
            'cold' => $query->where('last_followed_at', '<', now()->subDays(30)),
            default => $query
        };
    }

    private function addQualityScoreCondition(Builder $query, array $scoreRange): Builder
    {
        // 基于多个因素计算质量评分
        return $query->selectRaw('*, (
            CASE
                WHEN industry IN (1, 2, 3) THEN 20 ELSE 10 END +
                CASE
                    WHEN region IN (1, 2) THEN 15 ELSE 5 END +
                CASE
                    WHEN source = 1 THEN 25 ELSE 10 END +
                CASE
                    WHEN stage >= 3 THEN 30 ELSE 10 END +
                CASE
                    WHEN last_followed_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 10 ELSE 0 END
            ) as quality_score
        ')->havingBetween('quality_score', [$scoreRange['min'], $scoreRange['max']]);
    }
}
```

#### 1.2 查询构建器组合使用

```php
class LeadQueryService
{
    public function __construct(
        private QueryBuilderInterface $queryBuilder,
        private CustomLeadQueryBuilder $customQueryBuilder
    ) {}

    public function buildAdvancedLeadQuery(array $filters): Builder
    {
        $query = Lead::query();

        // 使用标准查询构建器
        $standardConditions = array_intersect_key($filters,
            array_flip(['status', 'region', 'source'])
        );

        if (!empty($standardConditions)) {
            $query = $this->queryBuilder->buildComplexQuery($standardConditions, $query);
        }

        // 使用自定义查询构建器
        $customConditions = array_intersect_key($filters,
            array_flip(['smart_status', 'quality_score', 'follow_up_urgency'])
        );

        if (!empty($customConditions)) {
            $query = $this->customQueryBuilder->addLeadSpecificConditions($query, $customConditions);
        }

        return $query;
    }
}
```

### 2. 自定义查询方法的添加

#### 2.1 业务特定查询方法

```php
trait LeadQueryMethods
{
    /**
     * 查找需要跟进的线索
     */
    public function findLeadsNeedingFollowUp(): Collection
    {
        $conditions = [
            'status' => ['operator' => 'in', 'value' => [1, 2]],
            'last_followed_at' => [
                'operator' => '<',
                'value' => now()->subDays(7)->toDateTimeString()
            ]
        ];

        $query = $this->model->newQuery();
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        // 按跟进紧急程度排序
        $query->selectRaw('*, DATEDIFF(NOW(), last_followed_at) as days_since_follow_up')
              ->orderBy('days_since_follow_up', 'desc');

        return $query->get();
    }

    /**
     * 查找高价值线索
     */
    public function findHighValueLeads(array $criteria = []): Collection
    {
        $conditions = [
            'status' => Lead::STATUS_ACTIVE,
            'industry' => ['operator' => 'in', 'value' => $criteria['target_industries'] ?? [1, 2, 3]],
            'stage' => ['operator' => '>=', 'value' => 3]
        ];

        // 添加关联条件：有决策者联系人
        $conditions['relations']['contacts'] = [
            'position' => ['operator' => 'like', 'value' => '%总%|%经理%|%主管%']
        ];

        $query = $this->model->newQuery()
            ->with(['contacts' => function ($q) {
                $q->where('position', 'like', '%总%')
                  ->orWhere('position', 'like', '%经理%')
                  ->orWhere('position', 'like', '%主管%');
            }]);

        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        return $query->get();
    }
}
```

### 3. 查询结果的后处理和转换

#### 3.1 结果数据转换

```php
public function getLeadsWithProcessedData(array $filters): Collection
{
    // 构建基础查询
    $query = $this->model->newQuery()
        ->with(['contacts:id,name,mobile,position', 'users:id,name']);

    $conditions = $this->buildQueryConditions($filters);
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    $leads = $query->get();

    // 后处理数据
    return $leads->map(function ($lead) {
        return $this->processLeadData($lead);
    });
}

private function processLeadData($lead): array
{
    return [
        'id' => $lead->id,
        'company_info' => [
            'full_name' => $lead->company_full_name,
            'short_name' => $lead->company_short_name,
            'display_name' => $lead->internal_name ?: $lead->company_short_name
        ],
        'business_info' => [
            'region_name' => $this->getRegionName($lead->region),
            'industry_name' => $this->getIndustryName($lead->industry),
            'source_name' => $this->getSourceName($lead->source),
            'status_name' => $this->getStatusName($lead->status),
            'stage_name' => $this->getStageName($lead->stage)
        ],
        'contact_summary' => [
            'total_contacts' => $lead->contacts->count(),
            'key_contacts' => $lead->contacts->filter(function ($contact) {
                return str_contains($contact->position, '总') ||
                       str_contains($contact->position, '经理');
            })->values(),
            'primary_mobile' => $lead->contacts->first()?->mobile
        ],
        'assignment_info' => [
            'owner' => $lead->users->where('pivot.role_type', Lead::ROLE_TYPE_OWNER)->first(),
            'collaborators' => $lead->users->where('pivot.role_type', Lead::ROLE_TYPE_COLLABORATOR),
            'creator' => $lead->creator
        ],
        'timeline_info' => [
            'created_at' => $lead->created_at->toDateTimeString(),
            'last_followed_at' => $lead->last_followed_at?->toDateTimeString(),
            'days_since_creation' => $lead->created_at->diffInDays(now()),
            'days_since_follow_up' => $lead->last_followed_at?->diffInDays(now())
        ]
    ];
}
```

### 4. 批量查询操作的优化

#### 4.1 批量数据查询

```php
public function batchQueryLeads(array $batchConditions): array
{
    // 使用 EnhancedQueryBuilder 的批量查询功能
    $results = $this->queryBuilder->buildBatchQuery($batchConditions, Lead::class);

    $processedResults = [];

    foreach ($results as $index => $collection) {
        $processedResults[$index] = [
            'conditions' => $batchConditions[$index],
            'count' => $collection->count(),
            'data' => $collection->take(10), // 只返回前10条作为预览
            'has_more' => $collection->count() > 10
        ];
    }

    return $processedResults;
}
```

#### 4.2 批量统计查询

```php
public function batchStatisticsQuery(): array
{
    $batchConditions = [
        // 各状态线索统计
        ['status' => 1], // 活跃线索
        ['status' => 2], // 跟进中线索
        ['status' => 3], // 已转化线索

        // 各区域线索统计
        ['region' => 1], // 华北区域
        ['region' => 2], // 华南区域
        ['region' => 3], // 华东区域

        // 各来源线索统计
        ['source' => 1], // 官网来源
        ['source' => 2], // 推广来源
        ['source' => 3], // 转介绍来源
    ];

    $batchResults = $this->queryBuilder->buildBatchQuery($batchConditions, Lead::class);

    return [
        'status_stats' => [
            'active' => $batchResults[0]->count(),
            'following' => $batchResults[1]->count(),
            'converted' => $batchResults[2]->count()
        ],
        'region_stats' => [
            'north' => $batchResults[3]->count(),
            'south' => $batchResults[4]->count(),
            'east' => $batchResults[5]->count()
        ],
        'source_stats' => [
            'website' => $batchResults[6]->count(),
            'promotion' => $batchResults[7]->count(),
            'referral' => $batchResults[8]->count()
        ]
    ];
}
```

### 5. 数据库连接池的管理

#### 5.1 连接池配置优化

```php
public function optimizeConnectionPool(): void
{
    // 在 DatabaseServiceProvider 中配置连接池
    $this->app->singleton('db.connection.pool', function ($app) {
        return new ConnectionPoolManager([
            'min_connections' => 5,
            'max_connections' => 20,
            'idle_timeout' => 300,
            'validation_query' => 'SELECT 1'
        ]);
    });
}
```

#### 5.2 连接池监控

```php
public function monitorConnectionPool(): array
{
    $connectionStats = DB::getConnectionStats();

    return [
        'active_connections' => $connectionStats['active'],
        'idle_connections' => $connectionStats['idle'],
        'total_connections' => $connectionStats['total'],
        'max_connections' => $connectionStats['max'],
        'connection_usage_rate' => round(
            ($connectionStats['active'] / $connectionStats['max']) * 100, 2
        )
    ];
}
```

## 实际业务场景示例

### 1. 线索列表的复杂筛选查询

#### 1.1 完整的线索列表查询实现

```php
public function getAdvancedLeadsList(LeadListDTO $dto): LengthAwarePaginator
{
    // 构建复杂查询条件
    $conditions = [
        // 基础筛选条件
        'status' => $dto->status,
        'region' => $dto->region,
        'source' => $dto->source,
        'industry' => $dto->industry,
        'stage' => $dto->stage,

        // 日期范围筛选
        'created_at_range' => $dto->dateRange ? [
            'start' => $dto->dateRange['start'],
            'end' => $dto->dateRange['end'],
            'type' => 'date_range'
        ] : null,

        // 搜索条件
        'search' => $dto->keyword ? [
            'keyword' => $dto->keyword,
            'fields' => ['company_full_name', 'company_short_name', 'internal_name'],
            'match_type' => 'like'
        ] : null,

        // 负责人筛选
        'relations' => []
    ];

    // 我负责的线索
    if ($dto->assignedToMe) {
        $conditions['relations']['users'] = [
            'user_id' => auth()->id(),
            'role_type' => Lead::ROLE_TYPE_OWNER
        ];
    }

    // 我协同的线索
    if ($dto->collaboratedByMe) {
        $conditions['relations']['users'] = [
            'user_id' => auth()->id(),
            'role_type' => Lead::ROLE_TYPE_COLLABORATOR
        ];
    }

    // 联系人手机号筛选
    if ($dto->contactMobile) {
        $conditions['relations']['contacts'] = [
            'mobile' => $dto->contactMobile
        ];
    }

    // 构建查询
    $query = $this->model->newQuery()
        ->with([
            'creator:id,name',
            'contacts' => function ($q) {
                $q->select('id', 'name', 'mobile', 'position')
                  ->limit(3); // 只加载前3个联系人
            },
            'users' => function ($q) {
                $q->select('id', 'name')
                  ->where('role_type', Lead::ROLE_TYPE_OWNER);
            }
        ]);

    // 应用查询条件
    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 添加排序
    $sortRules = [
        $dto->sortField . ':' . $dto->sortDirection,
        'created_at:desc' // 默认按创建时间倒序
    ];
    $query = $this->queryBuilder->addDynamicSorting($query, $sortRules);

    // 查询优化
    $optimizationOptions = [
        'optimize_select' => true,
        'optimize_joins' => true,
        'use_index' => true,
        'indexes' => ['idx_status', 'idx_creator_id', 'idx_deleted_at'],
        'cache' => true,
        'cache_ttl' => 300
    ];
    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

    return $this->queryBuilder->buildPaginatedQuery($query, $dto->page, $dto->pageSize);
}
```

### 2. 线索统计报表的聚合查询

#### 2.1 综合统计报表

```php
public function generateLeadStatisticsReport(array $filters = []): array
{
    $baseQuery = $this->model->newQuery();

    // 应用基础筛选条件
    if (!empty($filters)) {
        $conditions = array_intersect_key($filters,
            array_flip(['region', 'source', 'industry', 'creator_id'])
        );

        if (!empty($conditions)) {
            $baseQuery = $this->queryBuilder->buildComplexQuery($conditions, $baseQuery);
        }
    }

    // 多维度聚合查询
    $aggregations = [
        // 基础统计
        'count' => ['field' => '*'],
        'avg' => ['field' => 'stage'],

        // 分组统计
        'group_count' => ['field' => 'status'],
        'region_count' => ['field' => 'region'],
        'source_count' => ['field' => 'source'],
        'industry_count' => ['field' => 'industry']
    ];

    $statisticsResult = $this->queryBuilder->buildAggregateQuery($baseQuery, $aggregations);

    // 时间趋势分析
    $trendQuery = clone $baseQuery;
    $trendQuery->selectRaw('
        DATE(created_at) as date,
        COUNT(*) as daily_count,
        SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as daily_converted
    ')
    ->where('created_at', '>=', now()->subDays(30))
    ->groupBy(DB::raw('DATE(created_at)'))
    ->orderBy('date');

    $trendData = $trendQuery->get();

    // 转化漏斗分析
    $funnelQuery = clone $baseQuery;
    $funnelData = $funnelQuery->selectRaw('
        stage,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM crm_lead WHERE deleted_at IS NULL), 2) as percentage
    ')
    ->groupBy('stage')
    ->orderBy('stage')
    ->get();

    return [
        'overview' => [
            'total_leads' => $statisticsResult['count'],
            'average_stage' => round($statisticsResult['avg'], 2),
            'conversion_rate' => $this->calculateConversionRate($statisticsResult)
        ],
        'distribution' => [
            'by_status' => $this->formatGroupCount($statisticsResult['group_count']),
            'by_region' => $this->formatGroupCount($statisticsResult['region_count']),
            'by_source' => $this->formatGroupCount($statisticsResult['source_count']),
            'by_industry' => $this->formatGroupCount($statisticsResult['industry_count'])
        ],
        'trend_analysis' => $trendData->toArray(),
        'funnel_analysis' => $funnelData->toArray()
    ];
}

private function formatGroupCount(array $groupData): array
{
    return collect($groupData)->map(function ($item) {
        return [
            'value' => $item['value'],
            'count' => $item['count'],
            'name' => $this->getDisplayName($item['field'], $item['value'])
        ];
    })->toArray();
}
```

### 3. 跨表关联的数据同步查询

#### 3.1 数据一致性检查查询

```php
public function checkDataConsistency(): array
{
    $inconsistencies = [];

    // 检查线索与联系人关联的一致性
    $orphanedContacts = DB::table('crm_lead_contact_relation as lcr')
        ->leftJoin('crm_lead as l', 'l.id', '=', 'lcr.lead_id')
        ->leftJoin('crm_contact as c', 'c.id', '=', 'lcr.contact_id')
        ->whereNull('l.id')
        ->orWhereNull('c.id')
        ->select('lcr.id', 'lcr.lead_id', 'lcr.contact_id')
        ->get();

    if ($orphanedContacts->isNotEmpty()) {
        $inconsistencies['orphaned_contact_relations'] = $orphanedContacts->toArray();
    }

    // 检查线索与用户关联的一致性
    $orphanedUserRelations = DB::table('crm_lead_user_relation as lur')
        ->leftJoin('crm_lead as l', 'l.id', '=', 'lur.lead_id')
        ->whereNull('l.id')
        ->select('lur.id', 'lur.lead_id', 'lur.user_id')
        ->get();

    if ($orphanedUserRelations->isNotEmpty()) {
        $inconsistencies['orphaned_user_relations'] = $orphanedUserRelations->toArray();
    }

    // 检查主负责人唯一性
    $duplicatePrimaryOwners = DB::table('crm_lead_user_relation')
        ->select('lead_id', DB::raw('COUNT(*) as owner_count'))
        ->where('role_type', Lead::ROLE_TYPE_OWNER)
        ->where('is_primary', 1)
        ->whereNull('deleted_at')
        ->groupBy('lead_id')
        ->having('owner_count', '>', 1)
        ->get();

    if ($duplicatePrimaryOwners->isNotEmpty()) {
        $inconsistencies['duplicate_primary_owners'] = $duplicatePrimaryOwners->toArray();
    }

    return $inconsistencies;
}
```

#### 3.2 跨表数据同步

```php
public function syncRelatedData(int $leadId): array
{
    $syncResults = [];

    // 同步联系人数据
    $contactSyncResult = $this->syncContactData($leadId);
    $syncResults['contacts'] = $contactSyncResult;

    // 同步用户关联数据
    $userSyncResult = $this->syncUserRelationData($leadId);
    $syncResults['user_relations'] = $userSyncResult;

    return $syncResults;
}

private function syncContactData(int $leadId): array
{
    // 查找线索关联的联系人
    $leadContacts = DB::table('crm_lead_contact_relation')
        ->where('lead_id', $leadId)
        ->whereNull('deleted_at')
        ->pluck('contact_id')
        ->toArray();

    // 检查联系人是否存在
    $existingContacts = DB::table('crm_contact')
        ->whereIn('id', $leadContacts)
        ->whereNull('deleted_at')
        ->pluck('id')
        ->toArray();

    $missingContacts = array_diff($leadContacts, $existingContacts);

    if (!empty($missingContacts)) {
        // 清理无效的关联关系
        DB::table('crm_lead_contact_relation')
            ->where('lead_id', $leadId)
            ->whereIn('contact_id', $missingContacts)
            ->update(['deleted_at' => now()]);
    }

    return [
        'total_relations' => count($leadContacts),
        'valid_relations' => count($existingContacts),
        'cleaned_relations' => count($missingContacts)
    ];
}
```

### 4. 大数据量的分页查询优化

#### 4.1 深度分页优化

```php
public function getDeepPaginationLeads(int $page, int $perPage): LengthAwarePaginator
{
    // 对于深度分页（page > 100），使用游标分页
    if ($page > 100) {
        return $this->getCursorBasedPagination($page, $perPage);
    }

    // 常规分页优化
    $query = $this->model->newQuery();

    // 只查询必要字段
    $query->select([
        'id', 'company_full_name', 'company_short_name',
        'status', 'region', 'creator_id', 'created_at'
    ]);

    // 强制使用复合索引
    $optimizationOptions = [
        'use_index' => true,
        'indexes' => ['idx_status_created_at'], // 假设有这个复合索引
        'optimize_select' => true
    ];

    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

    // 使用覆盖索引查询
    $query->orderBy('created_at', 'desc')->orderBy('id', 'desc');

    return $this->queryBuilder->buildPaginatedQuery($query, $page, $perPage);
}

private function getCursorBasedPagination(int $page, int $perPage): LengthAwarePaginator
{
    // 计算游标位置
    $offset = ($page - 1) * $perPage;

    // 首先获取游标 ID
    $cursorQuery = $this->model->newQuery()
        ->select('id')
        ->orderBy('created_at', 'desc')
        ->orderBy('id', 'desc')
        ->offset($offset)
        ->limit(1);

    $cursor = $cursorQuery->first();

    if (!$cursor) {
        return new LengthAwarePaginator([], 0, $perPage, $page);
    }

    // 基于游标查询数据
    $dataQuery = $this->model->newQuery()
        ->select([
            'id', 'company_full_name', 'company_short_name',
            'status', 'region', 'creator_id', 'created_at'
        ])
        ->where('id', '<=', $cursor->id)
        ->orderBy('created_at', 'desc')
        ->orderBy('id', 'desc')
        ->limit($perPage);

    $results = $dataQuery->get();
    $total = $this->model->count(); // 可以缓存这个值

    return new LengthAwarePaginator($results, $total, $perPage, $page);
}
```

### 5. 实时搜索功能的查询实现

#### 5.1 实时搜索优化

```php
public function realtimeSearchLeads(string $keyword, int $limit = 10): Collection
{
    // 实时搜索需要快速响应，使用多级缓存
    $cacheKey = "realtime_search:" . md5($keyword) . ":{$limit}";

    $cacheManager = app(CacheManager::class);
    $cachedResult = $cacheManager->get($cacheKey);

    if ($cachedResult !== null) {
        return collect($cachedResult);
    }

    // 构建快速搜索查询
    $conditions = [
        'status' => ['operator' => 'in', 'value' => [1, 2]], // 只搜索活跃线索
        'search' => [
            'keyword' => $keyword,
            'fields' => ['company_full_name', 'company_short_name'],
            'match_type' => 'starts_with' // 前缀匹配更快
        ]
    ];

    $query = $this->model->newQuery()
        ->select(['id', 'company_full_name', 'company_short_name', 'status'])
        ->limit($limit);

    $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

    // 强制使用前缀索引
    $optimizationOptions = [
        'use_index' => true,
        'indexes' => ['idx_company_name_prefix'], // 假设有前缀索引
        'optimize_select' => true
    ];

    $query = $this->queryBuilder->optimizeQuery($query, $optimizationOptions);

    $results = $query->get();

    // 缓存结果（短时间缓存）
    $cacheManager->set($cacheKey, $results->toArray(), 60); // 1分钟缓存

    return $results;
}
```

## 错误处理和调试

### 1. 常见查询错误的诊断和解决

#### 1.1 查询条件验证错误

```php
public function handleQueryValidationErrors(array $conditions): array
{
    try {
        // 验证查询条件
        $allowedFields = [
            'id', 'company_full_name', 'company_short_name', 'internal_name',
            'region', 'source', 'industry', 'status', 'stage', 'creator_id',
            'created_at', 'updated_at', 'last_followed_at'
        ];

        $this->queryBuilder->validateConditions($conditions, $allowedFields);

        $query = $this->model->newQuery();
        return ['success' => true, 'query' => $this->queryBuilder->buildComplexQuery($conditions, $query)];

    } catch (QueryOptimizationException $e) {
        return $this->handleQueryOptimizationException($e);
    } catch (\InvalidArgumentException $e) {
        return [
            'success' => false,
            'error_type' => 'validation_error',
            'message' => $e->getMessage(),
            'suggestions' => $this->getValidationErrorSuggestions($e->getMessage())
        ];
    }
}

private function handleQueryOptimizationException(QueryOptimizationException $e): array
{
    $errorResponse = [
        'success' => false,
        'error_type' => 'query_optimization_error',
        'message' => $e->getMessage(),
        'context' => $e->getContext()
    ];

    // 根据异常类型提供具体建议
    if ($e->isFieldValidationError()) {
        $errorResponse['suggestions'] = [
            '检查字段名是否正确',
            '确认字段在允许的查询字段列表中',
            '检查字段的数据类型是否匹配'
        ];
    } elseif ($e->isQueryTooComplex()) {
        $errorResponse['suggestions'] = [
            '简化查询条件',
            '拆分为多个简单查询',
            '添加适当的数据库索引',
            '考虑使用缓存减少查询频率'
        ];
    } elseif ($e->isQueryTimeout()) {
        $errorResponse['suggestions'] = [
            '优化查询条件',
            '添加数据库索引',
            '增加查询超时时间',
            '考虑异步处理'
        ];
    }

    return $errorResponse;
}

private function getValidationErrorSuggestions(string $errorMessage): array
{
    $suggestions = [];

    if (str_contains($errorMessage, '字段')) {
        $suggestions[] = '检查字段名拼写是否正确';
        $suggestions[] = '确认字段存在于数据库表中';
    }

    if (str_contains($errorMessage, '操作符')) {
        $suggestions[] = '使用支持的操作符：=, !=, >, >=, <, <=, like, in, between 等';
    }

    if (str_contains($errorMessage, '值')) {
        $suggestions[] = '检查查询值的数据类型是否正确';
        $suggestions[] = '确认数组类型的值格式正确';
    }

    return $suggestions;
}
```

### 2. 查询性能问题的排查方法

#### 2.1 性能问题诊断工具

```php
public function diagnosePerformanceIssues(array $conditions): array
{
    $diagnosis = [
        'issues' => [],
        'recommendations' => [],
        'query_analysis' => []
    ];

    try {
        $query = $this->model->newQuery();
        $query = $this->queryBuilder->buildComplexQuery($conditions, $query);

        // 获取查询统计
        $statistics = $this->queryBuilder->getQueryStatistics($query);
        $diagnosis['query_analysis'] = $statistics;

        // 检查查询复杂度
        if ($statistics['complexity_score'] > 50) {
            $diagnosis['issues'][] = [
                'type' => 'high_complexity',
                'severity' => 'warning',
                'message' => "查询复杂度过高：{$statistics['complexity_score']}",
                'impact' => '可能导致查询响应缓慢'
            ];

            $diagnosis['recommendations'][] = '考虑简化查询条件或拆分为多个查询';
        }

        // 检查表数量
        if ($statistics['table_count'] > 4) {
            $diagnosis['issues'][] = [
                'type' => 'too_many_tables',
                'severity' => 'warning',
                'message' => "涉及表数量过多：{$statistics['table_count']}",
                'impact' => '多表连接可能影响性能'
            ];

            $diagnosis['recommendations'][] = '考虑使用子查询或分步查询';
        }

        // 分析执行计划
        $executionPlan = $this->queryBuilder->analyzeQueryPlan($query);
        foreach ($executionPlan['performance_issues'] as $issue) {
            $diagnosis['issues'][] = [
                'type' => $issue['type'],
                'severity' => $this->getIssueSeverity($issue['type']),
                'message' => $issue['message'],
                'table' => $issue['table'],
                'impact' => $this->getIssueImpact($issue['type'])
            ];
        }

        // 获取优化建议
        $suggestions = $this->queryBuilder->getOptimizationSuggestions($query);
        $diagnosis['recommendations'] = array_merge(
            $diagnosis['recommendations'],
            $suggestions['suggestions']
        );

    } catch (Exception $e) {
        $diagnosis['issues'][] = [
            'type' => 'query_error',
            'severity' => 'error',
            'message' => $e->getMessage(),
            'impact' => '查询无法执行'
        ];
    }

    return $diagnosis;
}

private function getIssueSeverity(string $issueType): string
{
    return match ($issueType) {
        'no_index', 'full_table_scan' => 'high',
        'high_row_scan', 'temporary_table' => 'medium',
        'suboptimal_join' => 'low',
        default => 'medium'
    };
}

private function getIssueImpact(string $issueType): string
{
    return match ($issueType) {
        'no_index' => '全表扫描，严重影响性能',
        'full_table_scan' => '扫描大量数据行，响应缓慢',
        'high_row_scan' => '扫描行数过多，影响响应时间',
        'temporary_table' => '使用临时表，增加内存和磁盘开销',
        'suboptimal_join' => '连接效率不佳，轻微影响性能',
        default => '可能影响查询性能'
    };
}
```

### 3. 日志记录和监控配置

#### 3.1 查询日志配置

```php
public function configureQueryLogging(): void
{
    // 在 DatabaseServiceProvider 中配置查询监听器
    DB::listen(function ($query) {
        $executionTime = $query->time;

        // 记录慢查询
        if ($executionTime > 1000) { // 超过1秒
            Log::warning('慢查询检测', [
                'sql' => $query->sql,
                'bindings' => $query->bindings,
                'execution_time' => $executionTime,
                'connection' => $query->connectionName,
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10)
            ]);
        }

        // 记录查询统计
        if ($executionTime > 500) { // 超过500毫秒
            Log::info('查询性能监控', [
                'sql_pattern' => $this->normalizeQueryPattern($query->sql),
                'execution_time' => $executionTime,
                'bindings_count' => count($query->bindings)
            ]);
        }
    });
}
```

### 4. 查询调试工具的使用

#### 4.1 查询调试助手

```php
class QueryDebugHelper
{
    public function __construct(private QueryBuilderInterface $queryBuilder) {}

    /**
     * 调试查询构建过程
     */
    public function debugQueryBuilding(array $conditions): array
    {
        $debugInfo = [
            'input_conditions' => $conditions,
            'validation_result' => null,
            'built_query' => null,
            'optimization_applied' => null,
            'final_sql' => null,
            'execution_plan' => null,
            'performance_metrics' => null
        ];

        try {
            // 1. 验证条件
            $debugInfo['validation_result'] = $this->queryBuilder->validateConditions($conditions);

            // 2. 构建查询
            $query = Lead::query();
            $builtQuery = $this->queryBuilder->buildComplexQuery($conditions, $query);
            $debugInfo['built_query'] = [
                'sql' => $builtQuery->toSql(),
                'bindings' => $builtQuery->getBindings()
            ];

            // 3. 应用优化
            $optimizedQuery = $this->queryBuilder->optimizeQuery($builtQuery);
            $debugInfo['optimization_applied'] = [
                'sql' => $optimizedQuery->toSql(),
                'bindings' => $optimizedQuery->getBindings()
            ];

            // 4. 分析执行计划
            $debugInfo['execution_plan'] = $this->queryBuilder->analyzeQueryPlan($optimizedQuery);

            // 5. 获取性能指标
            $debugInfo['performance_metrics'] = $this->queryBuilder->getQueryStatistics($optimizedQuery);

        } catch (Exception $e) {
            $debugInfo['error'] = [
                'type' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return $debugInfo;
    }

    /**
     * 比较查询性能
     */
    public function compareQueryPerformance(array $conditions1, array $conditions2): array
    {
        $comparison = [
            'query1' => $this->measureQueryPerformance($conditions1),
            'query2' => $this->measureQueryPerformance($conditions2),
            'comparison' => []
        ];

        // 性能对比分析
        $comparison['comparison'] = [
            'execution_time_diff' => $comparison['query2']['execution_time'] - $comparison['query1']['execution_time'],
            'complexity_diff' => $comparison['query2']['complexity_score'] - $comparison['query1']['complexity_score'],
            'memory_diff' => $comparison['query2']['memory_usage'] - $comparison['query1']['memory_usage'],
            'recommendation' => $this->getPerformanceRecommendation($comparison)
        ];

        return $comparison;
    }

    private function measureQueryPerformance(array $conditions): array
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        try {
            $query = Lead::query();
            $query = $this->queryBuilder->buildComplexQuery($conditions, $query);
            $statistics = $this->queryBuilder->getQueryStatistics($query);

            // 执行查询（限制结果数量）
            $results = $query->limit(100)->get();

            return [
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2),
                'memory_usage' => memory_get_usage(true) - $startMemory,
                'result_count' => $results->count(),
                'complexity_score' => $statistics['complexity_score'],
                'sql' => $query->toSql()
            ];

        } catch (Exception $e) {
            return [
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2),
                'memory_usage' => memory_get_usage(true) - $startMemory,
                'error' => $e->getMessage(),
                'complexity_score' => 0,
                'sql' => null
            ];
        }
    }

    private function getPerformanceRecommendation(array $comparison): string
    {
        $timeDiff = $comparison['comparison']['execution_time_diff'];
        $complexityDiff = $comparison['comparison']['complexity_diff'];

        if ($timeDiff > 100 && $complexityDiff > 10) {
            return '查询2的性能明显较差，建议使用查询1的方案';
        } elseif ($timeDiff < -100 && $complexityDiff < -10) {
            return '查询2的性能明显较好，建议使用查询2的方案';
        } else {
            return '两个查询的性能相近，可根据业务需求选择';
        }
    }
}
```

### 5. 最佳实践总结

#### 5.1 查询构建最佳实践

1. **条件构建**：使用结构化的条件数组，避免手动拼接 SQL
2. **关联查询**：合理使用 `with()` 预加载，避免 N+1 查询问题
3. **索引优化**：根据查询模式创建合适的单列和复合索引
4. **缓存策略**：对频繁查询的结果进行缓存，设置合理的 TTL
5. **性能监控**：定期分析慢查询，持续优化数据库性能

#### 5.2 错误处理最佳实践

1. **异常分类**：使用专门的 QueryOptimizationException 处理查询相关异常
2. **日志记录**：详细记录查询错误和性能问题
3. **降级策略**：查询失败时提供备用查询方案
4. **用户友好**：将技术错误转换为用户可理解的提示信息

#### 5.3 开发调试建议

1. **使用调试工具**：利用 QueryDebugHelper 分析查询构建过程
2. **性能对比**：对比不同查询方案的性能表现
3. **执行计划分析**：定期检查查询的执行计划
4. **缓存监控**：监控缓存命中率和失效情况

## 总结

EnhancedQueryBuilder 提供了强大的查询构建和优化功能，通过合理使用其各项特性，可以显著提升应用的数据库查询性能。在实际使用中，建议：

1. 根据业务需求选择合适的查询方法
2. 重视查询性能监控和优化
3. 合理使用缓存策略
4. 建立完善的错误处理机制
5. 持续关注和优化数据库性能

通过遵循本指南的最佳实践，可以充分发挥 EnhancedQueryBuilder 的优势，构建高性能、可维护的数据库查询系统。
```
```
```
```
```
```
```

# TransactionManager 使用指南

## 概述

TransactionManager 是一个增强的事务管理器，提供了比 Laravel 原生事务更强大的功能，包括嵌套事务、死锁检测、超时控制、回调机制和性能监控。

## 基础使用说明

### 1. 核心功能和设计理念

TransactionManager 基于以下设计理念：

- **可靠性优先**: 提供强大的异常处理和重试机制
- **性能监控**: 内置事务执行历史和性能统计
- **灵活扩展**: 支持回调机制和自定义配置
- **业务友好**: 区分业务异常和系统异常，确保正确的错误传播

### 2. 依赖注入配置

#### 2.1 服务绑定

在 `DatabaseServiceProvider` 中已配置好服务绑定：

```php
// 单例绑定
$this->app->singleton(TransactionManagerInterface::class, function ($app) {
    return new TransactionManager;
});

// 别名注册
$this->app->alias(TransactionManagerInterface::class, TransactionManager::class);
$this->app->alias(TransactionManagerInterface::class, 'transaction.manager');
```

#### 2.2 在 Service 类中使用

```php
class LeadService
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository,
        private TransactionManagerInterface $transactionManager
    ) {}
}
```

### 3. 基本事务执行

#### 3.1 简单事务执行

```php
public function createLead(LeadCreateDTO $dto): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        // 业务规则验证
        if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
            throw BusinessException::fromErrorCode('Lead.company_already_exists');
        }
        
        // 数据持久化
        $lead = $this->leadRepository->create($dto->toArray());
        
        // 日志记录
        Log::info('线索创建成功', ['lead_id' => $lead->id]);
        
        return $lead;
    });
}
```

#### 3.2 带超时控制的事务

```php
public function complexOperation(): mixed
{
    return $this->transactionManager->executeInTransaction(
        callback: function () {
            // 复杂的业务逻辑
            return $this->performComplexOperation();
        },
        attempts: 1,
        timeout: 60 // 60秒超时
    );
}
```

## 高级功能详解

### 1. 事务回调机制

#### 1.1 注册回调函数

```php
// 在服务构造函数中注册回调
public function __construct(TransactionManagerInterface $transactionManager)
{
    $this->transactionManager = $transactionManager;
    
    // 注册事务开始前回调
    $this->transactionManager->registerCallback('before_begin', function () {
        Log::info('事务即将开始');
    });
    
    // 注册事务提交后回调
    $this->transactionManager->registerCallback('after_commit', function () {
        // 清除缓存
        Cache::tags(['leads'])->flush();
    });
    
    // 注册事务回滚后回调
    $this->transactionManager->registerCallback('after_rollback', function () {
        Log::warning('事务已回滚，需要检查数据一致性');
    });
}
```

#### 1.2 回调事件类型

- **before_begin**: 事务开始前执行
- **after_commit**: 事务提交后执行
- **after_rollback**: 事务回滚后执行

### 2. 死锁检测和重试机制

#### 2.1 自动死锁重试

```php
public function batchUpdateStatus(array $ids, int $status): int
{
    return $this->transactionManager->executeWithDeadlockRetry(
        callback: function () use ($ids, $status) {
            return $this->leadRepository->batchUpdateStatus($ids, $status);
        },
        maxRetries: 3,    // 最多重试3次
        retryDelay: 100   // 延迟100ms
    );
}
```

#### 2.2 死锁检测关键词

TransactionManager 自动检测以下死锁关键词：
- `deadlock found`
- `lock wait timeout`
- `deadlock detected`
- `try restarting transaction`

### 3. 事务嵌套处理

#### 3.1 创建嵌套事务

```php
public function complexBusinessOperation(): mixed
{
    return $this->transactionManager->executeInTransaction(function () {
        // 主事务逻辑
        $lead = $this->createMainLead();
        
        // 创建嵌套事务处理关联数据
        $savepointName = $this->transactionManager->beginNestedTransaction();
        
        try {
            // 嵌套事务中的操作
            $this->createRelatedContacts($lead);
            $this->createUserRelations($lead);
            
            // 提交嵌套事务
            $this->transactionManager->commitNestedTransaction($savepointName);
            
        } catch (Exception $e) {
            // 回滚嵌套事务，但保持主事务
            $this->transactionManager->rollbackNestedTransaction($savepointName);
            
            // 记录警告但不中断主事务
            Log::warning('关联数据创建失败', ['error' => $e->getMessage()]);
        }
        
        return $lead;
    });
}
```

#### 3.2 保存点管理

```php
// 自定义保存点名称
$savepointName = $this->transactionManager->beginNestedTransaction('lead_contacts');

// 释放保存点（不回滚）
$this->transactionManager->releaseSavepoint($savepointName);

// 获取活动保存点
$activePoints = $this->transactionManager->getActiveSavepoints();
```

### 4. 事务隔离级别管理

#### 4.1 设置隔离级别

```php
// 设置读已提交隔离级别
$this->transactionManager->setIsolationLevel('READ_COMMITTED');

// 设置可重复读隔离级别（MySQL 默认）
$this->transactionManager->setIsolationLevel('REPEATABLE_READ');

// 设置串行化隔离级别（最高级别）
$this->transactionManager->setIsolationLevel('SERIALIZABLE');
```

#### 4.2 获取当前隔离级别

```php
$currentLevel = $this->transactionManager->getIsolationLevel();
Log::info('当前事务隔离级别', ['level' => $currentLevel]);
```

### 5. 性能监控功能

#### 5.1 事务统计信息

```php
$stats = $this->transactionManager->getTransactionStatistics();
/*
返回结构：
[
    'total_transactions' => 150,
    'successful_transactions' => 145,
    'failed_transactions' => 5,
    'average_duration' => 0.25,
    'total_duration' => 37.5,
    'deadlock_retries' => 3,
    'timeout_errors' => 1,
    'active_savepoints' => 2
]
*/
```

#### 5.2 事务执行历史

```php
// 获取最近100条事务历史
$history = $this->transactionManager->getTransactionHistory(100);

// 清除历史记录
$this->transactionManager->clearTransactionHistory();
```

## 复杂查询场景示例

### 1. 多表关联查询的事务处理

```php
public function createLeadWithRelations(LeadCreateDTO $dto, array $contacts, array $users): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($dto, $contacts, $users) {
        // 1. 创建主线索记录
        $lead = $this->leadRepository->create($dto->toArray());
        
        // 2. 批量创建联系人
        $contactIds = [];
        foreach ($contacts as $contactData) {
            $contact = $this->contactRepository->create($contactData);
            $contactIds[] = $contact->id;
        }
        
        // 3. 创建线索-联系人关联
        $this->leadContactRelationRepository->createBatch($lead->id, $contactIds);
        
        // 4. 创建线索-用户关联
        foreach ($users as $userData) {
            $this->leadUserRelationRepository->create([
                'lead_id' => $lead->id,
                'user_id' => $userData['user_id'],
                'role_type' => $userData['role_type'],
                'is_primary' => $userData['is_primary'] ?? 0,
            ]);
        }
        
        return $lead;
    }, 1, 45); // 45秒超时
}
```

### 2. 批量数据操作的事务优化

```php
public function batchImportLeads(array $leadsData): array
{
    return $this->transactionManager->executeWithDeadlockRetry(function () use ($leadsData) {
        $results = [];
        $batchSize = 100; // 每批处理100条
        
        foreach (array_chunk($leadsData, $batchSize) as $batch) {
            // 使用嵌套事务处理每批数据
            $savepointName = $this->transactionManager->beginNestedTransaction();
            
            try {
                $batchResults = $this->leadRepository->createBatch($batch);
                $this->transactionManager->commitNestedTransaction($savepointName);
                $results = array_merge($results, $batchResults);
                
            } catch (Exception $e) {
                $this->transactionManager->rollbackNestedTransaction($savepointName);
                Log::warning('批量导入部分失败', [
                    'batch_size' => count($batch),
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $results;
    }, 5, 200); // 最多重试5次，延迟200ms
}
```

### 3. 条件查询和动态 SQL 构建

```php
public function conditionalUpdate(array $conditions, array $updates): int
{
    return $this->transactionManager->executeInTransaction(function () use ($conditions, $updates) {
        // 构建动态查询条件
        $query = $this->leadRepository->newQuery();
        
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }
        
        // 获取符合条件的记录数
        $affectedCount = $query->count();
        
        if ($affectedCount === 0) {
            throw BusinessException::fromErrorCode('Lead.no_records_match_conditions');
        }
        
        // 执行批量更新
        $result = $query->update($updates);
        
        Log::info('条件更新完成', [
            'conditions' => $conditions,
            'updates' => $updates,
            'affected_rows' => $result
        ]);
        
        return $result;
    });
}
```

### 4. 分页查询在事务中的最佳实践

```php
public function getLeadsWithTransactionSafety(LeadListDTO $dto): LengthAwarePaginator
{
    // 分页查询通常不需要事务，但如果涉及复杂的业务逻辑可以使用
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        // 1. 构建查询条件
        $query = $this->leadRepository->buildListQuery($dto);

        // 2. 应用业务规则过滤
        $query = $this->applyBusinessFilters($query, $dto);

        // 3. 执行分页查询
        $results = $query->paginate($dto->perPage);

        // 4. 记录查询统计
        Log::info('线索列表查询完成', [
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage()
        ]);

        return $results;
    }, 1, 10); // 短超时，因为查询应该很快
}
```

### 5. 聚合查询和统计分析的事务处理

```php
public function generateLeadStatistics(array $filters): array
{
    return $this->transactionManager->executeInTransaction(function () use ($filters) {
        // 设置读已提交隔离级别，避免长时间锁定
        $this->transactionManager->setIsolationLevel('READ_COMMITTED');

        $stats = [];

        // 1. 基础统计
        $stats['total_leads'] = $this->leadRepository->countByFilters($filters);
        $stats['active_leads'] = $this->leadRepository->countByStatus(Lead::STATUS_ACTIVE);

        // 2. 按区域统计
        $stats['by_region'] = $this->leadRepository->getStatsByRegion($filters);

        // 3. 按来源统计
        $stats['by_source'] = $this->leadRepository->getStatsBySource($filters);

        // 4. 时间趋势分析
        $stats['monthly_trend'] = $this->leadRepository->getMonthlyTrend($filters);

        return $stats;
    }, 1, 30); // 30秒超时，统计查询可能较慢
}
```

## 错误处理和调试

### 1. 常见异常类型和处理策略

#### 1.1 业务异常处理

```php
public function updateLead(int $id, LeadUpdateDTO $dto): Lead
{
    try {
        return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
            $lead = $this->leadRepository->findById($id);

            if (!$lead) {
                // 业务异常会直接传播，不会被包装成 TransactionException
                throw BusinessException::fromErrorCode('Lead.not_found');
            }

            return $this->leadRepository->update($id, $dto->toArray());
        });

    } catch (BusinessException $e) {
        // 业务异常直接重新抛出，保持原有的错误码和消息
        throw $e;

    } catch (TransactionException $e) {
        // 事务异常需要特殊处理
        if ($e->isDeadlock()) {
            Log::warning('更新线索时发生死锁', ['lead_id' => $id]);
            throw BusinessException::fromErrorCode('Lead.update_conflict');
        }

        if ($e->isTimeout()) {
            Log::error('更新线索超时', ['lead_id' => $id, 'timeout' => $e->getContext()['timeout']]);
            throw BusinessException::fromErrorCode('Lead.update_timeout');
        }

        // 其他事务异常
        throw BusinessException::fromErrorCode('Lead.update_failed');
    }
}
```

#### 1.2 异常类型说明

- **BusinessException**: 业务逻辑异常，会直接传播到 Controller
- **TransactionException**: 事务系统异常，包含详细的上下文信息
- **QueryException**: 数据库查询异常，会被转换为 TransactionException

### 2. 事务回滚的触发条件

#### 2.1 自动回滚条件

- 任何未捕获的异常
- 业务异常（BusinessException）
- 数据库约束违反
- 事务超时
- 死锁检测（会自动重试）

#### 2.2 手动回滚

```php
public function conditionalOperation(): mixed
{
    return $this->transactionManager->executeInTransaction(function () {
        $lead = $this->createLead($dto);

        // 根据业务条件决定是否继续
        if (!$this->validateBusinessCondition($lead)) {
            // 抛出异常触发回滚
            throw BusinessException::fromErrorCode('Lead.business_condition_failed');
        }

        return $lead;
    });
}
```

### 3. 性能问题诊断

#### 3.1 事务执行时间监控

```php
// 获取事务统计信息
$stats = $this->transactionManager->getTransactionStatistics();

if ($stats['average_duration'] > 1.0) { // 平均执行时间超过1秒
    Log::warning('事务执行时间过长', [
        'average_duration' => $stats['average_duration'],
        'total_transactions' => $stats['total_transactions']
    ]);
}
```

#### 3.2 内存使用监控

```php
// 事务历史中包含内存使用信息
$history = $this->transactionManager->getTransactionHistory(10);

foreach ($history as $record) {
    if ($record['memory_usage'] > 50 * 1024 * 1024) { // 超过50MB
        Log::warning('事务内存使用过高', [
            'memory_usage' => $record['memory_usage'],
            'action' => $record['action']
        ]);
    }
}
```

### 4. 日志记录和监控配置

#### 4.1 事务日志配置

在 `config/logging.php` 中配置事务专用日志通道：

```php
'channels' => [
    'transaction' => [
        'driver' => 'daily',
        'path' => storage_path('logs/transaction.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
        'formatter' => App\Logging\TransactionLogFormatter::class,
    ],
],
```

#### 4.2 监控关键指标

```php
public function monitorTransactionHealth(): array
{
    $stats = $this->transactionManager->getTransactionStatistics();

    $health = [
        'status' => 'healthy',
        'issues' => []
    ];

    // 检查失败率
    $failureRate = $stats['failed_transactions'] / max($stats['total_transactions'], 1);
    if ($failureRate > 0.05) { // 失败率超过5%
        $health['status'] = 'warning';
        $health['issues'][] = '事务失败率过高: ' . ($failureRate * 100) . '%';
    }

    // 检查平均执行时间
    if ($stats['average_duration'] > 2.0) { // 平均执行时间超过2秒
        $health['status'] = 'warning';
        $health['issues'][] = '事务平均执行时间过长: ' . $stats['average_duration'] . 's';
    }

    // 检查死锁重试次数
    if ($stats['deadlock_retries'] > 10) { // 死锁重试次数过多
        $health['status'] = 'critical';
        $health['issues'][] = '死锁重试次数过多: ' . $stats['deadlock_retries'];
    }

    return $health;
}
```

## 最佳实践指南

### 1. 在 Service 层中的正确使用模式

#### 1.1 标准事务模式

```php
class LeadService
{
    public function createLead(LeadCreateDTO $dto): Lead
    {
        // ✅ 正确：使用事务包装完整的业务逻辑
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务规则验证
            $this->validateBusinessRules($dto);

            // 数据持久化
            $lead = $this->leadRepository->create($dto->toArray());

            // 后续处理
            $this->processAfterCreate($lead);

            return $lead;
        });
    }

    // ❌ 错误：在 Repository 层使用事务
    // Repository 应该专注于数据访问，不应包含事务逻辑
}
```

#### 1.2 事务边界设计

```php
// ✅ 正确：事务边界清晰，包含完整的业务操作
public function transferLead(int $leadId, int $fromUserId, int $toUserId): bool
{
    return $this->transactionManager->executeInTransaction(function () use ($leadId, $fromUserId, $toUserId) {
        // 1. 验证转移条件
        $this->validateTransferConditions($leadId, $fromUserId, $toUserId);

        // 2. 更新线索负责人
        $this->leadUserRelationRepository->updatePrimaryUser($leadId, $toUserId);

        // 3. 记录转移历史
        $this->leadHistoryRepository->recordTransfer($leadId, $fromUserId, $toUserId);

        // 4. 发送通知
        $this->notificationService->sendTransferNotification($leadId, $toUserId);

        return true;
    });
}

// ❌ 错误：事务边界过小，无法保证数据一致性
public function transferLeadWrong(int $leadId, int $fromUserId, int $toUserId): bool
{
    // 每个操作单独使用事务，无法保证整体一致性
    $this->transactionManager->executeInTransaction(fn() =>
        $this->leadUserRelationRepository->updatePrimaryUser($leadId, $toUserId)
    );

    $this->transactionManager->executeInTransaction(fn() =>
        $this->leadHistoryRepository->recordTransfer($leadId, $fromUserId, $toUserId)
    );

    return true;
}
```

### 2. 与 Repository 层的协作方式

#### 2.1 职责分离

```php
// ✅ 正确：Service 层管理事务，Repository 层专注数据访问
class LeadService
{
    public function updateLeadWithValidation(int $id, LeadUpdateDTO $dto): Lead
    {
        return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
            // Service 层：业务逻辑和验证
            $lead = $this->leadRepository->findById($id);
            $this->validateUpdatePermissions($lead, $dto);

            // Repository 层：纯数据操作
            $this->leadRepository->update($id, $dto->toArray());

            return $this->leadRepository->findById($id); // 返回最新数据
        });
    }
}

class LeadRepository
{
    // ✅ 正确：Repository 不包含事务逻辑
    public function update(int $id, array $data): bool
    {
        return $this->model->where('id', $id)->update($data);
    }

    // ❌ 错误：Repository 不应该管理事务
    public function updateWithTransaction(int $id, array $data): bool
    {
        return $this->transactionManager->executeInTransaction(function () use ($id, $data) {
            return $this->model->where('id', $id)->update($data);
        });
    }
}
```

### 3. 性能优化技巧

#### 3.1 批量操作优化

```php
public function optimizedBatchCreate(array $leadsData): array
{
    return $this->transactionManager->executeInTransaction(function () use ($leadsData) {
        // ✅ 使用批量插入而不是循环单条插入
        $leads = $this->leadRepository->createBatch($leadsData);

        // ✅ 批量创建关联关系
        $relationData = [];
        foreach ($leads as $index => $lead) {
            if (isset($leadsData[$index]['contacts'])) {
                foreach ($leadsData[$index]['contacts'] as $contactId) {
                    $relationData[] = [
                        'lead_id' => $lead->id,
                        'contact_id' => $contactId,
                        'created_at' => now()
                    ];
                }
            }
        }

        if (!empty($relationData)) {
            $this->leadContactRelationRepository->createBatch($relationData);
        }

        return $leads;
    }, 1, 60); // 批量操作可能需要更长时间
}
```

#### 3.2 查询优化

```php
public function getLeadWithOptimizedQueries(int $id): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($id) {
        // ✅ 使用预加载避免 N+1 查询
        $lead = $this->leadRepository->findWithRelations($id, [
            'contacts',
            'users',
            'history' => function ($query) {
                $query->latest()->limit(10); // 只加载最近10条历史
            }
        ]);

        if (!$lead) {
            throw BusinessException::fromErrorCode('Lead.not_found');
        }

        return $lead;
    }, 1, 5); // 查询操作应该很快
}
```

### 4. 常见陷阱和避免方法

#### 4.1 避免长时间事务

```php
// ❌ 错误：事务时间过长
public function processLargeDataset(array $data): void
{
    $this->transactionManager->executeInTransaction(function () use ($data) {
        foreach ($data as $item) {
            // 大量数据处理，可能导致长时间锁定
            $this->processComplexItem($item);
            sleep(1); // 模拟耗时操作
        }
    });
}

// ✅ 正确：分批处理，减少事务时间
public function processLargeDatasetOptimized(array $data): void
{
    $batchSize = 100;

    foreach (array_chunk($data, $batchSize) as $batch) {
        $this->transactionManager->executeInTransaction(function () use ($batch) {
            foreach ($batch as $item) {
                $this->processComplexItem($item);
            }
        }, 1, 30); // 每批最多30秒
    }
}
```

#### 4.2 避免嵌套事务滥用

```php
// ❌ 错误：过度使用嵌套事务
public function overNestedTransaction(): void
{
    $this->transactionManager->executeInTransaction(function () {
        $sp1 = $this->transactionManager->beginNestedTransaction();

        $sp2 = $this->transactionManager->beginNestedTransaction(); // 过度嵌套
        $sp3 = $this->transactionManager->beginNestedTransaction(); // 过度嵌套

        // 复杂的嵌套逻辑，难以维护
    });
}

// ✅ 正确：合理使用嵌套事务
public function reasonableNestedTransaction(): Lead
{
    return $this->transactionManager->executeInTransaction(function () {
        // 主要业务逻辑
        $lead = $this->createMainLead($dto);

        // 可选的附加操作使用嵌套事务
        $savepointName = $this->transactionManager->beginNestedTransaction();
        try {
            $this->createOptionalRelations($lead);
            $this->transactionManager->commitNestedTransaction($savepointName);
        } catch (Exception $e) {
            $this->transactionManager->rollbackNestedTransaction($savepointName);
            Log::warning('附加操作失败，但主操作继续', ['error' => $e->getMessage()]);
        }

        return $lead;
    });
}
```

## 实际业务场景

### 1. 线索创建的完整事务流程

```php
public function createLeadComplete(LeadCreateDTO $dto, array $contacts = [], array $users = []): Lead
{
    return SimpleTransactionLogger::logBusinessTransaction(
        function () use ($dto, $contacts, $users) {
            return $this->transactionManager->executeInTransaction(function () use ($dto, $contacts, $users) {
                // 1. 业务规则验证
                if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
                    throw BusinessException::fromErrorCode('Lead.company_already_exists');
                }

                // 2. 创建主线索记录
                $lead = $this->leadRepository->create($dto->toArray());

                // 3. 处理联系人关联（使用嵌套事务）
                if (!empty($contacts)) {
                    $contactSavepoint = $this->transactionManager->beginNestedTransaction('contacts');
                    try {
                        $this->processLeadContacts($lead->id, $contacts);
                        $this->transactionManager->commitNestedTransaction($contactSavepoint);
                    } catch (Exception $e) {
                        $this->transactionManager->rollbackNestedTransaction($contactSavepoint);
                        Log::warning('联系人关联创建失败', ['lead_id' => $lead->id, 'error' => $e->getMessage()]);
                    }
                }

                // 4. 处理用户关联
                if (!empty($users)) {
                    $this->processLeadUsers($lead->id, $users);
                }

                // 5. 初始化线索状态
                $this->initializeLeadStatus($lead);

                return $lead;
            }, 1, 45); // 45秒超时
        },
        'Lead',
        'create',
        $dto->toArray(),
        ['company_name' => $dto->companyFullName]
    );
}
```

### 2. 批量导入数据的事务处理

```php
public function batchImportLeadsWithValidation(array $importData): array
{
    $results = [
        'success' => [],
        'failed' => [],
        'statistics' => []
    ];

    return $this->transactionManager->executeInTransaction(function () use ($importData, &$results) {
        $batchSize = 50; // 每批50条记录
        $totalBatches = ceil(count($importData) / $batchSize);

        foreach (array_chunk($importData, $batchSize) as $batchIndex => $batch) {
            $batchSavepoint = $this->transactionManager->beginNestedTransaction("batch_{$batchIndex}");

            try {
                // 批量验证
                $validatedBatch = $this->validateImportBatch($batch);

                // 批量创建
                $createdLeads = $this->leadRepository->createBatch($validatedBatch);

                $this->transactionManager->commitNestedTransaction($batchSavepoint);
                $results['success'] = array_merge($results['success'], $createdLeads);

                Log::info('批量导入批次成功', [
                    'batch_index' => $batchIndex + 1,
                    'batch_size' => count($batch),
                    'total_batches' => $totalBatches
                ]);

            } catch (Exception $e) {
                $this->transactionManager->rollbackNestedTransaction($batchSavepoint);

                // 记录失败的批次
                $results['failed'][] = [
                    'batch_index' => $batchIndex + 1,
                    'error' => $e->getMessage(),
                    'data' => $batch
                ];

                Log::warning('批量导入批次失败', [
                    'batch_index' => $batchIndex + 1,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $results['statistics'] = [
            'total_records' => count($importData),
            'success_count' => count($results['success']),
            'failed_count' => count($results['failed']),
            'success_rate' => count($results['success']) / count($importData) * 100
        ];

        return $results;
    }, 1, 300); // 批量导入可能需要较长时间
}
```

### 3. 复杂业务规则验证的事务管理

```php
public function updateLeadWithComplexValidation(int $id, LeadUpdateDTO $dto): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($id, $dto) {
        // 1. 获取当前线索数据
        $currentLead = $this->leadRepository->findById($id);
        if (!$currentLead) {
            throw BusinessException::fromErrorCode('Lead.not_found');
        }

        // 2. 复杂业务规则验证
        $this->validateComplexBusinessRules($currentLead, $dto);

        // 3. 状态变更验证
        if ($dto->status !== null && $dto->status !== $currentLead->status) {
            $this->validateStatusTransition($currentLead->status, $dto->status);
        }

        // 4. 权限验证
        $this->validateUpdatePermissions($currentLead, auth()->id());

        // 5. 执行更新
        $updated = $this->leadRepository->update($id, $dto->toArray());

        if (!$updated) {
            throw BusinessException::fromErrorCode('Lead.update_failed');
        }

        // 6. 触发后续业务逻辑
        $this->triggerAfterUpdateActions($currentLead, $dto);

        return $this->leadRepository->findById($id);
    });
}

private function validateComplexBusinessRules(Lead $currentLead, LeadUpdateDTO $dto): void
{
    // 复杂的业务规则验证逻辑
    if ($dto->companyFullName && $dto->companyFullName !== $currentLead->company_full_name) {
        // 检查公司名称重复
        if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
            throw BusinessException::fromErrorCode('Lead.company_name_exists');
        }

        // 检查公司名称格式
        if (!$this->validateCompanyNameFormat($dto->companyFullName)) {
            throw BusinessException::fromErrorCode('Lead.invalid_company_name_format');
        }
    }

    // 区域变更验证
    if ($dto->region && $dto->region !== $currentLead->region) {
        if (!$this->canChangeRegion($currentLead, $dto->region)) {
            throw BusinessException::fromErrorCode('Lead.region_change_not_allowed');
        }
    }
}
```

### 4. 跨模块数据同步的事务协调

```php
public function synchronizeLeadData(int $leadId): array
{
    return $this->transactionManager->executeInTransaction(function () use ($leadId) {
        $syncResults = [];

        // 1. 获取线索数据
        $lead = $this->leadRepository->findById($leadId);
        if (!$lead) {
            throw BusinessException::fromErrorCode('Lead.not_found');
        }

        // 2. 同步到客户模块
        $customerSavepoint = $this->transactionManager->beginNestedTransaction('customer_sync');
        try {
            $syncResults['customer'] = $this->customerService->syncFromLead($lead);
            $this->transactionManager->commitNestedTransaction($customerSavepoint);
        } catch (Exception $e) {
            $this->transactionManager->rollbackNestedTransaction($customerSavepoint);
            $syncResults['customer'] = ['error' => $e->getMessage()];
        }

        // 3. 同步到营销模块
        $marketingSavepoint = $this->transactionManager->beginNestedTransaction('marketing_sync');
        try {
            $syncResults['marketing'] = $this->marketingService->syncFromLead($lead);
            $this->transactionManager->commitNestedTransaction($marketingSavepoint);
        } catch (Exception $e) {
            $this->transactionManager->rollbackNestedTransaction($marketingSavepoint);
            $syncResults['marketing'] = ['error' => $e->getMessage()];
        }

        // 4. 更新同步状态
        $this->leadRepository->updateSyncStatus($leadId, $syncResults);

        return $syncResults;
    }, 1, 120); // 跨模块同步可能需要较长时间
}
```

## 监控和维护

### 1. 事务健康检查

```php
public function performTransactionHealthCheck(): array
{
    $healthReport = [
        'overall_status' => 'healthy',
        'checks' => [],
        'recommendations' => []
    ];

    // 1. 检查事务统计
    $stats = $this->transactionManager->getTransactionStatistics();
    $healthReport['checks']['statistics'] = $this->analyzeTransactionStats($stats);

    // 2. 检查活动保存点
    $activeSavepoints = $this->transactionManager->getActiveSavepoints();
    if (count($activeSavepoints) > 10) {
        $healthReport['checks']['savepoints'] = [
            'status' => 'warning',
            'message' => '活动保存点过多，可能存在未正确清理的嵌套事务',
            'count' => count($activeSavepoints)
        ];
    }

    // 3. 检查事务历史
    $history = $this->transactionManager->getTransactionHistory(50);
    $recentFailures = array_filter($history, fn($record) => $record['action'] === 'rollback');

    if (count($recentFailures) > 5) {
        $healthReport['checks']['recent_failures'] = [
            'status' => 'critical',
            'message' => '最近事务失败次数过多',
            'failure_count' => count($recentFailures)
        ];
        $healthReport['overall_status'] = 'critical';
    }

    return $healthReport;
}
```

### 2. 性能优化建议

#### 2.1 事务大小控制

- **小事务**: 单个实体操作，超时设置 5-10 秒
- **中等事务**: 多个相关实体操作，超时设置 30-60 秒
- **大事务**: 批量操作或复杂业务流程，超时设置 120-300 秒

#### 2.2 重试策略

- **死锁重试**: 3-5 次，延迟 100-500ms
- **超时重试**: 通常不建议重试，应优化业务逻辑
- **网络异常**: 可以重试 2-3 次

#### 2.3 监控指标

定期监控以下指标：
- 事务成功率（应 > 95%）
- 平均执行时间（应 < 1 秒）
- 死锁重试次数（应 < 总事务数的 1%）
- 活动保存点数量（应 < 5 个）

## 总结

TransactionManager 提供了强大而灵活的事务管理能力，正确使用可以显著提高应用的数据一致性和可靠性。关键要点：

1. **在 Service 层使用事务**，Repository 层专注数据访问
2. **合理设计事务边界**，包含完整的业务操作
3. **善用嵌套事务**处理可选操作和错误恢复
4. **监控事务性能**，及时发现和解决问题
5. **区分异常类型**，确保正确的错误处理和传播

通过遵循这些最佳实践，可以构建出高质量、高可靠性的业务应用。

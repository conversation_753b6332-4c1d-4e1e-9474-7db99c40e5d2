# 错误处理重构文档（简化版）

## 概述

本文档说明了如何重构项目中的错误处理机制，移除 ErrorCode 常量类，直接使用配置文件中的错误码字符串，实现更简洁的错误管理方案。

## 重构目标

1. **移除中间层**：删除 ErrorCode 常量类，减少代码复杂度
2. **直接引用**：直接使用配置文件中的错误码字符串
3. **保持功能**：维持参数化错误消息和统一管理的优势
4. **向后兼容**：保持现有异常处理机制不变
5. **简化维护**：减少文件数量，降低维护成本

## 重构对比

### 重构前（使用常量类）

```php
// 需要导入常量类
use App\Constants\ErrorCode;

// 使用常量
throw BusinessException::fromErrorCode(ErrorCode::LEAD_NOT_FOUND);
throw BusinessException::fromErrorCode(
    ErrorCode::LEAD_OPERATION_NOT_SUPPORTED,
    ['type' => $operation['type']]
);
```

### 重构后（直接使用字符串）

```php
// 无需导入额外类

// 直接使用字符串
throw BusinessException::fromErrorCode('Lead.not_found');
throw BusinessException::fromErrorCode(
    'Lead.operation_not_supported',
    ['type' => $operation['type']]
);
```

## 核心变更

### 1. 删除 ErrorCode 常量类

- **文件**: `app/Constants/ErrorCode.php` ❌ 已删除
- **原因**: 简化代码结构，减少不必要的抽象层

### 2. 更新 BusinessException 类

```php
// 新增方法：动态获取所有错误码
public static function getAllErrorCodes(): array
{
    $errors = config('errors', []);
    $errorCodes = [];
    
    foreach ($errors as $module => $moduleErrors) {
        foreach ($moduleErrors as $errorType => $config) {
            $errorCodes[] = "{$module}.{$errorType}";
        }
    }
    
    return $errorCodes;
}
```

### 3. 重构 LeadService 类

```php
// 移除导入
- use App\Constants\ErrorCode;

// 更新错误处理
- throw BusinessException::fromErrorCode(ErrorCode::LEAD_NOT_FOUND);
+ throw BusinessException::fromErrorCode('Lead.not_found');

- throw BusinessException::fromErrorCode(ErrorCode::LEAD_COMPANY_ALREADY_EXISTS);
+ throw BusinessException::fromErrorCode('Lead.company_already_exists');
```

## 配置文件结构

`config/errors.php` 保持不变：

```php
return [
    'Lead' => [
        'not_found' => [
            'message' => '线索不存在',
            'code' => 404,
        ],
        'company_already_exists' => [
            'message' => '该公司已存在线索记录',
            'code' => 409,
        ],
        'operation_not_supported' => [
            'message' => '不支持的操作类型: :type',
            'code' => 400,
        ],
    ],
    // 其他模块...
];
```

## 使用方法

### 1. 基本错误处理

```php
if (!$lead) {
    throw BusinessException::fromErrorCode('Lead.not_found');
}
```

### 2. 参数化错误消息

```php
throw BusinessException::fromErrorCode(
    'Lead.operation_not_supported',
    ['type' => 'invalid_operation']
);
```

### 3. 检查错误码是否存在

```php
if (BusinessException::hasErrorCode('Lead.not_found')) {
    // 错误码存在
}
```

### 4. 获取所有错误码

```php
$errorCodes = BusinessException::getAllErrorCodes();
// 返回: ['Lead.not_found', 'Lead.company_already_exists', ...]
```

### 5. 向后兼容

```php
// 仍然支持传统方式
throw new BusinessException('自定义错误消息', 422);
```

## 重构优势

### 1. 代码简化

- ✅ 减少了一个文件（ErrorCode.php）
- ✅ 移除了不必要的导入语句
- ✅ 降低了代码复杂度

### 2. 维护便利

- ✅ 只需维护配置文件
- ✅ 错误码直观可见
- ✅ 减少了学习成本

### 3. 功能保持

- ✅ 保持参数化错误消息
- ✅ 保持统一的错误管理
- ✅ 保持向后兼容性

### 4. 开发效率

- ✅ 减少文件切换
- ✅ 错误码一目了然
- ✅ 配置即文档

## 添加新错误类型

### 步骤

1. 在 `config/errors.php` 中添加配置
2. 在代码中直接使用新的错误码字符串

### 示例

```php
// 1. 添加配置
'Lead' => [
    'status_locked' => [
        'message' => '线索状态已锁定，无法修改',
        'code' => 423,
    ],
],

// 2. 使用错误码
throw BusinessException::fromErrorCode('Lead.status_locked');
```

## 错误码命名规范

### 格式

- 使用 `module.error_type` 格式
- 模块名使用小写单数形式
- 错误类型使用下划线分隔的小写单词

### 示例

- ✅ `lead.not_found`
- ✅ `contact.mobile_exists`
- ✅ `user.permission_denied`
- ❌ `Lead.NotFound`
- ❌ `contact_mobile_exists`

## 测试验证

### 单元测试

```php
public function test_create_exception_from_error_code(): void
{
    $exception = BusinessException::fromErrorCode('Lead.not_found');
    
    $this->assertEquals('线索不存在', $exception->getMessage());
    $this->assertEquals(404, $exception->getCode());
}
```

### 语法检查

```bash
php -l app/Exceptions/BusinessException.php  # ✅ 通过
php -l app/Services/LeadService.php          # ✅ 通过
```

## 总结

本次重构成功简化了错误处理机制：

- **移除了不必要的抽象层**：删除 ErrorCode 常量类
- **保持了核心功能**：参数化消息、统一管理、向后兼容
- **提升了开发体验**：代码更简洁，维护更容易
- **降低了学习成本**：减少了需要理解的概念和文件

重构后的代码更加直观和简洁，同时保持了所有必要的功能，为项目的长期发展提供了更好的基础。

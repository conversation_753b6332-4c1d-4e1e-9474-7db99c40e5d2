# CRM API 项目分层架构文档

## 1. 项目架构概览

### 1.1 整体分层架构图

```mermaid
graph TB
    subgraph "客户端层"
        Client[客户端应用]
    end
    
    subgraph "API 网关层"
        Route[路由层]
        Middleware[中间件层]
    end
    
    subgraph "控制器层"
        Controller[Controller 层]
        Request[Request 验证层]
    end
    
    subgraph "业务逻辑层"
        DTO[DTO 数据传输层]
        Service[Service 业务服务层]
    end
    
    subgraph "数据访问层"
        Repository[Repository 仓储层]
        Contract[Contract 接口层]
    end
    
    subgraph "数据模型层"
        Model[Model 数据模型层]
        Database[(数据库)]
    end
    
    subgraph "响应处理层"
        Resource[Resource 资源层]
        ApiResponse[API 响应层]
    end
    
    subgraph "异常处理层"
        Exception[Exception 异常层]
        Handler[Handler 处理器]
    end
    
    Client --> Route
    Route --> Middleware
    Middleware --> Controller
    Controller --> Request
    Request --> DTO
    DTO --> Service
    Service --> Repository
    Repository --> Contract
    Contract --> Model
    Model --> Database
    Service --> Resource
    Resource --> ApiResponse
    Service --> Exception
    Exception --> Handler
    Handler --> ApiResponse
    ApiResponse --> Client
```

### 1.2 请求处理完整链路

```mermaid
sequenceDiagram
    participant C as 客户端
    participant R as 路由
    participant M as 中间件
    participant Ctrl as Controller
    participant Req as Request
    participant D as DTO
    participant S as Service
    participant Repo as Repository
    participant Model as Model
    participant DB as 数据库
    participant Res as Resource
    participant API as ApiResponse
    
    C->>R: HTTP 请求
    R->>M: 路由匹配
    M->>Ctrl: 中间件验证通过
    Ctrl->>Req: 请求验证
    Req->>D: 创建 DTO
    D->>S: 调用业务服务
    S->>Repo: 调用仓储方法
    Repo->>Model: 操作数据模型
    Model->>DB: 数据库操作
    DB-->>Model: 返回数据
    Model-->>Repo: 返回模型实例
    Repo-->>S: 返回业务数据
    S->>Res: 格式化响应
    Res->>API: 统一响应格式
    API-->>C: JSON 响应
```

### 1.3 各层职责说明

| 层次 | 职责 | 禁止事项 |
|------|------|----------|
| **Controller** | HTTP 请求处理，调用 Service 层 | 直接访问 Repository 或 Model，包含业务逻辑 |
| **Request** | 输入验证，数据格式化 | 包含业务逻辑 |
| **DTO** | 数据传输对象，类型安全 | 包含业务逻辑，直接数据库操作 |
| **Service** | 业务逻辑处理，事务管理 | 直接处理 HTTP 请求，直接访问 Model |
| **Repository** | 数据访问抽象，查询封装 | 包含业务逻辑 |
| **Model** | 数据模型，关系定义 | 复杂业务逻辑，HTTP 相关操作 |
| **Resource** | API 响应格式化 | 业务逻辑处理 |

### 1.4 依赖注入和接口设计原则

#### 依赖倒置原则应用

```php
// ✅ 正确示例：Service 依赖 Repository 接口
class LeadService
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository,
        private TransactionManagerInterface $transactionManager
    ) {}
}

// ✅ 正确示例：在 ServiceProvider 中绑定接口实现
class RepositoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(LeadRepositoryInterface::class, LeadRepository::class);
    }
}
```

#### 接口设计原则

- **单一职责**：每个接口只定义一类相关的操作
- **接口隔离**：避免臃肿的接口，使用细粒度接口
- **契约明确**：接口方法签名清晰，返回类型明确

## 2. 具体实现分析

### 2.1 现有架构实现情况

#### ✅ 符合规范的部分

1. **完整的分层架构**
   - Controller → Service → Repository → Model 层次清晰
   - 每层职责边界明确，依赖方向正确

2. **Repository 模式实现**
   - 所有 Repository 都定义了接口
   - 通过依赖注入容器管理依赖关系
   - 实现了数据访问层的抽象

3. **DTO 模式应用**
   - 使用 readonly 属性确保不可变性
   - 提供类型安全的数据传输
   - 封装数据转换和验证逻辑

4. **统一异常处理**
   - BusinessException 支持配置化错误码
   - 全局异常处理器统一响应格式
   - 结构化错误信息

5. **API 响应规范**
   - ApiResponse 类统一响应格式
   - Resource 类格式化输出数据
   - 支持分页和错误响应

#### 🔧 需要改进的部分

1. **PHPStan 级别提升**
   - 当前 Level 5，建议提升到 Level 8
   - 增强类型检查和代码质量

2. **测试覆盖率**
   - 需要补充单元测试和集成测试
   - 核心业务逻辑测试覆盖率需达到 80%+

3. **文档完善**
   - API 文档需要更详细的示例
   - 架构决策记录（ADR）文档

### 2.2 代码质量分析

#### 类型注解完整性

<augment_code_snippet path="app/DTOs/Lead/LeadCreateDTO.php" mode="EXCERPT">
````php
/**
 * 线索创建数据传输对象
 */
class LeadCreateDTO extends BaseDTO
{
    public readonly string $companyFullName;
    public readonly ?string $internalName;
    public readonly int $region;
    
    public function __construct(array $data)
    {
        $this->companyFullName = $data['company_full_name'];
        $this->region = (int)$data['region'];
    }
}
````
</augment_code_snippet>

#### 异常处理机制

<augment_code_snippet path="app/Exceptions/BusinessException.php" mode="EXCERPT">
````php
/**
 * 业务逻辑异常类
 */
class BusinessException extends Exception
{
    public static function fromErrorCode(string $errorCode, array $params = []): static
    {
        $errorConfig = config("errors.{$errorCode}");
        $message = $errorConfig['message'];
        return new static($message, $errorConfig['code']);
    }
}
````
</augment_code_snippet>

## 3. 线索模块架构设计

### 3.1 数据模型设计

基于已定义的数据表结构，线索模块包含以下核心实体：

```mermaid
erDiagram
    Lead {
        int id PK
        string company_full_name
        string company_short_name
        string internal_name
        int region
        int source
        int industry
        int status
        int stage
        string address
        int creator_id
        datetime last_followed_at
        text remark
    }
    
    LeadUserRelation {
        int id PK
        int lead_id FK
        int user_id FK
        int role_type
        int is_primary
    }
    
    Contact {
        int id PK
        string name
        enum gender
        int age
        string mobile
        string telephone
        string email
        string wx
        string department
        string position
        text remark
    }
    
    LeadContactRelation {
        int id PK
        int lead_id FK
        int contact_id FK
    }
    
    Lead ||--o{ LeadUserRelation : has
    Lead ||--o{ LeadContactRelation : has
    Contact ||--o{ LeadContactRelation : bound_to
```

### 3.2 完整分层架构实现

#### Model 层设计

<augment_code_snippet path="app/Models/Lead.php" mode="EXCERPT">
````php
/**
 * 线索模型
 */
class Lead extends Model
{
    use HasFactory, SoftDeletes;

    // 状态常量定义
    public const STATUS_LABELS = [
        1 => '待跟进',
        2 => '跟进中',
        3 => '已转化',
        4 => '无意向',
    ];

    // 关联关系定义
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'crm_lead_user_relation')
            ->withPivot(['role_type', 'is_primary']);
    }
}
````
</augment_code_snippet>

#### Repository 层设计

<augment_code_snippet path="app/Repositories/LeadRepositoryInterface.php" mode="EXCERPT">
````php
/**
 * 线索仓储接口
 */
interface LeadRepositoryInterface
{
    public function getLeadsList(LeadListDTO $dto): LengthAwarePaginator;
    public function findById(int $id);
    public function create(array $data);
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
    public function existsByCompanyName(string $companyName): bool;
    public function batchUpdateStatus(array $ids, int $status): int;
}
````
</augment_code_snippet>

#### Service 层设计

<augment_code_snippet path="app/Services/LeadService.php" mode="EXCERPT">
````php
/**
 * 线索业务服务类
 */
class LeadService
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository,
        private TransactionManagerInterface $transactionManager
    ) {}

    public function createLead(LeadCreateDTO $dto): Lead
    {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务规则验证
            if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
                throw BusinessException::fromErrorCode('Lead.company_already_exists');
            }

            return $this->leadRepository->create($dto->toArray());
        });
    }
}
````
</augment_code_snippet>

#### Controller 层设计

<augment_code_snippet path="app/Http/Controllers/LeadController.php" mode="EXCERPT">
````php
/**
 * 线索控制器
 */
class LeadController extends Controller
{
    public function __construct(private LeadService $leadService) {}

    public function store(CreateLeadRequest $request): JsonResponse
    {
        $dto = LeadCreateDTO::fromRequest($request);
        $lead = $this->leadService->createLead($dto);
        return ApiResponse::success(new LeadResource($lead), '创建线索成功');
    }
}
````
</augment_code_snippet>

### 3.3 DTO 层设计

#### 数据传输对象设计原则

- **不可变性**：使用 `readonly` 属性
- **类型安全**：明确定义所有属性类型
- **业务逻辑**：封装相关的数据处理逻辑
- **转换方法**：提供 `fromRequest()` 和 `toArray()` 方法

<augment_code_snippet path="app/DTOs/Lead/LeadCreateDTO.php" mode="EXCERPT">
````php
/**
 * 线索创建数据传输对象
 */
class LeadCreateDTO extends BaseDTO
{
    public readonly string $companyFullName;
    public readonly string $companyShortName;
    public readonly ?string $internalName;
    public readonly int $region;

    public static function fromRequest(CreateLeadRequest $request): self
    {
        return new self($request->validated());
    }

    public function toArray(): array
    {
        return [
            'company_full_name' => $this->companyFullName,
            'company_short_name' => $this->companyShortName,
            'region' => $this->region,
        ];
    }
}
````
</augment_code_snippet>

### 3.4 Request 验证层设计

#### 请求验证规则

```php
/**
 * 创建线索请求验证
 */
class CreateLeadRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'company_full_name' => 'required|string|max:255|unique:crm_lead,company_full_name',
            'company_short_name' => 'required|string|max:100',
            'internal_name' => 'nullable|string|max:100',
            'region' => 'required|integer|in:1,2,3,4,5,6,7',
            'source' => 'required|integer|in:1,2,3,4,5,6,7,8',
            'industry' => 'required|integer|in:1,2,3,4,5,6,7,8,9,10,11,12',
            'status' => 'nullable|integer|in:1,2,3,4',
            'stage' => 'nullable|integer|in:1,2,3,4,5,6',
            'address' => 'nullable|string|max:255',
            'creator_id' => 'required|integer|exists:users,id',
            'last_followed_at' => 'nullable|date',
            'remark' => 'nullable|string',
        ];
    }
}
```

### 3.5 Resource 响应层设计

#### API 资源格式化

```php
/**
 * 线索资源类
 */
class LeadResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'company_full_name' => $this->company_full_name,
            'company_short_name' => $this->company_short_name,
            'internal_name' => $this->internal_name,
            'region' => $this->region,
            'region_label' => $this->region_label,
            'status' => $this->status,
            'status_label' => $this->status_label,
            'creator' => new UserResource($this->whenLoaded('creator')),
            'users' => UserResource::collection($this->whenLoaded('users')),
            'contacts' => ContactResource::collection($this->whenLoaded('contacts')),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
```

## 4. 架构规范和最佳实践

### 4.1 SOLID 原则应用

#### 单一职责原则 (SRP)

```php
// ✅ 正确示例：职责分离
class LeadService
{
    // 只负责线索相关的业务逻辑
    public function createLead(LeadCreateDTO $dto): Lead { }
    public function updateLead(int $id, LeadUpdateDTO $dto): bool { }
}

class LeadRepository
{
    // 只负责线索数据访问
    public function create(array $data): Lead { }
    public function findById(int $id): ?Lead { }
}

// ❌ 错误示例：职责混合
class LeadManager
{
    public function createLead($data) { } // 业务逻辑
    public function sendNotification($lead) { } // 通知逻辑
    public function generateReport($leads) { } // 报表逻辑
}
```

#### 开闭原则 (OCP)

```php
// ✅ 正确示例：使用接口扩展
interface LeadRepositoryInterface
{
    public function create(array $data): Lead;
    public function findById(int $id): ?Lead;
}

class DatabaseLeadRepository implements LeadRepositoryInterface
{
    // 数据库实现
}

class CacheLeadRepository implements LeadRepositoryInterface
{
    // 缓存实现
}
```

#### 里氏替换原则 (LSP)

- 子类可以替换父类而不影响程序正确性
- 接口实现必须遵循契约

#### 接口隔离原则 (ISP)

```php
// ✅ 正确示例：细粒度接口
interface LeadReaderInterface
{
    public function findById(int $id): ?Lead;
    public function findByCompanyName(string $name): ?Lead;
}

interface LeadWriterInterface
{
    public function create(array $data): Lead;
    public function update(int $id, array $data): bool;
}

// ❌ 错误示例：臃肿接口
interface LeadRepositoryInterface
{
    public function create(array $data): Lead;
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
    public function findById(int $id): ?Lead;
    public function generateReport(): array; // 不相关的方法
    public function sendEmail(Lead $lead): bool; // 不相关的方法
}
```

#### 依赖倒置原则 (DIP)

<augment_code_snippet path="app/Services/LeadService.php" mode="EXCERPT">
````php
// ✅ 正确示例：依赖注入
class LeadService
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository,
        private TransactionManagerInterface $transactionManager
    ) {}
}
````
</augment_code_snippet>

### 4.2 异常处理策略

#### 异常层次设计

<augment_code_snippet path="app/Exceptions/BusinessException.php" mode="EXCERPT">
````php
/**
 * 业务异常基类
 */
class BusinessException extends Exception
{
    public static function fromErrorCode(string $errorCode, array $context = []): static
    {
        $config = config("errors.{$errorCode}");
        return new static($config['message'], $config['code']);
    }
}
````
</augment_code_snippet>

#### 配置化错误管理

<augment_code_snippet path="config/errors.php" mode="EXCERPT">
````php
return [
    'Lead' => [
        'not_found' => [
            'message' => '线索不存在',
            'code' => 404,
        ],
        'company_already_exists' => [
            'message' => '该公司已存在线索记录',
            'code' => 409,
        ],
    ],
];
````
</augment_code_snippet>

#### Service 层异常处理

```php
public function createLead(LeadCreateDTO $dto): Lead
{
    try {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务逻辑
            return $this->leadRepository->create($dto->toArray());
        });
    } catch (QueryException $e) {
        Log::error('线索创建数据库错误', [
            'error' => $e->getMessage(),
            'dto' => $dto->toArray()
        ]);
        throw BusinessException::fromErrorCode('Lead.create_failed');
    }
}
```

### 4.3 API 响应格式规范

#### 统一响应格式

<augment_code_snippet path="app/ApiResponses/ApiResponse.php" mode="EXCERPT">
````php
/**
 * 统一API响应格式封装
 */
class ApiResponse
{
    public static function success($data = null, string $message = 'success', int $code = 200): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    public static function error(string $message = 'error', int $code = 400, $data = null): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ], $code);
    }
}
````
</augment_code_snippet>

#### 成功响应示例

```json
{
    "code": 200,
    "message": "获取线索列表成功",
    "data": {
        "data": [
            {
                "id": 1,
                "company_full_name": "示例公司",
                "status": 1,
                "status_label": "待跟进"
            }
        ],
        "meta": {
            "current_page": 1,
            "total": 100
        }
    }
}
```

#### 错误响应示例

```json
{
    "code": 404,
    "message": "线索不存在",
    "errors": {
        "trace_id": "abc123"
    }
}
```

### 4.4 代码质量要求

#### 类型注解规范

- **必须**：所有方法参数和返回值必须有类型声明
- **PHPDoc**：所有 public 和 protected 方法必须有注释
- **复杂类型**：使用 `array{key: type}` 格式注解复杂数组

```php
/**
 * 创建新的线索
 *
 * @param LeadCreateDTO $dto 线索创建数据传输对象
 * @return Lead 创建的线索实例
 * @throws BusinessException 当业务规则验证失败时
 */
public function createLead(LeadCreateDTO $dto): Lead
{
    // ...
}

/**
 * @param array{
 *     company_full_name: string,
 *     company_short_name: string,
 *     region: int,
 *     status?: int|null
 * } $data 线索数据
 */
public function processLeadData(array $data): void
{
    // ...
}
```

#### 命名约定规范

- **类名**：PascalCase（`LeadService`）
- **方法名**：camelCase（`createLead`）
- **变量名**：camelCase（`$leadData`）
- **常量名**：UPPER_SNAKE_CASE（`STATUS_ACTIVE`）
- **接口名**：`{Entity}RepositoryInterface`

## 5. 架构验证和质量保证

### 5.1 静态分析工具配置

#### PHPStan Level 8 配置

<augment_code_snippet path="phpstan.neon" mode="EXCERPT">
````yaml
parameters:
    level: 8
    paths:
        - app/
        - config/
        - routes/

    # 启用严格规则
    checkMissingIterableValueType: true
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    checkUninitializedProperties: true
    checkDynamicProperties: true
````
</augment_code_snippet>

#### 代码格式检查

```bash
# 检查代码格式
make lint

# 自动修复格式问题
make lint-fix

# 运行静态分析
make analyze
```

### 5.2 测试策略

#### 测试覆盖率要求

- **核心业务逻辑**：覆盖率 >= 80%
- **关键路径**：覆盖率 >= 95%
- **边界条件**：必须测试边界情况

#### 测试类型

```php
/**
 * 线索服务测试
 */
class LeadServiceTest extends TestCase
{
    public function test_create_lead_with_valid_data(): void
    {
        $dto = new LeadCreateDTO([
            'company_full_name' => '测试公司',
            'region' => 1,
            'creator_id' => 1,
        ]);

        $lead = $this->leadService->createLead($dto);

        $this->assertInstanceOf(Lead::class, $lead);
        $this->assertEquals('测试公司', $lead->company_full_name);
    }

    public function test_create_lead_throws_exception_when_company_exists(): void
    {
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('该公司已存在线索记录');

        // 测试逻辑
    }
}
```

### 5.3 代码审查检查点

#### 架构合规检查

- [ ] 是否遵循分层架构
- [ ] 依赖方向是否正确
- [ ] 是否使用了接口抽象
- [ ] 职责边界是否清晰

#### 代码质量检查

- [ ] 类型注解是否完整
- [ ] PHPDoc 注释是否详细
- [ ] 命名是否符合约定
- [ ] 异常处理是否完善

#### 性能和安全检查

- [ ] 是否存在 N+1 查询问题
- [ ] 是否使用了适当的索引
- [ ] 输入验证是否完整
- [ ] 敏感信息是否正确处理

### 5.4 持续改进机制

#### 技术债务管理

- **识别**：定期审查代码异味和架构问题
- **优先级**：根据影响程度排优先级
- **跟踪**：使用任务文档跟踪改进进度

#### 架构演进

- **监控**：监控架构指标和性能数据
- **评估**：定期评估架构决策的有效性
- **改进**：基于反馈持续优化架构设计

## 总结

本 CRM API 项目采用了严格的分层架构设计，遵循 SOLID 原则，实现了高内聚、低耦合的代码结构。通过 Repository 模式、DTO 模式、统一异常处理和 API 响应格式，确保了代码的可维护性、可测试性和可扩展性。

项目已建立了完善的代码质量保证机制，包括 PHPStan Level 8 静态分析、Laravel Pint 代码格式化、完整的测试策略和代码审查流程。这些措施共同保证了项目的高质量交付和长期可维护性。

随着业务的发展，架构将持续演进和优化，始终保持技术先进性和业务适应性的平衡。

# API 日志中间件优化文档

## 概述
本文档详细说明了对 Laravel API 日志中间件的优化改进，包括安全性、性能、可维护性等多个方面的提升。

## 优化成果

### 1. 安全性增强

#### 敏感信息过滤
- **原有问题**: 只过滤 `password` 和 `password_confirmation` 字段
- **优化方案**: 扩展到 15+ 种敏感字段类型，支持递归过滤
- **配置位置**: `config/logging.php` 中的 `sensitive_fields` 配置

#### 用户代理字符串处理
- **原有问题**: 可能记录过长的用户代理字符串
- **优化方案**: 添加长度限制，默认 500 字符
- **配置项**: `LOG_MAX_USER_AGENT_LENGTH`

### 2. 功能完整性提升

#### 新增日志字段
```php
[
    'user_id' => 123,                    // 用户身份
    'memory_usage' => [                  // 内存使用情况
        'current_mb' => 25.6,
        'peak_mb' => 28.3
    ],
    'input_size' => 1024,                // 请求体大小
    'full_url' => 'https://...',         // 完整URL
    'timestamp' => '2024-01-01T12:00:00Z' // ISO格式时间戳
]
```

### 3. 配置灵活性

#### 路径排除配置
```php
'excluded_paths' => [
    '/health',
    '/ping',
    '/metrics',
    '/api/health',
]
```

#### 方法排除配置
```php
'excluded_methods' => [
    'OPTIONS',
]
```

#### 全局开关
```env
API_LOGGING_ENABLED=true
```

### 4. 性能优化

#### 异步日志记录
- **配置**: `API_LOGGING_ASYNC=true`
- **原理**: 使用 `register_shutdown_function` 在响应发送后记录日志
- **适用**: PHP-FPM 环境

#### 输入数据大小限制
- **默认限制**: 10KB
- **超限处理**: 记录元信息而非实际数据
- **配置项**: `LOG_MAX_INPUT_SIZE`

## 使用指南

### 基础配置

1. **环境变量设置**
```env
# 启用API日志
API_LOGGING_ENABLED=true

# 启用异步日志（生产环境推荐）
API_LOGGING_ASYNC=true

# 调整限制
LOG_MAX_USER_AGENT_LENGTH=500
LOG_MAX_INPUT_SIZE=10240
```

2. **排除不必要的路径**
```php
// config/logging.php
'excluded_paths' => [
    '/health',
    '/ping',
    '/metrics',
    '/api/health',
    '/api/status',
]
```

### 高级配置

#### 自定义敏感字段
```php
// config/logging.php
'sensitive_fields' => [
    'password',
    'secret_key',
    'private_token',
    // 添加业务特定的敏感字段
    'bank_account',
    'social_security',
]
```

#### 性能调优
```php
// 大流量场景下的配置建议
'max_input_size' => 5120,        // 减小到 5KB
'max_user_agent_length' => 200,  // 减小到 200 字符
```

## 监控建议

### 关键指标
1. **日志文件大小增长率**
2. **内存使用峰值**
3. **日志记录延迟**
4. **异常日志频率**

### 告警设置
- 日志文件大小超过阈值
- 内存使用异常
- 敏感信息泄露检测

## 最佳实践

### 生产环境
1. 启用异步日志记录
2. 设置合理的数据大小限制
3. 定期清理历史日志文件
4. 监控日志记录性能影响

### 开发环境
1. 保持同步日志记录便于调试
2. 记录更详细的调试信息
3. 定期检查敏感信息过滤效果

### 安全考虑
1. 定期审查敏感字段列表
2. 监控日志访问权限
3. 考虑日志加密存储
4. 实施日志保留策略

## 故障排除

### 常见问题
1. **日志记录失败**: 检查磁盘空间和权限
2. **性能影响**: 启用异步记录或调整限制
3. **敏感信息泄露**: 更新敏感字段配置
4. **内存使用过高**: 减小输入数据限制

### 调试技巧
1. 查看 Laravel 日志中的中间件错误
2. 监控应用响应时间变化
3. 使用性能分析工具检查影响

## 后续规划
1. 集成专业日志管理系统
2. 实现日志采样功能
3. 添加实时日志分析
4. 考虑分布式链路追踪集成

# 全局错误捕获机制文档

## 概述

本文档详细说明了 Laravel API 项目中实现的全局错误捕获机制，该机制能够捕获所有类型的 PHP 错误（包括 Fatal Error、Parse Error 等无法被 Exception 捕获的错误），确保 API 始终返回统一的 JSON 格式响应，并将错误信息记录到专门的日志文件中。

## 设计目标

1. **全面覆盖**: 捕获所有类型的 PHP 错误和异常
2. **统一响应**: 确保 API 始终返回 JSON 格式
3. **详细日志**: 记录完整的错误上下文信息
4. **安全保护**: 过滤敏感信息，区分开发/生产环境
5. **零业务影响**: 错误处理不影响正常业务流程

## 架构设计

### 核心组件

#### 1. GlobalErrorHandlerService
全局错误处理的核心服务，负责：
- 注册 PHP 错误处理器
- 处理各种类型的错误
- 生成统一的错误响应
- 记录详细的错误日志

#### 2. GlobalErrorHandlerMiddleware
错误处理中间件，负责：
- 在请求开始时注册错误处理器
- 设置错误上下文信息
- 在请求结束时清理上下文

#### 3. 增强的异常处理器
扩展 Laravel 的异常处理器，集成全局错误处理功能。

### 错误处理流程

```
请求开始
    ↓
注册错误处理器
    ↓
设置错误上下文
    ↓
执行业务逻辑
    ↓
发生错误/异常
    ↓
捕获并处理错误
    ↓
记录错误日志
    ↓
返回 JSON 响应
    ↓
清理上下文
```

## 支持的错误类型

### PHP 错误级别
- **E_ERROR**: 致命错误
- **E_WARNING**: 警告
- **E_PARSE**: 解析错误
- **E_NOTICE**: 通知
- **E_CORE_ERROR**: 核心错误
- **E_CORE_WARNING**: 核心警告
- **E_COMPILE_ERROR**: 编译错误
- **E_COMPILE_WARNING**: 编译警告
- **E_USER_ERROR**: 用户错误
- **E_USER_WARNING**: 用户警告
- **E_USER_NOTICE**: 用户通知
- **E_STRICT**: 严格模式
- **E_RECOVERABLE_ERROR**: 可恢复错误
- **E_DEPRECATED**: 废弃警告
- **E_USER_DEPRECATED**: 用户废弃警告

### 异常类型
- 所有未捕获的异常
- ErrorException（转换后的 PHP 错误）
- 自定义业务异常

## 日志记录

### 日志文件结构

#### 一般错误日志
- **文件路径**: `storage/logs/error-YYYY-MM-DD.log`
- **记录内容**: 警告、通知、一般错误
- **保留期限**: 30 天

#### 致命错误日志
- **文件路径**: `storage/logs/fatal-error-YYYY-MM-DD.log`
- **记录内容**: 致命错误、解析错误、核心错误
- **保留期限**: 90 天

### 日志格式

```json
{
    "type": "PHP_ERROR",
    "severity": "E_ERROR",
    "severity_code": 1,
    "message": "Call to undefined function nonExistentFunction()",
    "file": "/path/to/controller.php",
    "line": 25,
    "timestamp": "2024-01-01T12:00:00Z",
    "context": {
        "trace_id": "550e8400-e29b-41d4-a716-446655440000",
        "request_method": "GET",
        "request_uri": "/api/test/fatal-error",
        "request_url": "http://localhost:8000/api/test/fatal-error",
        "user_id": 123,
        "ip_address": "127.0.0.1",
        "user_agent": "Mozilla/5.0...",
        "session_id": "abc123",
        "request_data": {
            "param1": "value1",
            "password": "[FILTERED]"
        }
    },
    "memory_usage": 2097152,
    "peak_memory": 4194304
}
```

## API 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "result": "操作成功"
    }
}
```

### 错误响应（开发环境）
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "error_type": "PHP_ERROR",
        "severity": "E_ERROR",
        "severity_name": "E_ERROR",
        "message": "Call to undefined function nonExistentFunction()",
        "file": "/path/to/controller.php",
        "line": 25
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 错误响应（生产环境）
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 验证错误响应
```json
{
    "code": 422,
    "message": "数据验证失败",
    "errors": {
        "email": ["邮箱格式不正确"],
        "password": ["密码长度至少6位"]
    }
}
```

## 配置说明

### 日志通道配置
```php
// config/logging.php
'channels' => [
    'error' => [
        'driver' => 'daily',
        'path' => storage_path('logs/error.log'),
        'level' => 'error',
        'days' => 30,
    ],
    
    'fatal_error' => [
        'driver' => 'daily',
        'path' => storage_path('logs/fatal-error.log'),
        'level' => 'emergency',
        'days' => 90,
    ],
],
```

### 中间件注册
```php
// app/Http/Kernel.php
'api' => [
    \App\Http\Middleware\GlobalErrorHandlerMiddleware::class,
    \App\Http\Middleware\ApiLoggingMiddleware::class,
    // 其他中间件...
],
```

## 安全特性

### 敏感信息过滤
自动过滤以下敏感字段：
- password, password_confirmation
- token, api_key, secret
- authorization, x-api-key
- access_token, refresh_token
- private_key, credit_card
- card_number, cvv, ssn, id_card

### 环境区分
- **开发环境**: 返回详细错误信息，包含文件路径、行号、堆栈跟踪
- **生产环境**: 返回简化错误信息，隐藏敏感的技术细节

## 测试和验证

### API 测试端点

#### 获取测试列表
```bash
GET /api/test/errors/
```

#### 各种错误类型测试
```bash
GET /api/test/errors/normal           # 正常响应
GET /api/test/errors/fatal-error      # 致命错误
GET /api/test/errors/parse-error      # 解析错误
GET /api/test/errors/warning          # 警告
GET /api/test/errors/notice           # 通知
GET /api/test/errors/exception        # 异常
GET /api/test/errors/business-exception # 业务异常
GET /api/test/errors/memory-error     # 内存错误
GET /api/test/errors/division-by-zero # 除零错误
GET /api/test/errors/type-error       # 类型错误
GET /api/test/errors/array-error      # 数组访问错误
GET /api/test/errors/stack-overflow   # 栈溢出
```

### 命令行测试工具

#### 测试所有错误类型
```bash
php artisan error:test --all
```

#### 测试特定错误类型
```bash
php artisan error:test exception
php artisan error:test fatal-error
```

#### 指定测试 URL
```bash
php artisan error:test --url=http://localhost:8000 --all
```

#### 验证错误响应格式
```bash
# 验证所有错误响应是否使用新格式
php artisan error:validate-format

# 详细输出
php artisan error:validate-format --verbose

# 指定测试 URL
php artisan error:validate-format --url=http://localhost:8000 --verbose
```

### 测试结果示例
```
Starting error handling tests...
Base URL: http://localhost:8000

Testing: normal
  ✓ Test passed (HTTP 200)

Testing: warning
  ✓ Test passed (HTTP 200)

Testing: exception
  ✓ Test passed (HTTP 500)

Testing: fatal-error
  ✓ Test passed (HTTP 500)

Test Summary:
Total tests: 8
Passed: 8
Failed: 0
```

## 性能考虑

### 错误处理开销
- 错误处理逻辑经过优化，最小化性能影响
- 使用高效的日志记录机制
- 避免在错误处理过程中引入新的错误

### 内存管理
- 及时清理错误上下文，避免内存泄漏
- 合理控制日志数据大小
- 使用流式处理大量错误数据

## 监控和维护

### 错误监控指标
- 错误发生频率
- 错误类型分布
- 致命错误数量
- 响应时间影响

### 日志管理
- 自动日志文件轮转
- 磁盘空间监控
- 日志文件权限检查
- 定期清理过期日志

### 告警机制
- 致命错误立即告警
- 错误频率异常告警
- 日志系统故障告警

## 故障排除

### 常见问题

#### 1. 错误处理器未生效
**症状**: 错误仍然显示 PHP 默认格式
**解决**: 检查中间件是否正确注册，确认执行顺序

#### 2. 日志文件无法创建
**症状**: 错误信息未记录到日志文件
**解决**: 检查 storage/logs 目录权限，确保 Web 服务器有写入权限

#### 3. JSON 响应格式错误
**症状**: 返回的不是标准 JSON 格式
**解决**: 检查 ApiResponse 类是否正确引入，验证响应生成逻辑

#### 4. 敏感信息泄露
**症状**: 错误日志中包含密码等敏感信息
**解决**: 检查敏感字段过滤配置，确认过滤逻辑正确执行

### 调试技巧

1. **查看错误日志**: 检查 storage/logs 目录下的错误日志文件
2. **使用测试端点**: 通过测试 API 验证错误处理功能
3. **检查中间件执行**: 确认 GlobalErrorHandlerMiddleware 在正确位置
4. **验证配置**: 检查日志通道配置是否正确

## 扩展和定制

### 自定义错误类型处理
```php
// 在 GlobalErrorHandlerService 中添加
private static function handleCustomError($error): void
{
    // 自定义错误处理逻辑
}
```

### 集成外部监控服务
```php
// 发送错误到外部服务
private static function sendToExternalService($errorData): void
{
    // Sentry, Bugsnag, 等错误监控服务集成
}
```

### 自定义响应格式
```php
// 自定义 API 响应格式
private static function createCustomResponse($errorData): JsonResponse
{
    // 自定义响应格式逻辑
}
```

## 最佳实践

1. **定期检查错误日志**: 及时发现和解决系统问题
2. **监控错误趋势**: 分析错误模式，预防潜在问题
3. **保持日志清洁**: 定期清理过期日志，避免磁盘空间不足
4. **测试错误处理**: 定期运行错误测试，确保机制正常工作
5. **更新敏感字段列表**: 根据业务变化更新敏感信息过滤规则

通过这套全局错误捕获机制，可以确保 API 在任何错误情况下都能提供一致的用户体验，同时为开发者提供足够的调试信息。

<?php

/**
 * 测试 .augmentignore 文件效果的脚本
 * 检查哪些文件应该被忽略，哪些文件应该被包含
 */
echo "=== .augmentignore 文件效果测试 ===\n\n";

// 应该被忽略的文件和目录
$shouldBeIgnored = [
    'vendor/',
    'node_modules/',
    'storage/logs/',
    'storage/framework/cache/',
    'bootstrap/cache/',
    '.env',
    '.DS_Store',
    '.idea/',
    '.vscode/',
    'composer.lock',
    'phpstan-baseline.neon',
    '.phpunit.result.cache',
    'storage/phpstan/',
];

// 应该被包含的文件和目录
$shouldBeIncluded = [
    'app/',
    'config/',
    'database/',
    'routes/',
    'tests/',
    'docs/',
    'scripts/',
    'examples/',
    '.env.example',
    'composer.json',
    'phpunit.xml',
    'Makefile',
    'README.md',
];

echo "📋 应该被忽略的文件/目录:\n";
echo "================================\n";
foreach ($shouldBeIgnored as $path) {
    $exists = file_exists($path) ? '✅ 存在' : '❌ 不存在';
    $status = file_exists($path) ? '(应被忽略)' : '(无需忽略)';
    echo sprintf("%-30s %s %s\n", $path, $exists, $status);
}

echo "\n📋 应该被包含的文件/目录:\n";
echo "================================\n";
foreach ($shouldBeIncluded as $path) {
    $exists = file_exists($path) ? '✅ 存在' : '❌ 不存在';
    $status = file_exists($path) ? '(应被包含)' : '(缺失文件)';
    echo sprintf("%-30s %s %s\n", $path, $exists, $status);
}

echo "\n📊 统计信息:\n";
echo "================================\n";

// 统计各类文件数量
$vendorFiles = is_dir('vendor') ? count(scandir('vendor')) - 2 : 0;
$logFiles = is_dir('storage/logs') ? count(glob('storage/logs/*.log')) : 0;
$cacheFiles = is_dir('bootstrap/cache') ? count(scandir('bootstrap/cache')) - 2 : 0;

echo "vendor/ 目录文件数: $vendorFiles\n";
echo "日志文件数: $logFiles\n";
echo "启动缓存文件数: $cacheFiles\n";

// 检查 .augmentignore 文件本身
if (file_exists('.augmentignore')) {
    $content = file_get_contents('.augmentignore');
    $lines = count(explode("\n", $content));
    echo ".augmentignore 文件行数: $lines\n";
    echo "✅ .augmentignore 文件存在并已配置\n";
} else {
    echo "❌ .augmentignore 文件不存在\n";
}

echo "\n💡 建议:\n";
echo "================================\n";
echo "1. 确保 AI 助手不会分析 vendor/ 和 node_modules/ 目录\n";
echo "2. 日志文件和缓存文件应该被忽略\n";
echo "3. 环境配置文件 (.env) 应该被忽略以保护敏感信息\n";
echo "4. IDE 配置目录应该被忽略\n";
echo "5. 核心业务代码目录 (app/, config/, routes/ 等) 应该被包含\n";

echo "\n✅ 测试完成！\n";

<?php

/**
 * 验证事务上下文修复的简化测试脚本
 */

require_once __DIR__.'/../vendor/autoload.php';

use App\DTOs\Lead\LeadCreateDTO;
use App\Services\LeadService;
use Illuminate\Foundation\Application;

// 创建 Laravel 应用实例
$app = new Application(realpath(__DIR__.'/..'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// 启动应用
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$app->loadEnvironmentFrom('.env');
$app->bootstrapWith([
    \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
    \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
    \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
    \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
    \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
    \Illuminate\Foundation\Bootstrap\BootProviders::class,
]);

// 设置测试请求上下文
$traceId = 'verify-context-fix-'.time();
$request = \Illuminate\Http\Request::create('/api/leads', 'POST', [], [], [], [
    'HTTP_X_REQUEST_ID' => $traceId,
    'HTTP_CONTENT_TYPE' => 'application/json',
]);
$app->instance('request', $request);

echo "✅ 事务上下文修复验证\n";
echo "==========================================\n\n";

try {
    $leadService = $app->make(LeadService::class);

    // 创建测试数据
    $testData = [
        'company_full_name' => '验证修复公司'.time(),
        'company_short_name' => '验证修复',
        'internal_name' => '内部验证',
        'region' => 1,
        'source' => 1,
        'industry' => 1,
        'status' => 1,
        'stage' => 1,
        'creator_id' => 1,
        'address' => '验证地址',
        'remark' => '事务上下文修复验证',
    ];

    $dto = new LeadCreateDTO($testData);

    // 执行创建
    $lead = $leadService->createLead($dto);

    echo "✅ 线索创建成功\n";
    echo "📊 线索ID: {$lead->id}\n";
    echo "📊 公司名称: {$lead->company_full_name}\n";
    echo "📊 Trace ID: {$traceId}\n";

    // 检查日志
    $today = date('Y-m-d');
    $logFile = storage_path("logs/transaction-{$today}.log");

    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $traceCount = substr_count($logs, $traceId);
        $moduleCount = substr_count($logs, '"module":"Lead"');
        $actionCount = substr_count($logs, '"action":"create"');
        $businessDataCount = substr_count($logs, '"business_data"');

        echo "\n📋 日志验证结果:\n";
        echo "✅ 包含 trace_id 的日志条数: {$traceCount}\n";
        echo "✅ 包含 Lead 模块信息条数: {$moduleCount}\n";
        echo "✅ 包含 create 操作信息条数: {$actionCount}\n";
        echo "✅ 包含业务数据信息条数: {$businessDataCount}\n";

        if ($traceCount >= 2 && $moduleCount >= 2 && $actionCount >= 2 && $businessDataCount >= 1) {
            echo "\n🎉 事务上下文修复验证成功！\n";
            echo "✅ 事务开始和提交日志都包含了完整的业务上下文信息\n";
        } else {
            echo "\n⚠️  事务上下文可能仍有问题\n";
        }
    } else {
        echo "\n⚠️  事务日志文件不存在\n";
    }

} catch (Exception $e) {
    echo '❌ 验证失败: '.$e->getMessage()."\n";
    echo '📍 错误位置: '.$e->getFile().':'.$e->getLine()."\n";
}

<?php

/**
 * 事务日志功能测试脚本
 *
 * 用于验证事务日志回调机制是否正常工作
 */

require_once __DIR__.'/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// 创建 Laravel 应用实例
$app = new Application(realpath(__DIR__.'/../'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 模拟 HTTP 请求
$request = Request::create('/api/leads', 'POST', [
    'company_full_name' => '测试事务日志公司',
    'company_short_name' => '测试公司',
    'region' => 1,
    'source' => 1,
    'industry' => 1,
    'creator_id' => 1,
]);

$request->headers->set('X-Request-ID', 'test-transaction-log-'.time());
$request->headers->set('Content-Type', 'application/json');

echo "🚀 开始测试事务日志功能...\n\n";

try {
    // 测试1：验证服务注册
    echo "📋 测试1：验证服务注册\n";

    $transactionManager = $app->make(\App\Contracts\TransactionManagerInterface::class);
    echo "✅ TransactionManager 注册成功\n";

    $transactionLogService = $app->make(\App\Services\Transaction\TransactionLogService::class);
    echo "✅ TransactionLogService 注册成功\n";

    $callbackManager = $app->make(\App\Services\Transaction\TransactionCallbackManager::class);
    echo "✅ TransactionCallbackManager 注册成功\n";

    echo "\n";

    // 测试2：验证回调注册
    echo "📋 测试2：验证回调注册\n";

    $callbackManager->registerLeadCallbacks();
    echo "✅ 线索事务回调注册成功\n";

    echo "\n";

    // 测试3：测试事务成功场景
    echo "📋 测试3：测试事务成功场景\n";

    $result = $transactionManager->executeInTransaction(function () {
        echo "  🔄 执行模拟业务逻辑...\n";

        // 模拟业务逻辑
        usleep(100000); // 100ms

        return [
            'id' => 999,
            'company_name' => '测试事务日志公司',
            'status' => 'success',
        ];
    });

    echo '✅ 事务执行成功，结果：'.json_encode($result)."\n";
    echo "\n";

    // 测试4：测试事务失败场景
    echo "📋 测试4：测试事务失败场景\n";

    try {
        $transactionManager->executeInTransaction(function () {
            echo "  🔄 执行会失败的业务逻辑...\n";

            // 模拟业务逻辑
            usleep(50000); // 50ms

            throw new \App\Exceptions\BusinessException('模拟业务异常');
        });
    } catch (\Exception $e) {
        echo '✅ 事务回滚成功，异常：'.$e->getMessage()."\n";
    }

    echo "\n";

    // 测试5：检查日志文件
    echo "📋 测试5：检查日志文件\n";

    $today = date('Y-m-d');
    $transactionLogFile = storage_path("logs/transaction-{$today}.log");
    $businessLogFile = storage_path("logs/business-{$today}.log");

    if (file_exists($transactionLogFile)) {
        echo "✅ 事务日志文件存在：{$transactionLogFile}\n";

        $transactionLogs = file_get_contents($transactionLogFile);
        $logCount = substr_count($transactionLogs, 'test-transaction-log-');
        echo "📊 包含测试日志条数：{$logCount}\n";

        // 显示最后几行日志
        $lines = explode("\n", trim($transactionLogs));
        $lastLines = array_slice($lines, -3);
        echo "📝 最近的事务日志：\n";
        foreach ($lastLines as $line) {
            if (! empty($line)) {
                $logData = json_decode($line, true);
                if ($logData && isset($logData['message'])) {
                    echo "   - {$logData['timestamp']} [{$logData['level']}] {$logData['message']}\n";
                }
            }
        }
    } else {
        echo "⚠️  事务日志文件不存在：{$transactionLogFile}\n";
    }

    if (file_exists($businessLogFile)) {
        echo "✅ 业务日志文件存在：{$businessLogFile}\n";

        $businessLogs = file_get_contents($businessLogFile);
        $logCount = substr_count($businessLogs, 'test-transaction-log-');
        echo "📊 包含测试日志条数：{$logCount}\n";
    } else {
        echo "⚠️  业务日志文件不存在：{$businessLogFile}\n";
    }

    echo "\n";

    // 测试6：验证 trace_id 一致性
    echo "📋 测试6：验证 trace_id 一致性\n";

    $traceId = $request->header('X-Request-ID');
    echo "🔍 当前 trace_id：{$traceId}\n";

    if (file_exists($transactionLogFile)) {
        $traceCount = substr_count(file_get_contents($transactionLogFile), $traceId);
        echo "📊 事务日志中包含此 trace_id 的条数：{$traceCount}\n";
    }

    if (file_exists($businessLogFile)) {
        $traceCount = substr_count(file_get_contents($businessLogFile), $traceId);
        echo "📊 业务日志中包含此 trace_id 的条数：{$traceCount}\n";
    }

    echo "\n";

    echo "🎉 事务日志功能测试完成！\n\n";

    // 使用说明
    echo "📖 使用说明：\n";
    echo "1. 查看事务日志：tail -f storage/logs/transaction-{$today}.log\n";
    echo "2. 查看业务日志：tail -f storage/logs/business-{$today}.log\n";
    echo "3. 按 trace_id 查询：grep '{$traceId}' storage/logs/*.log\n";
    echo "4. 查询事务失败：grep 'rollback' storage/logs/transaction-*.log\n";
    echo "5. 查询性能问题：grep 'duration_ms' storage/logs/transaction-*.log | grep -E 'duration_ms\":[0-9]{4,}'\n";

} catch (\Exception $e) {
    echo '❌ 测试失败：'.$e->getMessage()."\n";
    echo '📍 错误位置：'.$e->getFile().':'.$e->getLine()."\n";
    echo "🔍 堆栈跟踪：\n".$e->getTraceAsString()."\n";
}

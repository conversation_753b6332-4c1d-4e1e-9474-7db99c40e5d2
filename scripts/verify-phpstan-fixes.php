<?php

/**
 * 验证 PHPStan 数组类型注解修复效果的脚本
 */
echo "=== PHPStan 数组类型注解修复验证 ===\n\n";

// 需要检查的文件和方法
$fixedMethods = [
    'app/Services/LeadService.php' => [
        'createLead',
        'updateLead',
        'batchUpdateStatus',
    ],
    'app/Services/LeadUserRelationService.php' => [
        'setLeadCollaborators',
    ],
    'app/Services/LeadContactRelationService.php' => [
        'createBatchLeadContactRelations',
        'syncLeadContacts',
        'deleteBatchRelations',
    ],
    'app/Services/ContactService.php' => [
        'getContactsList',
        'createBatchContacts',
    ],
];

echo "📋 检查已修复的方法类型注解:\n";
echo "================================\n";

foreach ($fixedMethods as $file => $methods) {
    echo "\n📁 文件: $file\n";

    if (! file_exists($file)) {
        echo "❌ 文件不存在\n";

        continue;
    }

    $content = file_get_contents($file);

    foreach ($methods as $method) {
        // 检查方法是否存在详细的类型注解
        $pattern = "/\/\*\*.*?@param\s+array[{<].*?\*\/.*?public\s+function\s+{$method}\s*\(/s";

        if (preg_match($pattern, $content)) {
            echo "  ✅ {$method}() - 已添加详细类型注解\n";
        } else {
            echo "  ❌ {$method}() - 缺少详细类型注解\n";
        }
    }
}

echo "\n📊 PHPStan 分析结果:\n";
echo "================================\n";

// 运行 PHPStan 分析
$output = [];
$returnCode = 0;
exec('cd '.__DIR__.'/.. && make analyze 2>&1', $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ PHPStan 分析通过，无错误\n";
} else {
    echo "❌ PHPStan 分析发现错误:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
}

echo "\n📈 Baseline 文件统计:\n";
echo "================================\n";

if (file_exists('phpstan-baseline.neon')) {
    $baselineContent = file_get_contents('phpstan-baseline.neon');

    // 统计总错误数
    $totalErrors = substr_count($baselineContent, 'message:');
    echo "总错误数: $totalErrors\n";

    // 检查是否还有我们修复的错误
    $fixedErrors = [
        'LeadService.*createLead.*array',
        'LeadService.*updateLead.*array',
        'LeadService.*batchUpdateStatus.*array',
        'LeadUserRelationService.*setLeadCollaborators.*array',
        'LeadContactRelationService.*createBatchLeadContactRelations.*array',
        'LeadContactRelationService.*syncLeadContacts.*array',
        'LeadContactRelationService.*deleteBatchRelations.*array',
    ];

    $remainingErrors = 0;
    foreach ($fixedErrors as $errorPattern) {
        if (preg_match("/$errorPattern/", $baselineContent)) {
            $remainingErrors++;
            echo "❌ 仍存在错误: $errorPattern\n";
        }
    }

    if ($remainingErrors === 0) {
        echo "✅ 所有目标错误已从 baseline 中移除\n";
    } else {
        echo "⚠️  还有 $remainingErrors 个错误未完全修复\n";
    }
} else {
    echo "❌ phpstan-baseline.neon 文件不存在\n";
}

echo "\n🔍 代码质量检查:\n";
echo "================================\n";

// 检查类型注解的质量
$qualityChecks = [
    'array{' => '使用了结构化数组类型',
    'array<' => '使用了泛型数组类型',
    '@param array ' => '使用了基础数组类型（需要改进）',
];

$totalFiles = 0;
$improvedFiles = 0;

foreach ($fixedMethods as $file => $methods) {
    if (! file_exists($file)) {
        continue;
    }

    $totalFiles++;
    $content = file_get_contents($file);
    $hasStructuredTypes = false;

    foreach ($qualityChecks as $pattern => $description) {
        $count = substr_count($content, $pattern);
        if ($count > 0 && ($pattern === 'array{' || $pattern === 'array<')) {
            $hasStructuredTypes = true;
        }
    }

    if ($hasStructuredTypes) {
        $improvedFiles++;
    }
}

echo "改进的文件数: $improvedFiles / $totalFiles\n";
echo '改进率: '.round(($improvedFiles / $totalFiles) * 100, 1)."%\n";

echo "\n💡 建议:\n";
echo "================================\n";
echo "1. 定期运行 PHPStan 分析确保代码质量\n";
echo "2. 为新增方法添加详细的类型注解\n";
echo "3. 考虑将类似修复应用到 Repository 层\n";
echo "4. 建立项目级别的类型注解规范\n";
echo "5. 在 CI/CD 流程中集成 PHPStan 检查\n";

echo "\n✅ 验证完成！\n";

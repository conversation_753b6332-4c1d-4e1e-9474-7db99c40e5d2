<?php

/**
 * 事务回滚测试脚本
 *
 * 使用方法: php artisan tinker < scripts/test-transaction-rollback.php
 */
echo "=== 事务回滚测试开始 ===\n";

// 测试场景配置
$testScenarios = [
    'runtime-exception' => '运行时异常测试',
    'business-exception' => '业务异常测试',
];

$testResults = [];

foreach ($testScenarios as $testType => $testName) {
    echo "\n--- 执行 {$testName} ---\n";

    try {
        // 创建测试 DTO
        $dto = new App\DTOs\Lead\LeadCreateDTO([
            'company_full_name' => "Test Company {$testType}",
            'company_short_name' => "Test {$testType}",
            'region' => 1,
            'source' => 1,
            'industry' => 1,
            'status' => 1,
            'stage' => 1,
            'creator_id' => 1,
        ]);

        // 设置测试头部
        request()->headers->set('X-Test-Transaction-Rollback', $testType);

        // 获取服务实例
        $leadService = app(App\Services\LeadService::class);

        // 执行测试
        $exceptionCaught = null;
        try {
            $result = $leadService->createLead($dto);
            echo "意外成功: {$result->id}\n";
        } catch (Exception $e) {
            $exceptionCaught = $e;
            echo '异常类型: '.get_class($e)."\n";
            echo '异常代码: '.$e->getCode()."\n";
            echo '异常消息: '.$e->getMessage()."\n";
        }

        // 验证数据库回滚
        $count = 0;
        try {
            $count = App\Models\Lead::where('company_full_name', "Test Company {$testType}")->count();
        } catch (Exception $e) {
            echo '数据库查询失败: '.$e->getMessage()."\n";
        }

        echo "数据库记录数: {$count} (应该为 0)\n";

        // 记录测试结果
        $testResults[$testName] = [
            'exception_caught' => $exceptionCaught !== null,
            'exception_type' => $exceptionCaught ? get_class($exceptionCaught) : null,
            'exception_code' => $exceptionCaught ? $exceptionCaught->getCode() : null,
            'rollback_success' => $count === 0,
            'status' => ($exceptionCaught !== null && $count === 0) ? 'PASSED' : 'FAILED',
        ];

        // 清理请求头部
        request()->headers->remove('X-Test-Transaction-Rollback');

    } catch (Exception $e) {
        echo '测试执行失败: '.$e->getMessage()."\n";
        $testResults[$testName] = [
            'status' => 'ERROR',
            'error' => $e->getMessage(),
        ];
    }
}

// 输出测试总结
echo "\n=== 测试结果总结 ===\n";
foreach ($testResults as $testName => $result) {
    echo "{$testName}: {$result['status']}\n";
    if (isset($result['exception_type'])) {
        echo "  - 异常类型: {$result['exception_type']}\n";
        echo "  - 异常代码: {$result['exception_code']}\n";
        echo '  - 回滚成功: '.($result['rollback_success'] ? '是' : '否')."\n";
    }
    if (isset($result['error'])) {
        echo "  - 错误: {$result['error']}\n";
    }
}

$passedCount = count(array_filter($testResults, fn ($r) => $r['status'] === 'PASSED'));
$totalCount = count($testResults);

echo "\n通过测试: {$passedCount}/{$totalCount}\n";
echo "=== 事务回滚测试结束 ===\n";

<?php

/**
 * 业务日志功能测试脚本
 *
 * 使用方法：php scripts/test-business-log.php
 */

require_once __DIR__.'/../vendor/autoload.php';

use App\Facades\BusinessLog;
use Illuminate\Support\Str;

// 启动 Laravel 应用
$app = require_once __DIR__.'/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

class BusinessLogTest
{
    private string $testTraceId;

    public function __construct()
    {
        $this->testTraceId = 'test-'.Str::uuid()->toString();
    }

    public function runAllTests(): void
    {
        echo "🧪 开始业务日志功能测试...\n\n";

        // 设置测试 trace_id
        BusinessLog::setTraceId($this->testTraceId);
        echo "📋 测试 Trace ID: {$this->testTraceId}\n\n";

        $this->testBasicLogging();
        $this->testLeadOperations();
        $this->testContactOperations();
        $this->testAuthOperations();
        $this->testSystemOperations();
        $this->testErrorLogging();
        $this->testSensitiveDataFiltering();
        $this->testTraceIdFunctionality();

        $this->showLogFiles();

        echo "\n✅ 所有测试完成！\n";
        echo '📁 请检查 storage/logs/business-'.date('Y-m-d').".log 文件查看日志输出\n";
    }

    private function testBasicLogging(): void
    {
        echo "🔍 测试基础日志记录功能...\n";

        // 测试基础 info 日志
        BusinessLog::info([
            'message' => '基础日志记录测试',
            'operation' => 'test_operation',
            'test_data' => 'hello world',
        ]);

        // 测试不同级别的日志
        BusinessLog::debug(['message' => 'Debug 级别测试', 'level_test' => 'debug']);
        BusinessLog::warning(['message' => 'Warning 级别测试', 'level_test' => 'warning']);
        BusinessLog::error(['message' => 'Error 级别测试', 'level_test' => 'error']);
        BusinessLog::critical(['message' => 'Critical 级别测试', 'level_test' => 'critical']);

        echo "   ✅ 基础日志记录测试完成\n\n";
    }

    private function testLeadOperations(): void
    {
        echo "🏢 测试线索操作日志...\n";

        // 测试线索列表查询
        BusinessLog::info([
            'message' => '查询线索列表',
            'module' => 'Lead',
            'action' => 'list',
            'filters' => ['status' => 1, 'region' => 2],
            'page' => 1,
            'per_page' => 15,
        ]);

        // 测试线索详情查看
        BusinessLog::info([
            'message' => '查看线索详情',
            'module' => 'Lead',
            'action' => 'view',
            'lead_id' => 123,
        ]);

        // 测试线索创建
        BusinessLog::info([
            'message' => '创建新线索',
            'module' => 'Lead',
            'action' => 'create',
            'data' => [
                'company_full_name' => '测试公司有限公司',
                'company_short_name' => '测试公司',
                'region' => 1,
                'source' => 2,
                'industry' => 3,
            ],
        ]);

        // 测试线索更新
        BusinessLog::info([
            'message' => '更新线索信息',
            'module' => 'Lead',
            'action' => 'update',
            'lead_id' => 123,
            'data' => [
                'company_short_name' => '测试公司（更新）',
                'status' => 2,
            ],
        ]);

        // 测试线索删除
        BusinessLog::info([
            'message' => '删除线索',
            'module' => 'Lead',
            'action' => 'delete',
            'lead_id' => 123,
        ]);

        // 测试批量操作
        BusinessLog::info([
            'message' => '批量更新线索状态',
            'module' => 'Lead',
            'action' => 'batch_update_status',
            'lead_ids' => [101, 102, 103],
            'new_status' => 2,
            'total_count' => 3,
        ]);

        echo "   ✅ 线索操作日志测试完成\n\n";
    }

    private function testContactOperations(): void
    {
        echo "👤 测试联系人操作日志...\n";

        BusinessLog::info([
            'message' => '创建联系人',
            'module' => 'contact',
            'action' => 'create',
            'data' => [
                'name' => '张三',
                'mobile' => '***********',
                'email' => '<EMAIL>',
                'position' => '总经理',
            ],
        ]);

        BusinessLog::info([
            'message' => '更新联系人信息',
            'module' => 'contact',
            'action' => 'update',
            'contact_id' => 456,
            'data' => [
                'position' => '副总经理',
                'department' => '销售部',
            ],
        ]);

        BusinessLog::info([
            'message' => '删除联系人',
            'module' => 'contact',
            'action' => 'delete',
            'contact_id' => 456,
        ]);

        echo "   ✅ 联系人操作日志测试完成\n\n";
    }

    private function testAuthOperations(): void
    {
        echo "🔐 测试认证操作日志...\n";

        BusinessLog::info([
            'message' => '用户登录',
            'module' => 'auth',
            'action' => 'login',
            'user_id' => 1,
            'ip' => '*************',
            'user_agent' => 'Mozilla/5.0 Test Browser',
        ]);

        BusinessLog::info([
            'message' => '用户登出',
            'module' => 'auth',
            'action' => 'logout',
            'user_id' => 1,
        ]);

        BusinessLog::info([
            'message' => '用户注册',
            'module' => 'auth',
            'action' => 'register',
            'data' => [
                'email' => '<EMAIL>',
                'name' => '新用户',
            ],
        ]);

        BusinessLog::info([
            'message' => '密码重置',
            'module' => 'auth',
            'action' => 'password_reset',
            'user_id' => 1,
            'method' => 'email',
        ]);

        echo "   ✅ 认证操作日志测试完成\n\n";
    }

    private function testSystemOperations(): void
    {
        echo "⚙️ 测试系统操作日志...\n";

        BusinessLog::info([
            'message' => '系统维护开始',
            'module' => 'system',
            'action' => 'maintenance_start',
            'duration' => '30 minutes',
            'reason' => 'database upgrade',
        ]);

        BusinessLog::info([
            'message' => '清理系统缓存',
            'module' => 'system',
            'action' => 'cache_clear',
            'type' => 'all',
            'size_freed' => '256MB',
        ]);

        BusinessLog::info([
            'message' => '更新系统配置',
            'module' => 'system',
            'action' => 'config_update',
            'key' => 'app.debug',
            'old_value' => true,
            'new_value' => false,
        ]);

        BusinessLog::info([
            'message' => '数据库备份完成',
            'module' => 'system',
            'action' => 'backup_complete',
            'type' => 'database',
            'size' => '1.2GB',
            'duration' => '5 minutes',
        ]);

        echo "   ✅ 系统操作日志测试完成\n\n";
    }

    private function testErrorLogging(): void
    {
        echo "❌ 测试错误日志记录...\n";

        // 模拟异常
        $exception = new Exception('测试异常信息', 500);

        BusinessLog::logException('业务处理失败', $exception, [
            'module' => 'Lead',
            'action' => 'processing',
            'lead_id' => 789,
            'additional_info' => '详细错误上下文',
        ]);

        // 不带异常的错误日志
        BusinessLog::error([
            'message' => '数据验证失败',
            'module' => 'validation',
            'validation_errors' => [
                'company_name' => '公司名称不能为空',
                'mobile' => '手机号格式不正确',
            ],
        ]);

        echo "   ✅ 错误日志记录测试完成\n\n";
    }

    private function testSensitiveDataFiltering(): void
    {
        echo "🔒 测试敏感数据过滤...\n";

        BusinessLog::info([
            'message' => '敏感数据过滤测试',
            'operation' => 'sensitive_data_test',
            'username' => 'testuser',
            'password' => 'secret123',
            'password_confirmation' => 'secret123',
            'api_token' => 'abc123def456',
            'secret_key' => 'super-secret',
            'authorization' => 'Bearer token123',
            'normal_data' => '这是正常数据',
            'nested' => [
                'password' => 'nested-secret',
                'public_info' => '公开信息',
            ],
        ]);

        echo "   ✅ 敏感数据过滤测试完成\n";
        echo "   📝 敏感字段应该被替换为 [REDACTED]\n\n";
    }

    private function testTraceIdFunctionality(): void
    {
        echo "🔗 测试 Trace ID 功能...\n";

        // 测试获取当前 trace_id
        $currentTraceId = BusinessLog::getTraceId();
        echo "   📋 当前 Trace ID: {$currentTraceId}\n";

        // 测试设置新的 trace_id
        $newTraceId = 'custom-trace-'.time();
        BusinessLog::setTraceId($newTraceId);

        BusinessLog::info([
            'message' => 'Trace ID 测试',
            'operation' => 'trace_test',
            'old_trace_id' => $currentTraceId,
            'new_trace_id' => $newTraceId,
        ]);

        // 验证 trace_id 是否正确设置
        $verifyTraceId = BusinessLog::getTraceId();
        if ($verifyTraceId === $newTraceId) {
            echo "   ✅ Trace ID 设置和获取功能正常\n";
        } else {
            echo "   ❌ Trace ID 功能异常\n";
        }

        // 恢复原始 trace_id
        BusinessLog::setTraceId($this->testTraceId);

        echo "\n";
    }

    private function showLogFiles(): void
    {
        echo "📁 业务日志文件信息...\n";

        $logPath = storage_path('logs');
        $todayLogFile = $logPath.'/business-'.date('Y-m-d').'.log';

        if (file_exists($todayLogFile)) {
            $fileSize = filesize($todayLogFile);
            $lineCount = count(file($todayLogFile));

            echo '   📄 今日日志文件: business-'.date('Y-m-d').".log\n";
            echo '   📊 文件大小: '.$this->formatBytes($fileSize)."\n";
            echo "   📝 日志条数: {$lineCount} 条\n";

            // 显示最后几行日志
            echo "\n   📋 最近的日志条目:\n";
            $lines = file($todayLogFile);
            $lastLines = array_slice($lines, -3);

            foreach ($lastLines as $line) {
                $logData = json_decode(trim($line), true);
                if ($logData) {
                    echo "   🕐 {$logData['timestamp']} - {$logData['message']}\n";
                }
            }
        } else {
            echo "   ⚠️  今日业务日志文件不存在\n";
        }

        // 列出所有业务日志文件
        $businessLogs = glob($logPath.'/business-*.log');
        if (! empty($businessLogs)) {
            echo "\n   📚 所有业务日志文件:\n";
            foreach ($businessLogs as $logFile) {
                $fileName = basename($logFile);
                $fileSize = filesize($logFile);
                echo "   📄 {$fileName} ({$this->formatBytes($fileSize)})\n";
            }
        }
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return round($bytes, 2).' '.$units[$unitIndex];
    }
}

// 运行测试
try {
    $test = new BusinessLogTest;
    $test->runAllTests();
} catch (Exception $e) {
    echo '❌ 测试脚本执行失败: '.$e->getMessage()."\n";
    echo '📍 文件: '.$e->getFile().':'.$e->getLine()."\n";
    exit(1);
}

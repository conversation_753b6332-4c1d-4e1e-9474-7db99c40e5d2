<?php

/**
 * 事务上下文调试测试脚本
 *
 * 用于验证 SimpleTransactionLogger 的事务上下文传递是否正常工作
 */

require_once __DIR__.'/../vendor/autoload.php';

use App\DTOs\Lead\LeadCreateDTO;
use App\Services\LeadService;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Log;

// 创建 Laravel 应用实例
$app = new Application(realpath(__DIR__.'/..'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// 启动应用
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$app->loadEnvironmentFrom('.env');
$app->bootstrapWith([
    \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
    \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
    \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
    \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
    \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
    \Illuminate\Foundation\Bootstrap\BootProviders::class,
]);

// 设置测试请求上下文
$request = \Illuminate\Http\Request::create('/api/leads', 'POST', [], [], [], [
    'HTTP_X_REQUEST_ID' => 'test-context-debug-'.time(),
    'HTTP_CONTENT_TYPE' => 'application/json',
]);
$app->instance('request', $request);

echo "🔍 事务上下文调试测试\n";
echo "==========================================\n\n";

try {
    // 测试1：验证服务注册
    echo "📋 测试1：验证服务注册\n";

    $leadService = $app->make(LeadService::class);
    echo "✅ LeadService 注册成功\n";

    echo "\n";

    // 测试2：创建测试数据
    echo "📋 测试2：创建测试数据\n";

    $testData = [
        'company_full_name' => '事务上下文测试公司'.time(),
        'company_short_name' => '上下文测试',
        'internal_name' => '内部测试',
        'region' => 1,
        'source' => 1,
        'industry' => 1,
        'status' => 1,
        'stage' => 1,
        'creator_id' => 1,
        'address' => '测试地址',
        'remark' => '事务上下文调试测试',
    ];

    $dto = new LeadCreateDTO($testData);
    echo "✅ 测试数据创建成功\n";
    echo "📊 公司名称: {$dto->companyFullName}\n";

    echo "\n";

    // 测试3：执行线索创建（成功场景）
    echo "📋 测试3：执行线索创建（成功场景）\n";

    // 清理之前的日志，便于观察
    Log::info('=== 开始事务上下文调试测试 ===', [
        'test_type' => 'context_debug',
        'trace_id' => $request->header('X-Request-ID'),
    ]);

    $lead = $leadService->createLead($dto);

    echo "✅ 线索创建成功，ID: {$lead->id}\n";
    echo "✅ 公司名称: {$lead->company_full_name}\n";
    echo '✅ 内存使用: '.round(memory_get_usage(true) / 1024 / 1024, 2)." MB\n";

    echo "\n";

    // 测试4：检查日志文件
    echo "📋 测试4：检查日志文件\n";

    $today = date('Y-m-d');
    $transactionLogFile = storage_path("logs/transaction-{$today}.log");
    $debugLogFile = storage_path("logs/laravel-{$today}.log");

    if (file_exists($transactionLogFile)) {
        echo "✅ 事务日志文件存在\n";

        $transactionLogs = file_get_contents($transactionLogFile);
        $testLogCount = substr_count($transactionLogs, 'test-context-debug-');
        echo "📊 包含测试日志条数：{$testLogCount}\n";

        // 检查上下文信息
        $contextCount = substr_count($transactionLogs, '"module":"Lead"');
        echo "📊 包含 Lead 模块信息条数：{$contextCount}\n";

        $actionCount = substr_count($transactionLogs, '"action":"create"');
        echo "📊 包含 create 操作信息条数：{$actionCount}\n";
    } else {
        echo "⚠️  事务日志文件不存在：{$transactionLogFile}\n";
    }

    if (file_exists($debugLogFile)) {
        echo "✅ 调试日志文件存在\n";

        $debugLogs = file_get_contents($debugLogFile);
        $contextSetCount = substr_count($debugLogs, 'setTransactionContext 调用');
        echo "📊 上下文设置调用次数：{$contextSetCount}\n";

        $contextBeginCount = substr_count($debugLogs, 'logTransactionBegin 调用');
        echo "📊 事务开始回调调用次数：{$contextBeginCount}\n";
    } else {
        echo "⚠️  调试日志文件不存在：{$debugLogFile}\n";
    }

    echo "\n";

    // 测试5：执行失败场景测试
    echo "📋 测试5：执行失败场景测试\n";

    try {
        // 创建重复的公司名称，触发约束违反
        $duplicateData = $testData;
        $duplicateData['company_full_name'] = $lead->company_full_name; // 使用已存在的公司名称
        $duplicateDto = new LeadCreateDTO($duplicateData);

        $leadService->createLead($duplicateDto);
        echo "❌ 预期的失败没有发生\n";
    } catch (Exception $e) {
        echo '✅ 预期的创建失败: '.$e->getMessage()."\n";
        echo '✅ 异常类型: '.get_class($e)."\n";
        echo '✅ 内存使用: '.round(memory_get_usage(true) / 1024 / 1024, 2)." MB\n";
    }

    echo "\n";

    // 测试6：再次检查日志文件
    echo "📋 测试6：检查失败场景日志\n";

    if (file_exists($transactionLogFile)) {
        $transactionLogs = file_get_contents($transactionLogFile);
        $rollbackCount = substr_count($transactionLogs, '事务回滚');
        echo "📊 事务回滚日志条数：{$rollbackCount}\n";

        $errorContextCount = substr_count($transactionLogs, 'error_context');
        echo "📊 包含错误上下文信息条数：{$errorContextCount}\n";
    }

    echo "\n";
    echo "🎉 事务上下文调试测试完成\n";

} catch (Exception $e) {
    echo '❌ 测试执行失败: '.$e->getMessage()."\n";
    echo '📍 错误位置: '.$e->getFile().':'.$e->getLine()."\n";
    echo "📋 错误堆栈:\n".$e->getTraceAsString()."\n";
}

<?php

/**
 * 测试通用事务日志方法的脚本
 */

require_once __DIR__.'/../vendor/autoload.php';

use App\DTOs\Lead\LeadCreateDTO;
use App\Services\LeadService;
use App\Services\Transaction\SimpleTransactionLogger;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Log;

// 创建 Laravel 应用实例
$app = new Application(realpath(__DIR__.'/..'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// 启动应用
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$app->loadEnvironmentFrom('.env');
$app->bootstrapWith([
    \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
    \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
    \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
    \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
    \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
    \Illuminate\Foundation\Bootstrap\BootProviders::class,
]);

// 设置测试请求上下文
$traceId = 'generic-logger-test-'.time();
$request = \Illuminate\Http\Request::create('/api/leads', 'POST', [], [], [], [
    'HTTP_X_REQUEST_ID' => $traceId,
    'HTTP_CONTENT_TYPE' => 'application/json',
]);
$app->instance('request', $request);

echo "🔧 通用事务日志方法测试\n";
echo "==========================================\n\n";

try {
    $leadService = $app->make(LeadService::class);

    // 测试1：使用重构后的 LeadService（应该使用通用方法）
    echo "📋 测试1：线索创建（使用通用方法）\n";

    $testData = [
        'company_full_name' => '通用方法测试公司'.time(),
        'company_short_name' => '通用测试',
        'internal_name' => '内部通用',
        'region' => 1,
        'source' => 1,
        'industry' => 1,
        'status' => 1,
        'stage' => 1,
        'creator_id' => 1,
        'address' => '通用测试地址',
        'remark' => '通用事务日志方法测试',
    ];

    $dto = new LeadCreateDTO($testData);
    $lead = $leadService->createLead($dto);

    echo "✅ 线索创建成功，ID: {$lead->id}\n";
    echo "✅ 公司名称: {$lead->company_full_name}\n";

    echo "\n";

    // 测试2：直接使用通用方法（模拟其他模块）
    echo "📋 测试2：模拟 Contact 模块使用通用方法\n";

    $contactData = [
        'name' => '测试联系人'.time(),
        'mobile' => '***********',
        'email' => '<EMAIL>',
        'department' => '技术部',
        'position' => '工程师',
    ];

    $transactionManager = $app->make(\App\Contracts\TransactionManagerInterface::class);

    $result = SimpleTransactionLogger::logBusinessTransaction(
        function () use ($contactData, $transactionManager) {
            return $transactionManager->executeInTransaction(function () use ($contactData) {
                // 模拟联系人创建逻辑（不实际执行数据库操作）
                Log::info('模拟联系人创建', $contactData);

                // 返回模拟的联系人对象
                return (object) [
                    'id' => rand(1000, 9999),
                    'name' => $contactData['name'],
                    'mobile' => $contactData['mobile'],
                ];
            });
        },
        'Contact',
        'create',
        $contactData,
        ['department' => $contactData['department']]
    );

    echo "✅ 模拟联系人创建成功，ID: {$result->id}\n";
    echo "✅ 联系人姓名: {$result->name}\n";

    echo "\n";

    // 测试3：模拟 User 模块更新操作
    echo "📋 测试3：模拟 User 模块更新操作\n";

    $userId = 123;
    $updateData = [
        'name' => '更新用户名'.time(),
        'email' => '<EMAIL>',
        'status' => 'active',
    ];

    $updateResult = SimpleTransactionLogger::logBusinessTransaction(
        function () use ($userId, $updateData, $transactionManager) {
            return $transactionManager->executeInTransaction(function () use ($userId, $updateData) {
                // 模拟用户更新逻辑
                Log::info('模拟用户更新', ['user_id' => $userId, 'data' => $updateData]);

                return true; // 模拟更新成功
            });
        },
        'User',
        'update',
        $updateData,
        ['user_id' => $userId]
    );

    echo '✅ 模拟用户更新成功: '.($updateResult ? 'true' : 'false')."\n";

    echo "\n";

    // 测试4：检查日志文件
    echo "📋 测试4：检查事务日志\n";

    $today = date('Y-m-d');
    $logFile = storage_path("logs/transaction-{$today}.log");

    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);

        // 统计不同模块的日志
        $leadModuleCount = substr_count($logs, '"module":"Lead"');
        $contactModuleCount = substr_count($logs, '"module":"Contact"');
        $userModuleCount = substr_count($logs, '"module":"User"');

        // 统计不同操作的日志
        $createActionCount = substr_count($logs, '"action":"create"');
        $updateActionCount = substr_count($logs, '"action":"update"');

        // 统计当前测试的日志
        $currentTestCount = substr_count($logs, $traceId);

        echo "📊 日志统计结果:\n";
        echo "✅ Lead 模块日志条数: {$leadModuleCount}\n";
        echo "✅ Contact 模块日志条数: {$contactModuleCount}\n";
        echo "✅ User 模块日志条数: {$userModuleCount}\n";
        echo "✅ Create 操作日志条数: {$createActionCount}\n";
        echo "✅ Update 操作日志条数: {$updateActionCount}\n";
        echo "✅ 当前测试日志条数: {$currentTestCount}\n";

        if ($leadModuleCount >= 2 && $contactModuleCount >= 2 && $userModuleCount >= 2) {
            echo "\n🎉 通用事务日志方法测试成功！\n";
            echo "✅ 支持多个业务模块的事务日志记录\n";
            echo "✅ 支持多种操作类型的日志记录\n";
        } else {
            echo "\n⚠️  通用方法可能存在问题\n";
        }
    } else {
        echo "⚠️  事务日志文件不存在\n";
    }

} catch (Exception $e) {
    echo '❌ 测试失败: '.$e->getMessage()."\n";
    echo '📍 错误位置: '.$e->getFile().':'.$e->getLine()."\n";
}

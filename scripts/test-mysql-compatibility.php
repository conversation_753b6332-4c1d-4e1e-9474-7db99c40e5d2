<?php

/**
 * MySQL 5.7.28 兼容性测试脚本
 *
 * 使用方法：php scripts/test-mysql-compatibility.php
 */

require_once __DIR__.'/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// 启动 Laravel 应用
$app = require_once __DIR__.'/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

class MySQLCompatibilityTest
{
    private array $results = [];

    public function runAllTests(): void
    {
        echo "🔍 开始 MySQL 5.7.28 兼容性测试...\n\n";

        $this->testDatabaseConnection();
        $this->testMySQLVersion();
        $this->testCharacterSetSupport();
        $this->testJSONSupport();
        $this->testIndexSupport();
        $this->testTableCreation();

        $this->printSummary();
    }

    private function testDatabaseConnection(): void
    {
        echo "📡 测试数据库连接...\n";

        try {
            $pdo = DB::connection()->getPdo();
            $this->addResult('数据库连接', true, '连接成功');
            echo "   ✅ 数据库连接正常\n";
        } catch (Exception $e) {
            $this->addResult('数据库连接', false, $e->getMessage());
            echo '   ❌ 数据库连接失败: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function testMySQLVersion(): void
    {
        echo "🔢 检查 MySQL 版本...\n";

        try {
            $version = DB::select('SELECT VERSION() as version')[0]->version;
            $versionNumber = $this->extractVersionNumber($version);

            if (version_compare($versionNumber, '5.7.28', '>=')) {
                $this->addResult('MySQL 版本', true, "版本 {$version} 符合要求");
                echo "   ✅ MySQL 版本: {$version} (符合 >= 5.7.28 要求)\n";
            } else {
                $this->addResult('MySQL 版本', false, "版本 {$version} 低于要求");
                echo "   ⚠️  MySQL 版本: {$version} (建议升级到 5.7.28+)\n";
            }
        } catch (Exception $e) {
            $this->addResult('MySQL 版本', false, $e->getMessage());
            echo '   ❌ 无法获取 MySQL 版本: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function testCharacterSetSupport(): void
    {
        echo "🔤 测试字符集支持...\n";

        try {
            $charsets = DB::select('SHOW VARIABLES LIKE "character_set%"');
            $utf8mb4Supported = false;

            foreach ($charsets as $charset) {
                if (strpos($charset->Value, 'utf8mb4') !== false) {
                    $utf8mb4Supported = true;
                    break;
                }
            }

            if ($utf8mb4Supported) {
                $this->addResult('UTF8MB4 字符集', true, '支持 utf8mb4 字符集');
                echo "   ✅ 支持 utf8mb4 字符集\n";
            } else {
                $this->addResult('UTF8MB4 字符集', false, '不支持 utf8mb4 字符集');
                echo "   ❌ 不支持 utf8mb4 字符集\n";
            }

            // 测试 emoji 存储
            $testEmoji = '🚀📱💻';
            DB::statement('CREATE TEMPORARY TABLE test_emoji (id INT, content TEXT CHARACTER SET utf8mb4)');
            DB::statement('INSERT INTO test_emoji (id, content) VALUES (1, ?)', [$testEmoji]);
            $result = DB::select('SELECT content FROM test_emoji WHERE id = 1')[0]->content;

            if ($result === $testEmoji) {
                $this->addResult('Emoji 存储', true, 'Emoji 字符存储正常');
                echo "   ✅ Emoji 字符存储测试通过\n";
            } else {
                $this->addResult('Emoji 存储', false, 'Emoji 字符存储异常');
                echo "   ❌ Emoji 字符存储测试失败\n";
            }

        } catch (Exception $e) {
            $this->addResult('字符集支持', false, $e->getMessage());
            echo '   ❌ 字符集测试失败: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function testJSONSupport(): void
    {
        echo "📄 测试 JSON 支持...\n";

        try {
            // 测试 JSON 函数
            $result = DB::select('SELECT JSON_OBJECT("key", "value") as json_test')[0]->json_test;
            $decoded = json_decode($result, true);

            if ($decoded && isset($decoded['key']) && $decoded['key'] === 'value') {
                $this->addResult('JSON 函数', true, 'JSON 函数正常工作');
                echo "   ✅ JSON 函数支持正常\n";
            } else {
                $this->addResult('JSON 函数', false, 'JSON 函数异常');
                echo "   ❌ JSON 函数支持异常\n";
            }

            // 测试 JSON 字段类型
            DB::statement('CREATE TEMPORARY TABLE test_json (id INT, data JSON)');
            $testData = json_encode(['name' => 'test', 'value' => 123]);
            DB::statement('INSERT INTO test_json (id, data) VALUES (1, ?)', [$testData]);
            $result = DB::select('SELECT data FROM test_json WHERE id = 1')[0]->data;

            if (json_decode($result, true)) {
                $this->addResult('JSON 字段', true, 'JSON 字段类型正常');
                echo "   ✅ JSON 字段类型支持正常\n";
            } else {
                $this->addResult('JSON 字段', false, 'JSON 字段类型异常');
                echo "   ❌ JSON 字段类型支持异常\n";
            }

        } catch (Exception $e) {
            $this->addResult('JSON 支持', false, $e->getMessage());
            echo '   ❌ JSON 支持测试失败: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function testIndexSupport(): void
    {
        echo "🔍 测试索引支持...\n";

        try {
            // 测试长索引支持
            DB::statement('CREATE TEMPORARY TABLE test_index (
                id INT PRIMARY KEY,
                long_text VARCHAR(255),
                INDEX idx_long_text (long_text(191))
            ) ENGINE=InnoDB');

            $this->addResult('长索引支持', true, '长索引创建成功');
            echo "   ✅ 长索引支持正常\n";

        } catch (Exception $e) {
            $this->addResult('长索引支持', false, $e->getMessage());
            echo '   ❌ 长索引支持测试失败: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function testTableCreation(): void
    {
        echo "🏗️  测试表创建...\n";

        try {
            // 模拟创建类似项目中的表结构
            DB::statement('CREATE TEMPORARY TABLE test_crm_lead (
                id INT UNSIGNED NOT NULL AUTO_INCREMENT,
                company_full_name VARCHAR(255) NOT NULL,
                company_short_name VARCHAR(100) NOT NULL,
                region TINYINT UNSIGNED NOT NULL DEFAULT 1,
                status TINYINT UNSIGNED NOT NULL DEFAULT 1,
                remark TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at DATETIME DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY uniq_company_full_name (company_full_name),
                INDEX idx_status (status),
                INDEX idx_deleted_at (deleted_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

            $this->addResult('表创建', true, 'CRM 表结构创建成功');
            echo "   ✅ CRM 表结构创建测试通过\n";

        } catch (Exception $e) {
            $this->addResult('表创建', false, $e->getMessage());
            echo '   ❌ 表创建测试失败: '.$e->getMessage()."\n";
        }

        echo "\n";
    }

    private function extractVersionNumber(string $version): string
    {
        preg_match('/(\d+\.\d+\.\d+)/', $version, $matches);

        return $matches[1] ?? '0.0.0';
    }

    private function addResult(string $test, bool $passed, string $message): void
    {
        $this->results[] = [
            'test' => $test,
            'passed' => $passed,
            'message' => $message,
        ];
    }

    private function printSummary(): void
    {
        echo "📊 测试结果汇总\n";
        echo str_repeat('=', 50)."\n";

        $passed = 0;
        $total = count($this->results);

        foreach ($this->results as $result) {
            $status = $result['passed'] ? '✅ 通过' : '❌ 失败';
            echo sprintf("%-20s %s - %s\n", $result['test'], $status, $result['message']);
            if ($result['passed']) {
                $passed++;
            }
        }

        echo str_repeat('=', 50)."\n";
        echo sprintf("总计: %d/%d 通过 (%.1f%%)\n", $passed, $total, ($passed / $total) * 100);

        if ($passed === $total) {
            echo "\n🎉 所有测试通过！MySQL 5.7.28 兼容性良好。\n";
        } else {
            echo "\n⚠️  部分测试失败，请检查 MySQL 配置或考虑升级版本。\n";
        }
    }
}

// 运行测试
try {
    $test = new MySQLCompatibilityTest;
    $test->runAllTests();
} catch (Exception $e) {
    echo '❌ 测试脚本执行失败: '.$e->getMessage()."\n";
    exit(1);
}

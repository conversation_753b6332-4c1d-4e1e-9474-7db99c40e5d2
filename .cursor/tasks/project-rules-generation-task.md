# Laravel CRM API 项目开发规则生成任务

## 任务概述

基于当前 Laravel CRM API 项目的架构特点、技术栈和编码规范，生成一套完整的项目开发规则，用于指导后续功能开发，确保代码质量和架构一致性。

## 当前状态

- ✅ 分析项目技术栈和架构模式
- ✅ 研究现有编码规范和开发流程
- ✅ 生成技术规范规则文档
- ✅ 生成架构设计规则文档
- ✅ 生成代码质量规则文档
- ✅ 生成开发流程规则文档
- ✅ 创建任务文档记录

## 具体实施步骤

### 1. 项目分析阶段

#### 1.1 技术栈分析
- **框架**: Laravel 10.x
- **PHP 版本**: PHP 8.2+
- **数据库**: MySQL 5.7.28+
- **缓存**: Redis 7.2.4+
- **静态分析**: PHPStan Level 8 + Larastan ^2.11
- **代码格式**: Laravel Pint (PSR-12)
- **测试**: PHPUnit ^10.1
- **监控**: Laravel Telescope

#### 1.2 架构模式分析
- **分层架构**: Controller → Request → DTO → Service → Repository → Model → Resource
- **设计原则**: SOLID 原则应用
- **Repository 模式**: 接口定义 + 依赖注入
- **DTO 模式**: 类型安全的数据传输
- **异常处理**: 业务异常 + 统一处理

#### 1.3 编码规范分析
- **命名约定**: PascalCase 类名，camelCase 方法名
- **类型注解**: 完整的 PHPDoc + PHP 类型声明
- **文档要求**: 中文文档，不包含作者时间信息
- **测试策略**: 核心业务逻辑覆盖率 >= 80%

### 2. 规则文档生成阶段

#### 2.1 技术规范规则 (.augment/rules/technical-standards.md)
- **技术栈要求**: PHP 8.2+, Laravel 10.x, MySQL 5.7.28+
- **开发工具**: PHPStan Level 8, Laravel Pint, PHPUnit
- **依赖管理**: Composer 包管理规则
- **环境配置**: 开发和生产环境要求
- **性能要求**: 内存限制、查询优化
- **安全要求**: 认证授权、数据验证
- **监控日志**: 应用监控、日志规范
- **API 设计**: RESTful 设计、响应格式
- **版本控制**: Git 规范、代码审查
- **部署要求**: 生产环境优化

#### 2.2 架构设计规则 (.augment/rules/architecture-design.md)
- **分层架构**: 标准分层结构和职责划分
- **SOLID 原则**: 五大原则的具体应用
- **Repository 模式**: 接口定义和实现规范
- **Service 层**: 业务逻辑封装和依赖注入
- **DTO 模式**: 设计原则和命名约定
- **异常处理**: 异常层次和处理策略
- **架构验证**: 依赖方向检查和职责边界

#### 2.3 代码质量规则 (.augment/rules/code-quality.md)
- **命名约定**: 类、方法、变量、常量命名规范
- **类型注解**: PHPDoc 注释和 PHP 类型声明
- **异常处理**: 业务异常和系统异常定义
- **测试要求**: 覆盖率、测试类型、命名规范
- **代码格式**: PSR-12 标准和额外规范

#### 2.4 开发流程规则 (.augment/rules/development-workflow.md)
- **任务管理**: 任务文档创建和分支策略
- **代码质量检查**: 静态分析和测试验证
- **文档管理**: 文档位置、命名和内容要求
- **性能安全**: 查询优化和安全检查
- **环境管理**: 开发和生产环境配置
- **错误处理**: 日志记录和异常处理
- **持续改进**: 代码审查和技术债务管理

### 3. 规则特点和优势

#### 3.1 规则特点
- **具体可执行**: 每个规则都有明确的操作指导
- **可验证性**: 提供检查命令和验证方法
- **示例对比**: 包含正确和错误的代码示例
- **场景说明**: 说明适用场景和例外情况
- **风格一致**: 与现有规则文件保持一致的格式

#### 3.2 规则优势
- **类型安全**: 通过 DTO 模式和 PHPStan Level 8 确保类型安全
- **架构一致**: 严格的分层架构和 SOLID 原则应用
- **代码质量**: 完整的命名约定和类型注解要求
- **开发效率**: 标准化的开发流程和工具集成
- **可维护性**: 清晰的职责划分和文档要求

## 预期效果

### 1. 代码质量提升
- 统一的编码规范和命名约定
- 完整的类型注解和静态分析
- 标准化的异常处理机制

### 2. 架构一致性
- 严格的分层架构实施
- SOLID 原则的具体应用
- Repository 和 DTO 模式的规范使用

### 3. 开发效率
- 标准化的开发流程
- 自动化的代码质量检查
- 完善的文档管理体系

### 4. 团队协作
- 统一的开发规范
- 清晰的任务管理流程
- 标准化的代码审查要点

## 验证结果

- ✅ 技术规范规则文档完整，涵盖技术栈、工具、环境等要求
- ✅ 架构设计规则文档详细，包含分层架构和设计原则应用
- ✅ 代码质量规则文档全面，覆盖命名、类型、异常、测试等方面
- ✅ 开发流程规则文档完善，包含任务管理、质量检查、文档管理等流程
- ✅ 所有规则文档格式统一，包含具体示例和验证方法
- ✅ 规则内容与项目实际架构和技术栈完全匹配
- ✅ 规则具有可执行性和可验证性，能够指导实际开发工作

## 后续建议

### 1. 规则应用
- 在后续功能开发中严格按照规则执行
- 定期审查代码是否符合规则要求
- 根据项目发展适时更新规则内容

### 2. 团队培训
- 组织团队学习新的开发规则
- 分享规则背后的设计理念和最佳实践
- 建立规则执行的监督和反馈机制

### 3. 工具集成
- 将规则检查集成到 CI/CD 流程
- 配置 IDE 实时检查规则合规性
- 建立自动化的规则验证机制

### 4. 持续改进
- 收集规则执行过程中的问题和建议
- 定期评估规则的有效性和适用性
- 根据技术发展和项目需求更新规则

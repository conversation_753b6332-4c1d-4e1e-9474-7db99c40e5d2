# 全局错误捕获机制实现任务

## 任务概述
为 Laravel API 项目实现全面的全局错误捕获机制，确保所有类型的错误（包括 Fatal Error、Parse Error 等无法被 Exception 捕获的错误）都能正常返回 JSON 格式响应，并记录到专门的错误日志文件中。

## 实现内容

### 1. 全局错误处理服务 ✅
- **GlobalErrorHandlerService**: 核心错误处理服务
- **错误类型覆盖**: 处理所有 PHP 错误级别
- **JSON 响应**: 统一的 API 响应格式
- **日志记录**: 按日期分割的错误日志文件

### 2. 错误处理中间件 ✅
- **GlobalErrorHandlerMiddleware**: 注册错误处理器
- **上下文设置**: 为错误提供请求上下文信息
- **敏感信息过滤**: 防止敏感数据泄露到错误日志
- **生命周期管理**: 请求开始注册，结束时清理

### 3. 异常处理器增强 ✅
- **Handler 类扩展**: 集成全局错误处理
- **ErrorException 处理**: 处理转换为异常的 PHP 错误
- **详细日志记录**: 包含完整的错误上下文
- **开发/生产环境区分**: 不同环境返回不同详细程度的信息

### 4. 日志配置增强 ✅
- **错误日志通道**: 专门的错误日志文件
- **致命错误通道**: 单独的致命错误日志
- **按日期分割**: 自动按日期创建日志文件
- **长期保留**: 致命错误日志保留 90 天

### 5. 测试和验证 ✅
- **ErrorTestController**: 各种错误类型的测试端点
- **TestErrorHandlingCommand**: 命令行测试工具
- **全面覆盖**: 测试所有主要错误类型
- **自动化验证**: 批量测试和结果验证

## 核心组件

### 服务类
- `GlobalErrorHandlerService`: 全局错误处理核心服务
- 支持的错误类型：
  - Fatal Error (E_ERROR)
  - Parse Error (E_PARSE)
  - Core Error (E_CORE_ERROR)
  - Compile Error (E_COMPILE_ERROR)
  - User Error (E_USER_ERROR)
  - Recoverable Error (E_RECOVERABLE_ERROR)
  - Warning (E_WARNING)
  - Notice (E_NOTICE)

### 中间件
- `GlobalErrorHandlerMiddleware`: 错误处理器注册和上下文管理

### 异常处理器
- `App\Exceptions\Handler`: 增强的异常处理器

### 测试工具
- `ErrorTestController`: Web 端错误测试
- `TestErrorHandlingCommand`: 命令行测试工具

## 错误处理流程

### 1. 注册阶段
```
请求开始 → GlobalErrorHandlerMiddleware → 注册错误处理器 → 设置上下文
```

### 2. 错误捕获
```
PHP 错误发生 → set_error_handler → GlobalErrorHandlerService::handleError
致命错误发生 → register_shutdown_function → GlobalErrorHandlerService::handleFatalError
异常发生 → set_exception_handler → GlobalErrorHandlerService::handleException
```

### 3. 响应处理
```
错误记录到日志 → 检查是否 API 请求 → 返回 JSON 响应 → 清理上下文
```

## 日志文件结构

### 错误日志文件
- **路径**: `storage/logs/error-YYYY-MM-DD.log`
- **内容**: 一般错误、警告、通知
- **保留期**: 30 天

### 致命错误日志文件
- **路径**: `storage/logs/fatal-error-YYYY-MM-DD.log`
- **内容**: 致命错误、解析错误等
- **保留期**: 90 天

### 日志格式
```json
{
    "type": "PHP_ERROR",
    "severity": "E_ERROR",
    "severity_code": 1,
    "message": "Call to undefined function",
    "file": "/path/to/file.php",
    "line": 123,
    "timestamp": "2024-01-01T12:00:00Z",
    "context": {
        "trace_id": "uuid",
        "request_method": "GET",
        "request_uri": "/api/test",
        "user_id": 123,
        "ip_address": "127.0.0.1"
    }
}
```

## API 响应格式

### 正常响应
```json
{
    "code": 200,
    "message": "success",
    "data": {...}
}
```

### 错误响应
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "data": {
        "error_type": "PHP_ERROR",
        "message": "详细错误信息",
        "file": "错误文件",
        "line": 123
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "uuid"
}
```

## 测试端点

### 错误测试 API
- `GET /api/test/errors/` - 获取可用测试列表
- `GET /api/test/errors/normal` - 正常响应测试
- `GET /api/test/errors/fatal-error` - 致命错误测试
- `GET /api/test/errors/exception` - 异常测试
- `GET /api/test/errors/warning` - 警告测试
- `GET /api/test/errors/notice` - 通知测试
- `GET /api/test/errors/business-exception` - 业务异常测试

### 命令行测试
```bash
# 测试所有错误类型
php artisan error:test --all

# 测试特定错误类型
php artisan error:test exception

# 指定测试 URL
php artisan error:test --url=http://localhost:8000 --all
```

## 配置说明

### 日志通道配置
```php
// config/logging.php
'error' => [
    'driver' => 'daily',
    'path' => storage_path('logs/error.log'),
    'level' => 'error',
    'days' => 30,
],

'fatal_error' => [
    'driver' => 'daily',
    'path' => storage_path('logs/fatal-error.log'),
    'level' => 'emergency',
    'days' => 90,
],
```

### 中间件注册
```php
// app/Http/Kernel.php
'api' => [
    \App\Http\Middleware\GlobalErrorHandlerMiddleware::class,
    \App\Http\Middleware\ApiLoggingMiddleware::class,
    // ...
],
```

## 安全特性

### 敏感信息过滤
- 自动过滤密码、令牌等敏感字段
- 递归过滤嵌套数组中的敏感数据
- 可配置的敏感字段列表

### 环境区分
- **开发环境**: 返回详细错误信息
- **生产环境**: 返回简化错误信息
- **调试模式**: 控制错误详细程度

## 性能考虑

### 错误处理开销
- 最小化错误处理逻辑
- 避免在错误处理中引入新的错误
- 使用备用日志记录机制

### 内存管理
- 及时清理错误上下文
- 避免内存泄漏
- 合理的日志文件大小控制

## 监控和告警

### 错误监控
- 错误频率监控
- 致命错误立即告警
- 错误趋势分析

### 日志管理
- 自动日志轮转
- 磁盘空间监控
- 日志文件权限检查

## 部署建议

### 生产环境
1. 确保日志目录有写入权限
2. 配置日志文件轮转
3. 设置错误监控告警
4. 定期检查错误日志

### 开发环境
1. 启用详细错误信息
2. 使用测试端点验证功能
3. 监控错误处理性能
4. 验证日志记录完整性

## 故障排除

### 常见问题
1. **日志文件无法创建**: 检查目录权限
2. **错误处理器未生效**: 检查中间件注册
3. **JSON 响应格式错误**: 检查 ApiResponse 类
4. **上下文信息缺失**: 检查中间件执行顺序

### 调试技巧
1. 查看错误日志文件
2. 使用测试端点验证
3. 检查 PHP 错误报告级别
4. 验证中间件执行流程

## 扩展和定制

### 自定义错误类型
- 添加新的错误处理逻辑
- 扩展错误上下文信息
- 自定义响应格式

### 集成外部服务
- 错误报告服务集成
- 监控系统集成
- 告警通知集成

## 总结

本全局错误捕获机制实现了：

1. **全面覆盖**: 捕获所有类型的 PHP 错误和异常
2. **统一响应**: 确保 API 始终返回 JSON 格式
3. **详细日志**: 记录完整的错误上下文信息
4. **安全保护**: 过滤敏感信息，区分环境
5. **易于测试**: 提供完整的测试工具和端点
6. **高性能**: 最小化错误处理开销
7. **可维护**: 清晰的代码结构和文档

通过这套机制，可以确保在任何错误情况下，API 都能提供一致的用户体验，同时为开发者提供足够的调试信息。

# 硬编码重构任务

## 任务概述

检查并重构业务逻辑中的硬编码问题，将数据表层面的映射定义在 Model 中，将纯业务编码定义在 config 文件中维护。

## 发现的硬编码问题

### 1. LeadService.php 中的硬编码

- **问题**：第119行硬编码的不可删除状态 `[3, 4]`
- **类型**：业务规则配置
- **解决方案**：移到配置文件 + Model 方法封装

### 2. Request 验证规则中的硬编码

- **问题**：各种长度限制和数量限制硬编码
- **类型**：业务配置
- **解决方案**：统一移到配置文件

## 重构方案

### 1. 创建业务配置文件

- 文件：`config/business.php`
- 内容：线索业务规则、字段限制、分页配置等

### 2. Model 层改进

- 在 `Lead` 模型中添加业务规则方法
- 提供静态方法封装业务逻辑判断

### 3. Request 层重构

- 所有 FormRequest 类使用配置文件中的值
- 错误消息动态生成

## 实施步骤

### ✅ 已完成

1. 创建 `config/business.php` 配置文件
2. 在 `Lead` 模型中添加 `getNonDeletableStatuses()` 和 `isDeletableStatus()` 方法
3. 重构 `LeadService::deleteLead()` 方法使用新的业务规则方法
4. 重构所有 FormRequest 类使用配置值：
    - `CreateLeadRequest`
    - `UpdateLeadRequest`
    - `BatchUpdateStatusRequest`
    - `LeadListRequest`
5. 更新错误消息使用动态配置值

### 配置项说明

#### 业务规则配置

```php
'Lead' => [
    'non_deletable_statuses' => [3, 4], // 不允许删除的状态
    'field_limits' => [...],             // 字段长度限制
    'batch_limits' => [...],             // 批量操作限制
]
```

#### 分页配置

```php
'pagination' => [
    'default_per_page' => 15,
    'max_per_page' => 100,
]
```

## 验证结果

### 配置文件加载测试

```bash
php artisan config:show business
```

✅ 配置正确加载

### Model 方法测试

```php
Lead::getNonDeletableStatuses()  // [3, 4]
Lead::isDeletableStatus(3)       // false
Lead::isDeletableStatus(1)       // true
```

✅ 方法正常工作

## 优势

### 1. 可维护性提升

- 所有业务规则集中在配置文件中
- 修改业务规则无需修改代码
- 配置与代码分离

### 2. 代码质量改善

- 消除魔法数字
- 提高代码可读性
- 便于单元测试

### 3. 扩展性增强

- 新增业务规则只需修改配置
- 支持环境特定配置
- 便于 A/B 测试

## 后续建议

1. **环境配置**：考虑为不同环境设置不同的业务规则
2. **缓存优化**：对频繁访问的配置项考虑缓存
3. **配置验证**：添加配置项的验证机制
4. **文档完善**：为配置项添加详细的注释说明

## 影响范围

- ✅ 向后兼容：API 行为完全不变
- ✅ 性能影响：微乎其微
- ✅ 测试覆盖：现有测试仍然有效

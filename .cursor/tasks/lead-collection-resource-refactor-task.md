# 线索列表分页数据格式化重构任务

## 任务概述

将 LeadController 中的分页数据格式化逻辑重构到 Resources 层，创建 LeadCollection 资源类来统一处理分页数据的格式化。

## 需求背景

原有的 LeadController::list 方法中包含了分页数据格式化的逻辑，违反了单一职责原则。需要将数据格式化逻辑抽离到 Resources
层，提高代码的可维护性和复用性。

## 实现方案

### 1. 创建 LeadCollection 资源类

- 文件路径：`app/Resources/LeadCollection.php`
- 继承自 `Illuminate\Http\Resources\Json\ResourceCollection`
- 封装分页数据格式化逻辑
- 保持原有数据结构不变

### 2. 重构 LeadController

- 在 LeadController 中引入 LeadCollection
- 简化 list 方法的实现
- 移除原有的手动分页数据格式化代码

## 技术实现

### LeadCollection 类设计

```php
<?php

namespace App\Resources;

use App\Resources\Lead\LeadResource;use Illuminate\Http\Request;use Illuminate\Http\Resources\Json\ResourceCollection;

class LeadCollection extends ResourceCollection
{
    public $collects = LeadResource::class;

    public function toArray(Request $request): array
    {
        return [
            'list' => $this->collection,
            'pagination' => $this->getPaginationData(),
        ];
    }

    protected function getPaginationData(): array
    {
        // 分页数据格式化逻辑
    }
}
```

### Controller 重构

```php
// 重构前
$data = [
    'list' => LeadResource::collection($leads->items()),
    'pagination' => [
        'current_page' => $leads->currentPage(),
        'page_size' => $leads->perPage(),
        'total' => $leads->total(),
        'last_page' => $leads->lastPage(),
        'from' => $leads->firstItem(),
        'to' => $leads->lastItem(),
        'has_more_pages' => $leads->hasMorePages(),
    ],
];

// 重构后
$data = new LeadCollection($leads);
```

## 完成标准

- [x] 创建 LeadCollection 资源类
- [x] 实现分页数据格式化逻辑
- [x] 重构 LeadController::list 方法
- [x] 保持原有数据结构和字段名不变
- [x] 遵循 Laravel Resource 最佳实践
- [x] 符合项目编码规范

## 验证方式

1. 检查 LeadCollection 类是否正确继承 ResourceCollection
2. 验证分页数据格式是否与原有格式一致
3. 确认 Controller 代码简化且功能正常
4. 运行相关测试确保功能正常

## 影响范围

- 新增文件：`app/Resources/LeadCollection.php`
- 修改文件：`app/Http/Controllers/LeadController.php`
- 影响接口：线索列表分页接口

## 备注

此重构遵循了 SOLID 原则中的单一职责原则，将数据格式化职责从 Controller 层分离到 Resources 层，提高了代码的可维护性和可测试性。

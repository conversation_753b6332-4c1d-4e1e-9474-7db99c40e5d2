# 线索更新接口异常处理修复任务

## 任务概述

修复线索更新接口（PUT /api/leads/{id}）的异常处理问题。当线索不存在时，系统错误地返回 500 状态码而不是 404 状态码。

## 问题分析

### 错误现象
- **接口**: PUT /api/leads/1111
- **期望行为**: 线索不存在时返回 404 状态码
- **实际行为**: 返回 500 状态码，抛出 TransactionException
- **错误消息**: "事务回滚失败：线索不存在"

### 调用链分析
1. `LeadController::update()` 调用 `LeadService::updateLead(1111, {...})`
2. `LeadService::updateLead()` 在 `TransactionManager::executeInTransaction()` 中执行
3. 业务逻辑检查发现线索ID 1111不存在，抛出 `BusinessException`（404状态码）
4. `TransactionManager` 捕获异常并错误地包装成 `TransactionException`（500状态码）
5. `Handler` 中的 `BusinessException` 处理器无法触发，走到兜底处理器

### 根本原因
`TransactionManager::executeInTransaction()` 方法在第137-144行的异常处理逻辑有问题：

```php
// 原始代码（有问题）
if ($e instanceof TransactionException) {
    throw $e;
}

throw TransactionException::rollbackError(
    $e->getMessage(),
    ['original_exception' => get_class($e), 'attempts' => $attempt]
);
```

这导致所有非 `TransactionException` 的异常（包括 `BusinessException`）都被包装成 `TransactionException`，破坏了异常传播链。

## 解决方案

### 修复内容

**文件**: `app/Services/Database/TransactionManager.php`

**1. 添加 BusinessException 导入**
```php
use App\Exceptions\BusinessException;
```

**2. 修改异常处理逻辑（第136-150行）**
```php
// 修复后的代码
if ($e instanceof TransactionException) {
    throw $e;
}

// 如果是业务异常，直接抛出，不包装成事务异常
// 这样业务异常能正确传播到 Handler 并返回正确的 HTTP 状态码
if ($e instanceof BusinessException) {
    throw $e;
}

throw TransactionException::rollbackError(
    $e->getMessage(),
    ['original_exception' => get_class($e), 'attempts' => $attempt]
);
```

### 设计原则

**异常分类处理**:
- **BusinessException**: 业务逻辑异常，应该直接传播到 Handler，返回对应的 HTTP 状态码
- **TransactionException**: 事务管理异常，直接传播
- **其他异常**: 系统异常（如数据库连接异常、死锁等），包装成 TransactionException

**异常传播链**:
```
Service Layer (BusinessException) 
    ↓ (直接传播)
TransactionManager (不包装)
    ↓ (直接传播)  
Handler (BusinessException 处理器)
    ↓
ApiResponse (正确的 HTTP 状态码)
```

## 验证结果

### 修复前
```bash
# Tinker 测试
TransactionException: 事务回滚失败：线索不存在 (Code: 1006)
```

### 修复后
```bash
# Tinker 测试
BusinessException: 线索不存在 (Code: 404)
```

### 验证要点
- ✅ BusinessException 正确传播，未被包装
- ✅ 返回正确的 404 状态码
- ✅ 错误消息保持不变："线索不存在"
- ✅ 其他异常处理逻辑不受影响

## 影响范围

### 受益场景
- 所有在事务中抛出 BusinessException 的业务逻辑
- 线索、联系人等实体的 CRUD 操作
- 需要返回特定 HTTP 状态码的业务异常

### 不受影响
- TransactionException 的处理逻辑保持不变
- 系统异常（数据库连接异常等）仍会被正确包装
- 死锁重试机制不受影响

## 技术要点

### 异常处理最佳实践
1. **业务异常不应被包装**: BusinessException 包含了正确的 HTTP 状态码，包装会丢失这些信息
2. **异常传播链要清晰**: 每一层都应该明确知道如何处理不同类型的异常
3. **统一异常处理**: 在 Handler 中统一处理异常响应格式

### 代码质量改进
- 增强了异常处理的精确性
- 提高了 API 响应的准确性
- 改善了前端错误处理体验

## 后续建议

1. **测试覆盖**: 为异常处理逻辑添加单元测试
2. **文档更新**: 更新异常处理相关的技术文档
3. **监控告警**: 监控 BusinessException 的发生频率，识别业务问题

# 验证规则配置策略重构任务

## 任务概述

将 FormRequest 类中的验证规则配置从 `config/business.php` 迁移回各自的 Request 类中进行维护，实现配置职责的清晰分离。

## 重构目标

- **Request 层职责明确**：验证规则直接在各自的 Request 类中定义
- **config/business.php 职责限定**：仅保留纯业务逻辑配置
- **保持业务规则方法不变**：Model 中的业务规则方法继续使用配置文件

## 重构前的问题

### 1. 职责混淆

- FormRequest 类从配置文件读取验证规则
- 配置文件包含了验证相关的配置项
- 验证逻辑分散在两个地方

### 2. 可读性问题

- 验证规则不直观，需要查看配置文件
- 增加了理解和维护的复杂度

## 重构方案

### 1. config/business.php 重构

**移除的配置项：**

- `field_limits` - 字段长度限制
- `batch_limits` - 批量操作限制
- `pagination` - 分页配置
- `validation` - 通用验证规则配置

**保留的配置项：**

- `non_deletable_statuses` - 业务规则：不可删除状态
- `features` - 功能开关配置
- `workflow` - 业务流程配置

### 2. FormRequest 类重构

将所有验证规则直接定义在各自的 Request 类中：

#### CreateLeadRequest

```php
// 重构前：从配置读取
'company_full_name' => 'required|string|max:' . $fieldLimits['company_full_name_max']

// 重构后：直接定义
'company_full_name' => 'required|string|max:255'
```

#### UpdateLeadRequest

```php
// 重构前：从配置读取
'company_short_name' => 'sometimes|required|string|max:' . $fieldLimits['company_short_name_max']

// 重构后：直接定义
'company_short_name' => 'sometimes|required|string|max:100'
```

#### BatchUpdateStatusRequest

```php
// 重构前：从配置读取
'ids' => 'required|array|min:1|max:' . $batchLimits['max_batch_size']

// 重构后：直接定义
'ids' => 'required|array|min:1|max:100'
```

#### LeadListRequest

```php
// 重构前：从配置读取
'per_page' => 'integer|min:1|max:' . $paginationConfig['max_per_page']

// 重构后：直接定义
'per_page' => 'integer|min:1|max:100'
```

## 实施步骤

### ✅ 已完成

1. **重构 config/business.php**
    - 移除所有验证相关配置项
    - 保留纯业务逻辑配置
    - 添加功能开关和业务流程配置

2. **重构 CreateLeadRequest**
    - 移除配置文件依赖
    - 直接定义验证规则
    - 更新错误消息

3. **重构 UpdateLeadRequest**
    - 移除配置文件依赖
    - 直接定义验证规则
    - 保持唯一性验证逻辑

4. **重构 BatchUpdateStatusRequest**
    - 移除配置文件依赖
    - 直接定义批量操作限制
    - 更新错误消息

5. **重构 LeadListRequest**
    - 移除配置文件依赖
    - 直接定义分页限制
    - 更新错误消息

## 重构后的配置结构

### config/business.php - 纯业务配置

```php
'Lead' => [
    'non_deletable_statuses' => [3, 4],     // 业务规则
    'features' => [...],                     // 功能开关
    'workflow' => [...],                     // 业务流程
],
'system' => [
    'features' => [...],                     // 系统功能开关
],
```

### FormRequest 类 - 验证规则内聚

```php
public function rules(): array
{
    return [
        'field' => 'required|string|max:255',  // 直接定义
        // ...
    ];
}
```

## 验证结果

### 配置文件验证

```bash
php artisan config:show business
```

✅ 只显示业务相关配置，无验证配置项

### 业务规则方法验证

```php
Lead::getNonDeletableStatuses()  // [3, 4]
Lead::isDeletableStatus(3)       // false
Lead::isDeletableStatus(1)       // true
```

✅ 业务规则方法正常工作

### 语法检查

✅ 所有文件无语法错误

## 重构优势

### 1. 职责清晰分离

- **Request 层**：专注输入验证，规则一目了然
- **Config 层**：专注业务规则，功能开关明确

### 2. 可读性提升

- 验证规则直接可见，无需查看配置文件
- 减少了代码跳转和理解成本

### 3. 维护性改善

- 验证规则修改直接在 Request 类中
- 业务规则修改在配置文件中
- 职责边界清晰

### 4. 性能优化

- 减少了配置文件读取
- 验证规则编译时确定

## 影响范围

- ✅ 向后兼容：API 行为完全不变
- ✅ 业务逻辑：业务规则方法保持不变
- ✅ 测试覆盖：现有测试仍然有效

## 后续建议

1. **文档更新**：更新开发文档，明确配置职责分工
2. **代码规范**：制定验证规则定义规范
3. **监控配置**：监控业务配置的使用情况

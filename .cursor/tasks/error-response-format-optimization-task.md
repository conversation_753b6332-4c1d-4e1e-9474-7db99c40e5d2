# 错误响应格式优化任务

## 任务概述
优化 API 错误响应格式，将原有的 `data` 字段改为 `errors` 字段，提供更语义化和标准化的错误响应结构。

## 优化目标

### 原有格式
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "data": {
        "error_details": "..."
    }
}
```

### 新格式
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "error_details": "..."
    }
}
```

## 实现内容

### 1. ApiResponse 类增强 ✅
- **新增方法**: `errorWithErrors()` - 使用 errors 字段的错误响应
- **新增方法**: `systemError()` - 专门用于系统级错误的响应
- **保持兼容**: 原有的 `error()` 方法保持不变，确保向后兼容
- **验证错误**: `validationError()` 方法已更新使用 errors 字段

### 2. 异常处理器优化 ✅
- **ErrorException 处理**: 使用新的 systemError 方法
- **ModelNotFoundException**: 更新为新格式
- **AuthenticationException**: 更新为新格式
- **AuthorizationException**: 更新为新格式
- **BusinessException**: 更新为新格式
- **通用异常处理**: 更新为新格式

### 3. 全局错误处理服务优化 ✅
- **致命错误响应**: 更新 sendFatalErrorResponse 方法
- **一般错误响应**: 更新 sendErrorResponse 方法
- **响应结构**: 统一使用 errors 字段
- **调试信息**: 开发环境下在 errors 中提供详细信息

### 4. 测试工具增强 ✅
- **格式验证**: 新增 ValidateErrorResponseFormatCommand 命令
- **测试更新**: 更新 TestErrorHandlingCommand 检查 errors 字段
- **全面验证**: 验证所有错误响应是否使用新格式

### 5. 文档更新 ✅
- **API 响应格式**: 更新所有错误响应示例
- **测试文档**: 添加格式验证命令说明
- **使用指南**: 更新开发者使用指南

## 新增的 API 方法

### ApiResponse::systemError()
```php
ApiResponse::systemError(
    string $message = '服务器内部错误',
    int $code = 500,
    array $errorDetails = [],
    ?string $traceId = null
): JsonResponse
```

**特性**:
- 支持 trace_id 追踪
- errors 字段为空时显示为空对象 `{}`
- 统一的系统错误响应格式

### ApiResponse::errorWithErrors()
```php
ApiResponse::errorWithErrors(
    string $message = 'error',
    int $code = 400,
    $errors = null
): JsonResponse
```

**特性**:
- 使用 errors 字段替代 data 字段
- errors 为 null 时显示为空对象 `{}`
- 保持简洁的错误响应结构

## 响应格式对比

### 成功响应（保持不变）
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "result": "操作成功"
    }
}
```

### 验证错误响应
```json
{
    "code": 422,
    "message": "数据验证失败",
    "errors": {
        "email": ["邮箱格式不正确"],
        "password": ["密码长度至少6位"]
    }
}
```

### 系统错误响应（开发环境）
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {
        "error_type": "PHP_ERROR",
        "severity": "E_ERROR",
        "severity_name": "E_ERROR",
        "message": "Call to undefined function",
        "file": "/path/to/file.php",
        "line": 123
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 系统错误响应（生产环境）
```json
{
    "code": 500,
    "message": "服务器内部错误",
    "errors": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 业务异常响应
```json
{
    "code": 400,
    "message": "用户名已存在",
    "errors": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "trace_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## 测试和验证

### 格式验证命令
```bash
# 验证所有错误响应格式
php artisan error:validate-format

# 详细输出
php artisan error:validate-format --verbose

# 指定测试 URL
php artisan error:validate-format --url=http://localhost:8000
```

### 验证结果示例
```
Validating error response format...
Base URL: http://localhost:8000
Expected format: {"code": 500, "message": "...", "errors": {...}}

Testing: exception
  ✓ Format valid

Testing: business-exception
  ✓ Format valid

Testing: fatal-error
  ✓ Format valid

Validation Summary:
Total tests: 5
Passed: 5
Failed: 0

🎉 All error responses use the correct format!

Format Features:
Responses with trace_id: 5/5
Responses with timestamp: 5/5
Errors as array: 2/5
Errors as object: 3/5
```

### 错误处理测试
```bash
# 测试所有错误类型
php artisan error:test --all

# 测试特定错误
php artisan error:test exception
```

## 向后兼容性

### 保持兼容的方法
- `ApiResponse::error()` - 继续使用 data 字段
- `ApiResponse::success()` - 成功响应格式不变
- `ApiResponse::paginated()` - 分页响应格式不变

### 推荐使用的新方法
- `ApiResponse::systemError()` - 系统错误
- `ApiResponse::errorWithErrors()` - 一般错误
- `ApiResponse::validationError()` - 验证错误（已更新）

## 迁移指南

### 对于新开发
- 使用 `ApiResponse::systemError()` 处理系统错误
- 使用 `ApiResponse::errorWithErrors()` 处理业务错误
- 使用 `ApiResponse::validationError()` 处理验证错误

### 对于现有代码
- 现有代码无需立即修改，保持向后兼容
- 建议逐步迁移到新的错误响应方法
- 可以通过格式验证命令检查响应格式

## 优势特点

### 1. 语义化
- `errors` 字段更明确地表示错误信息
- 与 `data` 字段形成清晰的语义区分

### 2. 标准化
- 符合 RESTful API 设计最佳实践
- 与主流 API 框架的错误响应格式一致

### 3. 结构化
- 统一的错误响应结构
- 支持复杂的错误信息组织

### 4. 可扩展
- 支持多种错误信息格式
- 便于添加新的错误字段

### 5. 调试友好
- 包含 trace_id 便于问题追踪
- 包含 timestamp 便于时间定位
- 开发环境提供详细错误信息

## 最佳实践

### 1. 错误信息组织
```json
{
    "errors": {
        "field_errors": {
            "email": ["格式错误"],
            "password": ["长度不足"]
        },
        "business_errors": ["用户已存在"],
        "system_errors": {
            "database": "连接失败"
        }
    }
}
```

### 2. 错误代码规范
- 400-499: 客户端错误
- 500-599: 服务器错误
- 具体错误码与 HTTP 状态码保持一致

### 3. 错误消息规范
- message: 用户友好的错误描述
- errors: 详细的错误信息和调试数据

## 监控和维护

### 1. 格式一致性检查
- 定期运行格式验证命令
- 在 CI/CD 中集成格式检查
- 监控生产环境的错误响应格式

### 2. 错误响应分析
- 统计不同错误类型的频率
- 分析错误响应的结构复杂度
- 优化常见错误的响应内容

## 总结

本次优化实现了：

1. **格式标准化**: 统一使用 errors 字段表示错误信息
2. **向后兼容**: 保持现有 API 的兼容性
3. **工具完善**: 提供格式验证和测试工具
4. **文档更新**: 完整的使用指南和示例
5. **最佳实践**: 提供错误响应的设计规范

通过这次优化，API 错误响应更加标准化和语义化，为前端开发和 API 集成提供了更好的体验。

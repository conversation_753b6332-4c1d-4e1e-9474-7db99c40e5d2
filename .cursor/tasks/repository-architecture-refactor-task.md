# Repository架构重构任务

## 任务概述
重构CRM线索功能模块的架构，解决Repository层缺少接口抽象和Service层与Model层职责边界模糊的问题。

## 核心问题

### 问题1：Repository层缺少接口抽象
- 当前数据访问层直接使用Eloquent Model，违反了依赖倒置原则
- 需要为每个Repository创建对应的接口（Contract），实现数据访问层的抽象
- 涉及的实体：Contact、LeadUserRelation、LeadContactRelation

### 问题2：Service层与Model层职责边界模糊
- Service层应专注于业务逻辑编排，不应直接操作数据库
- Model层应仅负责数据模型定义和关系映射，不应包含复杂业务逻辑
- 需要明确定义各层职责：Controller → Service → Repository → Model

## 重构方案

### 1. 创建Repository接口抽象层
- ContactRepositoryInterface
- LeadUserRelationRepositoryInterface  
- LeadContactRelationRepositoryInterface

### 2. 实现Repository具体类
- ContactRepository
- LeadUserRelationRepository
- LeadContactRelationRepository

### 3. 创建Service层业务逻辑
- ContactService
- LeadUserRelationService（如需要）

### 4. 更新ServiceProvider绑定
- 在RepositoryServiceProvider中注册新的接口与实现类的绑定关系

### 5. 重构现有代码依赖
- 检查并更新现有代码中直接使用Model的地方
- 改为通过Service和Repository层访问

## 实施步骤

1. 创建Repository接口抽象层
2. 实现Repository具体类
3. 创建Service层业务逻辑
4. 更新ServiceProvider绑定
5. 重构现有代码依赖

## 预期收益

- 提高代码的可测试性和可维护性
- 实现依赖倒置，降低层间耦合
- 明确各层职责边界
- 为后续功能扩展提供良好的架构基础

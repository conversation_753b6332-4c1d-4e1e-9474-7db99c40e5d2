# 常量配置重构任务

## 任务概述
将 `app/Support/Constants.php` 中的所有常量定义迁移到 `config/business.php` 配置文件中，并更新所有引用这些常量的文件。

## 重构目标
- 统一常量管理：所有业务常量集中在配置文件中
- 提高可维护性：配置与代码分离，便于环境特定配置
- 保持功能不变：确保所有引用正确更新

## 分析结果

### Constants.php 中的常量分类

#### 1. 系统级配置常量
- `DEFAULT_PAGE_SIZE = 20` - 默认分页大小
- `MAX_PAGE_SIZE = 100` - 最大分页大小
- `DEFAULT_SORT_BY = 'created_at'` - 默认排序字段
- `DEFAULT_SORT_DIRECTION = 'desc'` - 默认排序方向

#### 2. 通用业务常量
- `GENDER_MALE = 'male'` - 性别：男
- `GENDER_FEMALE = 'female'` - 性别：女
- `GENDER_UNKNOWN = 'unknown'` - 性别：未知

#### 3. 角色相关常量
- `ROLE_TYPE_OWNER = 1` - 角色类型：负责人
- `ROLE_TYPE_PARTICIPANT = 2` - 角色类型：参与人
- `ROLE_TYPE_OBSERVER = 3` - 角色类型：观察者
- `IS_PRIMARY_YES = 1` - 是主负责人
- `IS_PRIMARY_NO = 0` - 不是主负责人

### 引用文件分析
- **唯一引用文件**：`app/DTOs/LeadListDTO.php`
- **引用位置**：第75行，使用 `Constants::DEFAULT_PAGE_SIZE`

## 迁移策略

### 1. 配置文件结构设计
```php
// config/business.php
'system' => [
    'pagination' => [
        'default_page_size' => 20,
        'max_page_size' => 100,
    ],
    'sorting' => [
        'default_sort_by' => 'created_at',
        'default_sort_direction' => 'desc',
    ],
],
'common' => [
    'gender_options' => [
        'male' => '男',
        'female' => '女',
        'unknown' => '未知',
    ],
    'role_types' => [
        'owner' => 1,
        'participant' => 2,
        'observer' => 3,
    ],
    'primary_flags' => [
        'yes' => 1,
        'no' => 0,
    ],
],
```

### 2. 常量映射关系
| 原常量 | 新配置路径 |
|--------|------------|
| `Constants::DEFAULT_PAGE_SIZE` | `config('business.system.pagination.default_page_size')` |
| `Constants::MAX_PAGE_SIZE` | `config('business.system.pagination.max_page_size')` |
| `Constants::DEFAULT_SORT_BY` | `config('business.system.sorting.default_sort_by')` |
| `Constants::DEFAULT_SORT_DIRECTION` | `config('business.system.sorting.default_sort_direction')` |
| `Constants::GENDER_MALE` | `config('business.common.gender_options.male')` |
| `Constants::ROLE_TYPE_OWNER` | `config('business.common.role_types.owner')` |
| `Constants::IS_PRIMARY_YES` | `config('business.common.primary_flags.yes')` |

## 实施步骤

### ✅ 已完成
1. **分析 Constants.php 内容**
   - 识别所有常量定义
   - 按用途分类：系统级、通用业务、角色相关

2. **搜索引用文件**
   - 发现唯一引用：`app/DTOs/LeadListDTO.php`
   - 确认引用位置和使用方式

3. **迁移常量到配置文件**
   - 在 `config/business.php` 中添加系统级配置
   - 在 `config/business.php` 中添加通用业务配置
   - 保持配置结构清晰，添加详细注释

4. **更新引用文件**
   - 移除 `use App\Support\Constants` 导入
   - 将 `Constants::DEFAULT_PAGE_SIZE` 替换为 `config('business.system.pagination.default_page_size', 20)`
   - 更新排序默认值使用配置

5. **删除 Constants.php 文件**
   - 完全移除 `app/Support/Constants.php`

6. **验证和测试**
   - 语法检查：无错误
   - 配置加载测试：正常
   - 功能测试：LeadListDTO 正常工作

## 重构后的配置结构

### config/business.php 新增配置节点
```php
'system' => [
    'pagination' => [
        'default_page_size' => 20,
        'max_page_size' => 100,
    ],
    'sorting' => [
        'default_sort_by' => 'created_at',
        'default_sort_direction' => 'desc',
    ],
],
'common' => [
    'gender_options' => [
        'male' => '男',
        'female' => '女',
        'unknown' => '未知',
    ],
    'role_types' => [
        'owner' => 1,
        'participant' => 2,
        'observer' => 3,
    ],
    'primary_flags' => [
        'yes' => 1,
        'no' => 0,
    ],
],
```

### LeadListDTO.php 更新后的代码
```php
// 重构前
$this->pageSize = $data['page_size'] ?? Constants::DEFAULT_PAGE_SIZE;
$this->sortBy = $data['sort_by'] ?? 'created_at';
$this->sortDirection = $data['sort_direction'] ?? 'desc';

// 重构后
$this->pageSize = $data['page_size'] ?? config('business.system.pagination.default_page_size', 20);
$this->sortBy = $data['sort_by'] ?? config('business.system.sorting.default_sort_by', 'created_at');
$this->sortDirection = $data['sort_direction'] ?? config('business.system.sorting.default_sort_direction', 'desc');
```

## 验证结果

### 配置文件加载测试
```bash
php artisan config:show business
```
✅ 所有配置项正确加载

### 配置值访问测试
```php
config('business.system.pagination.default_page_size')  // 20
config('business.system.sorting.default_sort_by')       // 'created_at'
config('business.common.role_types.owner')              // 1
config('business.common.gender_options.male')           // '男'
```
✅ 配置值正确返回

### 功能测试
```php
$dto = new LeadListDTO();
$dto->pageSize      // 20
$dto->sortBy        // 'created_at'
$dto->sortDirection // 'desc'
```
✅ LeadListDTO 功能正常

## 重构优势

### 1. 统一配置管理
- 所有常量集中在配置文件中
- 便于环境特定配置
- 支持配置缓存优化

### 2. 提高可维护性
- 配置与代码分离
- 修改配置无需重新部署代码
- 支持运行时配置更新

### 3. 增强扩展性
- 支持多环境配置
- 便于 A/B 测试
- 支持动态配置

### 4. 改善代码质量
- 消除硬编码常量类
- 配置键名语义化
- 支持配置验证

## 影响范围
- ✅ 向后兼容：功能完全不变
- ✅ 性能影响：微乎其微（配置缓存）
- ✅ 测试覆盖：现有测试仍然有效

## 后续建议
1. **配置验证**：添加配置项的验证机制
2. **文档完善**：为配置项添加更详细的说明
3. **环境配置**：考虑为不同环境设置不同的默认值
4. **监控配置**：监控配置的使用情况和性能影响

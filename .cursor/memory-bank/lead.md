# 需求

开发线索功能模块

## 数据表

### ER 图

```mermaid
erDiagram

  crm_lead {
    INT id PK "主键"
    VARCHAR company_full_name "公司全称"
    VARCHAR company_short_name "公司简称"
    VARCHAR internal_name "内部统称"
    TINYINT region "所属区域"
    TINYINT source "线索来源"
    TINYINT industry "所属行业"
    TINYINT status "线索状态"
    TINYINT stage "线索阶段"
    VARCHAR address "详细地址"
    INT creator_id "创建人用户ID"
    DATETIME last_followed_at "最近跟进时间"
    TEXT remark "备注"
    DATETIME created_at
    DATETIME updated_at
    DATETIME deleted_at
  }

  crm_lead_user_relation {
    INT id PK "主键"
    INT lead_id FK "线索ID"
    INT user_id "用户ID"
    TINYINT role_type "角色类型"
    TINYINT is_primary "是否主负责人"
    DATETIME created_at
    DATETIME deleted_at
  }

  crm_contact {
    INT id PK "主键"
    VARCHAR name "姓名"
    ENUM gender "性别"
    TINYINT age "年龄"
    VARCHAR mobile "手机"
    VARCHAR telephone "座机"
    VARCHAR email "邮箱"
    VARCHAR wx "微信"
    VARCHAR department "所属部门"
    VARCHAR position "职位"
    TEXT remark
    DATETIME created_at
    DATETIME updated_at
    DATETIME deleted_at
  }

  crm_lead_contact_relation {
    INT id PK "主键"
    INT lead_id FK "线索ID"
    INT contact_id FK "联系人ID"
    DATETIME created_at
    DATETIME deleted_at
  }

  %% 关系定义
  crm_lead ||--o{ crm_lead_user_relation : has
  crm_lead ||--o{ crm_lead_contact_relation : has
  crm_contact ||--o{ crm_lead_contact_relation : bound_to
```

### mysql DDL

CREATE TABLE `crm_lead` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',

`company_full_name` VARCHAR(255) NOT NULL COMMENT '公司全称',
`company_short_name` VARCHAR(100) NOT NULL COMMENT '公司简称',
`internal_name` VARCHAR(100) DEFAULT NULL COMMENT '内部统称',

`region` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '所属区域（代码由配置维护）',
`source` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '线索来源（代码由配置维护）',
`industry` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '所属行业（代码由配置维护）',
`status` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '线索状态（代码由配置维护）',
`stage` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '线索阶段（代码由配置维护）',
`address` VARCHAR(255) DEFAULT NULL COMMENT '详细地址',

`creator_id` INT UNSIGNED NOT NULL COMMENT '创建人用户 ID',

`last_followed_at` DATETIME DEFAULT NULL COMMENT '最近跟进时间',
`remark` TEXT COMMENT '备注',

`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间',

PRIMARY KEY (`id`),
UNIQUE KEY `uniq_company_full_name` (`company_full_name`),
INDEX `idx_status` (`status`),
INDEX `idx_creator_id` (`creator_id`),
INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='销售线索表';

CREATE TABLE `crm_lead_user_relation` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',

`lead_id` INT UNSIGNED NOT NULL COMMENT '线索 ID（关联 crm_lead.id）',
`user_id` INT UNSIGNED NOT NULL COMMENT '用户 ID（关联用户表）',
`role_type` TINYINT UNSIGNED NOT NULL COMMENT '用户类型：1=负责人，2=协同人',
`is_primary` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主负责人（仅在负责人角色下有效）',

`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间（逻辑删除）',

PRIMARY KEY (`id`),

UNIQUE KEY `uniq_lead_user_role` (`lead_id`, `user_id`, `role_type`),

INDEX `idx_lead_id` (`lead_id`),
INDEX `idx_user_id` (`user_id`),
INDEX `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='线索-用户关联表（负责人/协同人）';

CREATE TABLE `crm_contact` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
`name` VARCHAR(100) NOT NULL COMMENT '姓名',
`gender` ENUM('男', '女') DEFAULT NULL COMMENT '性别',

    `age` TINYINT UNSIGNED DEFAULT NULL COMMENT '年龄',

    `mobile` VARCHAR(20) DEFAULT NULL COMMENT '手机',
    `telephone` VARCHAR(20) DEFAULT NULL COMMENT '座机',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `wx` VARCHAR(100) DEFAULT NULL COMMENT '微信号',

    `department` VARCHAR(100) DEFAULT NULL COMMENT '所属部门',
    `position` VARCHAR(100) DEFAULT NULL COMMENT '职位/头衔',

    `remark` TEXT DEFAULT NULL COMMENT '备注',

    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_mobile` (`mobile`) COMMENT '防止重复联系人'

) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='联系人主表';

CREATE TABLE `crm_lead_contact_relation` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
`lead_id` INT UNSIGNED NOT NULL COMMENT '线索 ID',
`contact_id` INT UNSIGNED NOT NULL COMMENT '联系人 ID',

    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `deleted_at` DATETIME DEFAULT NULL COMMENT '软删除时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_lead_contact` (`lead_id`, `contact_id`)

) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='线索与联系人关系表';

## 功能点

### 线索列表

-   搜素：线索状态、公司简称、手机号、我负责的、我协同的
-   支持分页查找

### 线索创建

### 查看单个线索

### 编辑线索

### 软删除线索

---
type: "always_apply"
---



# 开发哲学（philosophy）

- 核心理念：渐进式开发与演进式设计
- 先实现，再优化。让系统尽早可用，让设计顺势演进。
- 避免陷入完美主义陷阱，优先实现可上线、可验证的版本，在需求清晰与场景成熟后，再系统性地重构与优化。


1. 原则一：先让它工作，再让它优雅

阶段	行动
✅ 实现阶段	实现核心业务流程，确保接口/模块可用
🔧 优化阶段	重构代码结构、抽象逻辑、提升复用性
📈 提升阶段	引入设计模式、插件机制、架构解耦


⸻

2. 原则二：不预支未来的复杂性

避免为假设的扩展场景做过度设计。只在多个实现需求明确存在时再引入抽象点。

✅ 合理做法：使用内聚结构快速实现，必要时使用：

// TODO: 可抽象为策略模式
// TODO: 后续可迁移为配置


⸻

3. 原则三：每一步都能部署上线

每个任务提交都是可运行、可测试、可部署的最小单元：
	•	不引入无使用方的类/接口
	•	不产生无依赖的扩展点
	•	不提交残缺逻辑（如假数据、空 handler）

⸻

4. 原则四：逐步演进代码结构

初始写法                    优化方向
逻辑在 Controller	       提取至 Service 层
查询逻辑写死在 Model	    提取至 Repository 层
静态配置写死	            抽为 config 文件或字典映射
try/catch 无日志	        替换为业务异常 + 日志
手动 return json	        使用统一响应类 JsonResponse


⸻

5. 原则五：技术方案与业务节奏对齐
	•	🌀 业务频繁变动 → 使用简单内聚结构，快交付
	•	🌱 业务稳定沉淀 → 适当引入策略、聚合建模
	•	🚀 长线发展场景 → 分层设计、领域驱动、事件解耦

⸻

6. 原则六：重构是责任而非附加项
	•	每次提交主动审视：是否存在重复逻辑、潜在抽象点？
	•	重构任务形成子任务：.cursor/tasks/debt-*.md
	•	重构不是事后处理，而是持续迭代的组成部分

⸻

🌟 行动口号（建议打印贴在墙上）
	•	「写完不是结束，提测才算交付。」
	•	「能上线是胜利，可扩展是加分。」
	•	「业务需求为锚，代码质量为帆。」
	•	「一步走对，再无绕路。」


---
type: "always_apply"
---

# 架构设计规则

## 分层架构规范

### 1. 架构层次定义

#### 1.1 标准分层结构

```
┌─────────────────┐
│   Controller    │ ← HTTP 请求处理，路由分发
├─────────────────┤
│   Request       │ ← 输入验证，数据格式化
├─────────────────┤
│     DTO         │ ← 数据传输对象，类型安全
├─────────────────┤
│   Service       │ ← 业务逻辑处理，事务管理
├─────────────────┤
│  Repository     │ ← 数据访问抽象，查询封装
├─────────────────┤
│    Model        │ ← 数据模型，关系定义
├─────────────────┤
│   Resource      │ ← API 响应格式化
└─────────────────┘
```

#### 1.2 层次职责划分

**Controller 层**

- **职责**: 处理 HTTP 请求，调用 Service 层
- **禁止**: 直接访问 Repository 或 Model
- **禁止**: 包含业务逻辑

```php
// ✅ 正确示例
public function store(CreateLeadRequest $request): JsonResponse
{
    $dto = LeadCreateDTO::fromRequest($request);
    $lead = $this->leadService->createLead($dto);
    return ApiResponse::success(new LeadResource($lead));
}

// ❌ 错误示例
public function store(CreateLeadRequest $request): JsonResponse
{
    // 直接访问 Repository（违反分层）
    $lead = $this->leadRepository->create($request->validated());
    
    // 包含业务逻辑（职责不清）
    if ($lead->status === 1) {
        // 发送通知逻辑
    }
}
```

**Service 层**

- **职责**: 业务逻辑处理，协调多个 Repository
- **职责**: 事务管理，异常处理
- **禁止**: 直接处理 HTTP 请求

```php
// ✅ 正确示例
public function createLead(LeadCreateDTO $dto): Lead
{
    return $this->transactionManager->executeInTransaction(function () use ($dto) {
        // 业务规则验证
        $this->validateBusinessRules($dto);
        
        // 数据持久化
        $lead = $this->leadRepository->create($dto->toArray());
        
        // 关联数据处理
        $this->processRelatedData($lead, $dto);
        
        return $lead;
    });
}
```

**Repository 层**

- **职责**: 数据访问抽象，查询封装
- **职责**: 数据库操作优化
- **禁止**: 包含业务逻辑

### 2. SOLID 原则应用

#### 2.1 单一职责原则 (SRP)

- **要求**: 每个类只有一个变更的理由
- **应用**: 分离数据访问、业务逻辑、表现层

```php
// ✅ 正确示例：职责分离
class LeadService
{
    // 只负责线索相关的业务逻辑
    public function createLead(LeadCreateDTO $dto): Lead { }
    public function updateLead(int $id, LeadUpdateDTO $dto): bool { }
}

class LeadRepository
{
    // 只负责线索数据访问
    public function create(array $data): Lead { }
    public function findById(int $id): ?Lead { }
}

// ❌ 错误示例：职责混合
class LeadManager
{
    public function createLead($data) { } // 业务逻辑
    public function sendNotification($lead) { } // 通知逻辑
    public function generateReport($leads) { } // 报表逻辑
}
```

#### 2.2 开闭原则 (OCP)

- **要求**: 对扩展开放，对修改封闭
- **应用**: 使用接口和抽象类

```php
// ✅ 正确示例：使用接口扩展
interface LeadRepositoryInterface
{
    public function create(array $data): Lead;
    public function findById(int $id): ?Lead;
}

class DatabaseLeadRepository implements LeadRepositoryInterface
{
    // 数据库实现
}

class CacheLeadRepository implements LeadRepositoryInterface
{
    // 缓存实现
}
```

#### 2.3 里氏替换原则 (LSP)

- **要求**: 子类可以替换父类而不影响程序正确性
- **应用**: 接口实现必须遵循契约

#### 2.4 接口隔离原则 (ISP)

- **要求**: 依赖于抽象接口而非具体实现
- **应用**: 定义细粒度的接口

```php
// ✅ 正确示例：细粒度接口
interface LeadReaderInterface
{
    public function findById(int $id): ?Lead;
    public function findByCompanyName(string $name): ?Lead;
}

interface LeadWriterInterface
{
    public function create(array $data): Lead;
    public function update(int $id, array $data): bool;
}

// ❌ 错误示例：臃肿接口
interface LeadRepositoryInterface
{
    public function create(array $data): Lead;
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
    public function findById(int $id): ?Lead;
    public function generateReport(): array; // 不相关的方法
    public function sendEmail(Lead $lead): bool; // 不相关的方法
}
```

#### 2.5 依赖倒置原则 (DIP)

- **要求**: 高层模块不依赖低层模块，都依赖于抽象
- **应用**: 使用依赖注入

```php
// ✅ 正确示例：依赖注入
class LeadService
{
    public function __construct(
        private LeadRepositoryInterface $leadRepository,
        private TransactionManagerInterface $transactionManager
    ) {}
}

// 在 ServiceProvider 中绑定
$this->app->bind(LeadRepositoryInterface::class, LeadRepository::class);
```

### 3. Repository 模式规范

#### 3.1 接口定义

- **要求**: 所有 Repository 必须定义接口
- **命名**: 使用 `{Entity}RepositoryInterface` 格式

```php
// ✅ 正确示例
interface LeadRepositoryInterface
{
    public function create(array $data): Lead;
    public function findById(int $id): ?Lead;
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
    public function existsByCompanyName(string $companyName): bool;
}
```

#### 3.2 实现规范

- **命名**: 使用 `{Entity}Repository` 格式
- **继承**: 可以继承基础 Repository 类
- **查询优化**: 使用 Eloquent 最佳实践

```php
// ✅ 正确示例
class LeadRepository implements LeadRepositoryInterface
{
    public function create(array $data): Lead
    {
        return Lead::create($data);
    }
    
    public function findById(int $id): ?Lead
    {
        return Lead::with(['contacts', 'users'])->find($id);
    }
    
    public function existsByCompanyName(string $companyName): bool
    {
        return Lead::where('company_full_name', $companyName)->exists();
    }
}
```

### 4. Service 层规范

#### 4.1 业务逻辑封装

- **职责**: 处理复杂的业务规则
- **事务**: 使用事务管理器处理数据一致性
- **异常**: 抛出业务异常

```php
// ✅ 正确示例
class LeadService
{
    public function createLead(LeadCreateDTO $dto): Lead
    {
        return $this->transactionManager->executeInTransaction(function () use ($dto) {
            // 业务规则验证
            if ($this->leadRepository->existsByCompanyName($dto->companyFullName)) {
                throw BusinessException::fromErrorCode('Lead.company_already_exists');
            }
            
            // 数据持久化
            $lead = $this->leadRepository->create($dto->toArray());
            
            // 日志记录
            Log::info('线索创建成功', ['lead_id' => $lead->id]);
            
            return $lead;
        });
    }
}
```

#### 4.2 依赖注入

- **要求**: 通过构造函数注入依赖
- **类型**: 依赖接口而非具体实现

### 5. DTO 模式规范

#### 5.1 DTO 设计原则

- **不可变性**: 使用 `readonly` 属性
- **类型安全**: 明确定义所有属性类型
- **业务逻辑**: 封装相关的数据处理逻辑

```php
// ✅ 正确示例
class LeadCreateDTO extends BaseDTO
{
    public readonly string $companyFullName;
    public readonly string $companyShortName;
    public readonly ?string $internalName;
    public readonly int $region;
    
    public function __construct(array $data)
    {
        $this->companyFullName = $data['company_full_name'];
        $this->companyShortName = $data['company_short_name'];
        $this->internalName = $data['internal_name'] ?? null;
        $this->region = (int) $data['region'];
    }
    
    public static function fromRequest(CreateLeadRequest $request): static
    {
        return new static($request->validated());
    }
    
    public function toArray(): array
    {
        return [
            'company_full_name' => $this->companyFullName,
            'company_short_name' => $this->companyShortName,
            'internal_name' => $this->internalName,
            'region' => $this->region,
        ];
    }
}
```

#### 5.2 DTO 命名约定

- **创建**: `{Entity}CreateDTO`
- **更新**: `{Entity}UpdateDTO`
- **查询**: `{Entity}ListDTO`

### 6. 异常处理规范

#### 6.1 异常层次

- **业务异常**: 继承 BusinessException
- **系统异常**: 继承标准异常类
- **统一处理**: 在 Handler 中统一处理

```php
// ✅ 正确示例
class BusinessException extends Exception
{
    public static function fromErrorCode(string $errorCode, array $context = []): static
    {
        $config = config("business.errors.{$errorCode}");
        return new static($config['message'], $config['code']);
    }
}

// 使用示例
throw BusinessException::fromErrorCode('Lead.company_already_exists');
```

### 7. 架构验证规则

#### 7.1 依赖方向检查

- **禁止**: 下层依赖上层
- **禁止**: 跨层访问（如 Controller 直接访问 Repository）

#### 7.2 接口使用检查

- **要求**: Service 层必须依赖 Repository 接口
- **要求**: 在 ServiceProvider 中绑定接口实现

#### 7.3 职责边界检查

- **Controller**: 不包含业务逻辑
- **Service**: 不处理 HTTP 相关逻辑
- **Repository**: 不包含业务规则

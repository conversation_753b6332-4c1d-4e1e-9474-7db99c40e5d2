---
type: "always_apply"
---

# 技术规范规则

## 技术栈要求

### 1. 核心技术栈

#### 1.1 PHP 版本要求

- **要求**: PHP >= 8.2
- **原因**: 支持最新的类型系统、性能优化和安全特性
- **验证**: `php -v` 检查版本

#### 1.2 Laravel 框架版本

- **要求**: Laravel 10.x
- **原因**: 长期支持版本，稳定性和安全性保证
- **验证**: `php artisan --version` 检查版本

#### 1.3 数据库要求

- **要求**: MySQL >= 5.7.28 或 MySQL 8.0+
- **原因**: 支持 JSON 字段、CTE 和现代 SQL 特性
- **验证**: `SELECT VERSION();` 检查数据库版本

### 2. 开发工具要求

#### 2.1 静态分析工具

- **PHPStan**: 必须使用 Level 8 级别
- **Larastan**: Laravel 专用扩展，版本 ^2.11
- **配置文件**: `phpstan.neon` 必须包含严格检查规则

```yaml
# 正确示例
parameters:
    level: 8
    checkMissingIterableValueType: true
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
```

#### 2.2 代码格式化工具

- **Laravel Pint**: 必须使用 PSR-12 标准
- **配置**: 使用项目默认配置
- **验证**: `make lint` 检查格式

#### 2.3 测试框架

- **PHPUnit**: 版本 ^10.1
- **测试覆盖率**: 核心业务逻辑覆盖率 >= 80%
- **测试类型**: 单元测试 + 功能测试

### 3. 依赖管理规则

#### 3.1 Composer 依赖

- **生产依赖**: 仅包含运行时必需的包
- **开发依赖**: 开发、测试、分析工具放在 require-dev
- **版本约束**: 使用语义化版本约束 (^x.y)

```json
// 正确示例
{
    "require": {
        "php": "^8.2",
        "laravel/framework": "^10.10"
    },
    "require-dev": {
        "phpstan/phpstan": "^1.12",
        "nunomaduro/larastan": "^2.11"
    }
}
```

#### 3.2 包管理规则

- **禁止**: 直接编辑 composer.json 添加依赖
- **要求**: 使用 `composer require` 和 `composer require --dev`
- **锁定**: 提交 composer.lock 文件

### 4. 环境配置规则

#### 4.1 环境变量

- **配置文件**: 使用 .env 文件管理环境变量
- **默认值**: config 文件中提供合理默认值

```php
// 正确示例
'database' => [
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
]
```

#### 4.2 缓存配置

- **Redis**: 生产环境必须使用 Redis 作为缓存驱动
- **版本要求**: Redis >= 7.2.4
- **连接池**: 配置合适的连接池大小

### 5. 性能要求

#### 5.1 内存限制

- **PHPStan**: 使用 --memory-limit=512M
- **PHP**: 生产环境 memory_limit >= 256M
- **Composer**: 使用 --optimize-autoloader

#### 5.2 查询优化

- **N+1 查询**: 禁止出现 N+1 查询问题
- **索引**: 数据库查询必须有适当索引
- **批量操作**: 大量数据操作使用批量处理

### 6. 安全要求

#### 6.1 认证授权

- **认证**: 使用 Laravel Sanctum
- **授权**: 实现基于角色的访问控制
- **会话**: 生产环境使用安全的会话配置

#### 6.2 数据验证

- **输入验证**: 所有用户输入必须验证
- **SQL 注入**: 使用 Eloquent ORM 或参数化查询
- **XSS 防护**: 输出数据必须转义

### 7. 监控和日志

#### 7.1 应用监控

- **Laravel Telescope**: 开发环境启用
- **性能监控**: 监控响应时间和内存使用
- **错误追踪**: 记录所有异常和错误

#### 7.2 日志规范

- **日志级别**: 正确使用 debug、info、warning、error
- **结构化日志**: 使用结构化格式记录业务日志
- **日志轮转**: 配置日志文件轮转策略

```php
// 正确示例
Log::info('线索创建成功', [
    'lead_id' => $lead->id,
    'company_name' => $lead->company_full_name,
    'creator_id' => $lead->creator_id,
    'trace_id' => request()->header('X-Trace-ID'),
]);
```

### 8. API 设计规范

#### 8.1 RESTful 设计

- **HTTP 方法**: 正确使用 GET、POST、PUT、DELETE
- **状态码**: 返回适当的 HTTP 状态码
- **资源命名**: 使用复数名词命名资源

#### 8.2 响应格式

- **统一格式**: 使用 ApiResponse 类统一响应格式
- **错误处理**: 统一的错误响应格式
- **分页**: 列表接口支持分页

```php
// 正确示例
return ApiResponse::success(
    new LeadResource($lead),
    '创建线索成功',
    201
);
```

### 9. 版本控制规则

#### 9.2 代码审查

- **静态分析**: 提交前必须通过 PHPStan Level 8 检查
- **代码格式**: 提交前必须通过 Laravel Pint 检查
- **测试**: 相关测试必须通过

### 10. 部署要求

#### 10.1 生产环境

- **PHP**: 启用 OPcache
- **Composer**: 使用 --no-dev --optimize-autoloader
- **缓存**: 启用配置缓存和路由缓存

#### 10.2 环境检查

- **健康检查**: 实现应用健康检查接口
- **依赖检查**: 验证所有外部依赖可用
- **配置验证**: 验证关键配置项正确性

```bash
# 部署检查命令
make analyze          # 静态分析检查
make test            # 测试检查
make lint            # 代码格式检查
```

## 违规处理



### 2. 手动审查

- **代码审查**: 人工审查关键代码变更
- **架构审查**: 定期审查架构设计
- **性能审查**: 定期审查性能指标

### 3. 持续改进

- **规则更新**: 根据项目发展更新规则
- **工具升级**: 定期升级开发工具版本
- **最佳实践**: 总结和分享最佳实践
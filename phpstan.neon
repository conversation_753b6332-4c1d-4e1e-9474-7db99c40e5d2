includes:
    - ./vendor/nunomaduro/larastan/extension.neon
    - phpstan-baseline.neon

parameters:
    # 检查级别：从 0 到 9，级别越高越严格
    # 推荐从 5 开始，逐步提升到 8 或 max
    level: 8

    # 检查路径
    paths:
        - app/
        - config/
        - routes/
        - bootstrap

    # 排除检查的路径
    excludePaths:
        - app/Console/Kernel.php
        - app/Http/Kernel.php
        - app/Exceptions/Handler.php
        - storage/
        - vendor/
        - database/
        - tests/
        - scripts/
        - public/

    # 缓存目录
    tmpDir: storage/phpstan

    # 启用严格规则
    checkMissingIterableValueType: true

    # Laravel 特定配置
    noUnnecessaryCollectionCall: true
    noUnnecessaryCollectionCallOnly: []
    noUnnecessaryCollectionCallExcept: []

    # 忽略错误模式（可根据需要调整）
    ignoreErrors:
        # 忽略 Laravel 魔术方法相关的错误
        - '#Call to an undefined method [a-zA-Z0-9\\_]+::factory\(\)#'
        # 忽略测试文件中的一些动态调用
        - '#tests.*#'
        # 忽略测试命令中的类型问题
        - '#app/Console/Commands/TestResourceBugFixCommand\.php#'
        # 忽略泛型类型缺失警告
        -
            identifier: missingType.generics

    # 自定义规则
    reportUnmatchedIgnoredErrors: false

    # 检查函数调用
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueInstanceof: true
    checkAlwaysTrueStrictComparison: true
    checkExplicitMixedMissingReturn: true
    checkFunctionNameCase: true
    checkInternalClassCaseSensitivity: true

    # 未使用变量检查
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    checkUninitializedProperties: true
    checkMissingCallableSignature: true

    # 动态属性
    checkDynamicProperties: true

<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;
use Monolog\Processor\PsrLogMessageProcessor;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Deprecations Log Channel
    |--------------------------------------------------------------------------
    |
    | This option controls the log channel that should be used to log warnings
    | regarding deprecated PHP and library features. This allows you to get
    | your application ready for upcoming major versions of dependencies.
    |
    */

    'deprecations' => [
        'channel' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),
        'trace' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => env('LOG_LEVEL', 'critical'),
            'replace_placeholders' => true,
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => env('LOG_PAPERTRAIL_HANDLER', SyslogUdpHandler::class),
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
                'connectionString' => 'tls://'.env('PAPERTRAIL_URL').':'.env('PAPERTRAIL_PORT'),
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => env('LOG_LEVEL', 'debug'),
            'facility' => LOG_USER,
            'replace_placeholders' => true,
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'business' => [
            'driver' => 'daily',
            'path' => storage_path('logs/business.log'),
            'level' => env('BUSINESS_LOG_LEVEL', 'info'),
            'days' => env('BUSINESS_LOG_RETENTION_DAYS', 14),
            'replace_placeholders' => true,
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        'api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/api.log'),
            'level' => 'info',
            'days' => 30,
        ],
        'api_json' => [
            'driver' => 'daily',
            'path' => storage_path('logs/api_json.log'),
            'level' => 'info',
            'days' => 30,
            'formatter' => Monolog\Formatter\JsonFormatter::class,
        ],

        'error' => [
            'driver' => 'single',
            'path' => storage_path('logs/error.log'),
            'level' => 'error',
            'days' => 30,
        ],

        'fatal_error' => [
            'driver' => 'daily',
            'path' => storage_path('logs/fatal-error.log'),
            'level' => 'emergency',
            'days' => 90, // 致命错误保留更长时间
        ],
        'transaction' => [
            'driver' => 'daily',
            'path' => storage_path('logs/transaction.log'),
            'level' => env('TRANSACTION_LOG_LEVEL', 'info'),
            'days' => env('TRANSACTION_LOG_RETENTION_DAYS', 30),
            //            'tap' => [App\Logging\TransactionLogFormatter::class],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for API request logging middleware
    |
    */
    'api_logging' => [
        'enabled' => env('API_LOGGING_ENABLED', true),
        'async' => env('API_LOGGING_ASYNC', false),
        'excluded_paths' => [
            '/health',
            '/ping',
            '/metrics',
            '/api/health',
        ],
        'excluded_methods' => [
            // 'OPTIONS',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Sensitive Fields Configuration
    |--------------------------------------------------------------------------
    |
    | Fields that should be filtered from logs for security
    |
    */
    'sensitive_fields' => [
        'password',
        'password_confirmation',
        'token',
        'api_key',
        'secret',
        'authorization',
        'x-api-key',
        'access_token',
        'refresh_token',
        'private_key',
        'credit_card',
        'card_number',
        'cvv',
        'ssn',
        'id_card',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Limits
    |--------------------------------------------------------------------------
    |
    | Limits for log data to prevent performance issues
    |
    */
    'max_user_agent_length' => env('LOG_MAX_USER_AGENT_LENGTH', 500),
    'max_input_size' => env('LOG_MAX_INPUT_SIZE', 10240), // 10KB

    /*
    |--------------------------------------------------------------------------
    | Resilient Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for fault-tolerant logging mechanisms
    |
    */
    'resilient' => [
        'max_failures' => env('LOG_RESILIENT_MAX_FAILURES', 5),
        'circuit_breaker_timeout' => env('LOG_RESILIENT_CIRCUIT_TIMEOUT', 300), // 5分钟
        'log_timeout' => env('LOG_RESILIENT_TIMEOUT', 2), // 2秒
        'enable_backup' => env('LOG_RESILIENT_ENABLE_BACKUP', true),
        'enable_queue_fallback' => env('LOG_RESILIENT_ENABLE_QUEUE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Health Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for log system health monitoring
    |
    */
    'monitoring' => [
        'alert_cooldown' => env('LOG_MONITORING_ALERT_COOLDOWN', 1800), // 30分钟
        'thresholds' => [
            'disk_usage_percent' => env('LOG_MONITORING_DISK_THRESHOLD', 85),
            'log_file_size_mb' => env('LOG_MONITORING_FILE_SIZE_THRESHOLD', 100),
            'failure_rate_percent' => env('LOG_MONITORING_FAILURE_RATE_THRESHOLD', 10),
            'response_time_ms' => env('LOG_MONITORING_RESPONSE_TIME_THRESHOLD', 1000),
        ],
    ],

];

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 数据库优化配置
    |--------------------------------------------------------------------------
    |
    | 这里配置数据库操作优化组件的相关参数
    |
    */

    /**
     * 查询构建器配置
     */
    'query_builder' => [
        /**
         * 最大查询复杂度
         */
        'max_complexity' => env('DB_MAX_QUERY_COMPLEXITY', 100),

        /**
         * 支持的查询操作符
         */
        'supported_operators' => [
            '=', '!=', '<>', '>', '>=', '<', '<=',
            'like', 'not like', 'ilike', 'not ilike',
            'in', 'not in', 'between', 'not between',
            'null', 'not null', 'exists', 'not exists',
        ],

        /**
         * 查询复杂度权重
         */
        'complexity_weights' => [
            'where' => 1,
            'join' => 3,
            'subquery' => 5,
            'union' => 4,
            'group_by' => 2,
            'having' => 2,
            'order_by' => 1,
        ],

        /**
         * 查询缓存配置
         */
        'cache' => [
            'enabled' => env('DB_QUERY_CACHE_ENABLED', false),
            'default_ttl' => env('DB_QUERY_CACHE_TTL', 3600),
            'prefix' => env('DB_QUERY_CACHE_PREFIX', 'query_cache:'),
        ],

        /**
         * 查询优化配置
         */
        'optimization' => [
            'auto_optimize' => env('DB_AUTO_OPTIMIZE', true),
            'use_index_hints' => env('DB_USE_INDEX_HINTS', false),
            'optimize_select' => env('DB_OPTIMIZE_SELECT', true),
            'optimize_joins' => env('DB_OPTIMIZE_JOINS', true),
        ],
    ],

    /**
     * 事务管理器配置
     */
    'transaction_manager' => [
        /**
         * 默认事务超时时间（秒）
         */
        'default_timeout' => env('DB_TRANSACTION_TIMEOUT', 30),

        /**
         * 最大重试次数
         */
        'max_retries' => env('DB_TRANSACTION_MAX_RETRIES', 3),

        /**
         * 重试延迟时间（毫秒）
         */
        'retry_delay' => env('DB_TRANSACTION_RETRY_DELAY', 100),

        /**
         * 死锁检测关键词
         */
        'deadlock_keywords' => [
            'deadlock found',
            'lock wait timeout',
            'deadlock detected',
            'try restarting transaction',
        ],

        /**
         * 保存点配置
         */
        'savepoints' => [
            'max_age' => env('DB_SAVEPOINT_MAX_AGE', 300), // 5分钟
            'auto_cleanup' => env('DB_SAVEPOINT_AUTO_CLEANUP', true),
        ],

        /**
         * 事务历史配置
         */
        'history' => [
            'enabled' => env('DB_TRANSACTION_HISTORY_ENABLED', true),
            'max_size' => env('DB_TRANSACTION_HISTORY_MAX_SIZE', 1000),
        ],
    ],

    /**
     * 性能监控配置
     */
    'monitoring' => [
        /**
         * 是否启用监控
         */
        'enabled' => env('DB_MONITORING_ENABLED', true),

        /**
         * 慢查询阈值（毫秒）
         */
        'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 1000),

        /**
         * 是否收集查询统计信息
         */
        'collect_statistics' => env('DB_COLLECT_STATISTICS', true),

        /**
         * 是否记录所有查询
         */
        'log_all_queries' => env('DB_LOG_ALL_QUERIES', false),

        /**
         * 查询日志级别
         */
        'query_log_level' => env('DB_QUERY_LOG_LEVEL', 'debug'),

        /**
         * 性能分析配置
         */
        'profiling' => [
            'enabled' => env('DB_PROFILING_ENABLED', false),
            'sample_rate' => env('DB_PROFILING_SAMPLE_RATE', 0.1), // 10% 采样率
        ],
    ],

    /**
     * 连接池配置
     */
    'connection_pool' => [
        /**
         * 是否启用连接池管理
         */
        'enabled' => env('DB_CONNECTION_POOL_ENABLED', false),

        /**
         * 连接健康检查间隔（秒）
         */
        'health_check_interval' => env('DB_HEALTH_CHECK_INTERVAL', 60),

        /**
         * 最大空闲连接数
         */
        'max_idle_connections' => env('DB_MAX_IDLE_CONNECTIONS', 10),

        /**
         * 连接超时时间（秒）
         */
        'connection_timeout' => env('DB_CONNECTION_TIMEOUT', 5),
    ],

    /**
     * 查询分析配置
     */
    'analysis' => [
        /**
         * 是否启用查询分析
         */
        'enabled' => env('DB_ANALYSIS_ENABLED', false),

        /**
         * 执行计划分析
         */
        'explain_queries' => env('DB_EXPLAIN_QUERIES', false),

        /**
         * 索引建议
         */
        'index_suggestions' => env('DB_INDEX_SUGGESTIONS', true),

        /**
         * 查询优化建议
         */
        'optimization_suggestions' => env('DB_OPTIMIZATION_SUGGESTIONS', true),
    ],

    /**
     * 错误处理配置
     */
    'error_handling' => [
        /**
         * 是否记录错误日志
         */
        'log_errors' => env('DB_LOG_ERRORS', true),

        /**
         * 错误日志级别
         */
        'error_log_level' => env('DB_ERROR_LOG_LEVEL', 'error'),

        /**
         * 是否在错误时抛出异常
         */
        'throw_exceptions' => env('DB_THROW_EXCEPTIONS', true),

        /**
         * 错误重试配置
         */
        'retry_on_error' => [
            'enabled' => env('DB_RETRY_ON_ERROR', true),
            'max_attempts' => env('DB_RETRY_MAX_ATTEMPTS', 3),
            'delay' => env('DB_RETRY_DELAY', 1000), // 毫秒
        ],
    ],

    /**
     * 开发环境配置
     */
    'development' => [
        /**
         * 是否启用调试模式
         */
        'debug_mode' => env('DB_DEBUG_MODE', env('APP_DEBUG', false)),

        /**
         * 是否显示查询详情
         */
        'show_query_details' => env('DB_SHOW_QUERY_DETAILS', false),

        /**
         * 是否启用查询分析器
         */
        'enable_profiler' => env('DB_ENABLE_PROFILER', false),
    ],

    /**
     * 生产环境配置
     */
    'production' => [
        /**
         * 是否启用查询缓存
         */
        'enable_query_cache' => env('DB_PROD_ENABLE_CACHE', true),

        /**
         * 是否启用查询优化
         */
        'enable_optimization' => env('DB_PROD_ENABLE_OPTIMIZATION', true),

        /**
         * 是否启用性能监控
         */
        'enable_monitoring' => env('DB_PROD_ENABLE_MONITORING', true),

        /**
         * 慢查询告警阈值（毫秒）
         */
        'slow_query_alert_threshold' => env('DB_PROD_SLOW_QUERY_ALERT', 2000),
    ],
];

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Redis 服务配置
    |--------------------------------------------------------------------------
    |
    | 这里配置 Redis 服务的相关参数，包括默认连接、序列化选项等
    |
    */

    /**
     * 默认 Redis 连接名称
     *
     * 指定 RedisService 默认使用的连接名称
     */
    'default_connection' => env('REDIS_DEFAULT_CONNECTION', 'default'),

    /**
     * 序列化配置
     */
    'serialization' => [
        /**
         * 是否启用自动序列化
         *
         * 当设置为 true 时，所有非字符串值都会自动序列化为 JSON
         */
        'auto_serialize' => env('REDIS_AUTO_SERIALIZE', true),

        /**
         * JSON 序列化选项
         *
         * 用于 json_encode 的选项标志
         */
        'json_options' => JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE,
    ],

    /**
     * 错误处理配置
     */
    'error_handling' => [
        /**
         * 是否记录错误日志
         */
        'log_errors' => env('REDIS_LOG_ERRORS', true),

        /**
         * 错误日志级别
         */
        'log_level' => env('REDIS_LOG_LEVEL', 'error'),

        /**
         * 是否在连接失败时抛出异常
         */
        'throw_on_connection_failure' => env('REDIS_THROW_ON_CONNECTION_FAILURE', false),
    ],

    /**
     * 性能配置
     */
    'performance' => [
        /**
         * 批量操作的最大键数量
         */
        'max_batch_size' => env('REDIS_MAX_BATCH_SIZE', 1000),

        /**
         * 默认过期时间（秒）
         *
         * 当没有指定过期时间时使用的默认值，null 表示永不过期
         */
        'default_ttl' => env('REDIS_DEFAULT_TTL', null),
    ],

    /**
     * 键名前缀配置
     */
    'key_prefix' => [
        /**
         * 缓存键前缀
         */
        'cache' => env('REDIS_CACHE_PREFIX', 'cache:'),

        /**
         * 队列键前缀
         */
        'queue' => env('REDIS_QUEUE_PREFIX', 'queue:'),

        /**
         * 会话键前缀
         */
        'session' => env('REDIS_SESSION_PREFIX', 'session:'),

        /**
         * 临时数据键前缀
         */
        'temp' => env('REDIS_TEMP_PREFIX', 'temp:'),
    ],

    /**
     * 监控配置
     */
    'monitoring' => [
        /**
         * 是否启用性能监控
         */
        'enabled' => env('REDIS_MONITORING_ENABLED', false),

        /**
         * 慢查询阈值（毫秒）
         */
        'slow_query_threshold' => env('REDIS_SLOW_QUERY_THRESHOLD', 100),

        /**
         * 是否记录所有操作
         */
        'log_all_operations' => env('REDIS_LOG_ALL_OPERATIONS', false),
    ],
];

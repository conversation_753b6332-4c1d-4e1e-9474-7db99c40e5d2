<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 错误消息配置
    |--------------------------------------------------------------------------
    |
    | 这里定义了应用程序的错误消息和对应的HTTP状态码
    | 支持参数化消息，使用 :param 格式定义参数占位符
    |
    */

    'Lead' => [
        'not_found' => [
            'message' => '线索不存在',
            'code' => 404,
        ],
        'company_name_exists' => [
            'message' => '该公司名称已被其他线索使用',
            'code' => 409,
        ],
        'company_already_exists' => [
            'message' => '该公司已存在线索记录',
            'code' => 409,
        ],
        'status_not_deletable' => [
            'message' => '当前状态的线索不允许删除',
            'code' => 403,
        ],
        'invalid_status' => [
            'message' => '无效的线索状态',
            'code' => 400,
        ],
        'operation_not_supported' => [
            'message' => '不支持的操作类型: :type',
            'code' => 400,
        ],
        'batch_limit_exceeded' => [
            'message' => '批量操作数量超出限制，最多允许 :limit 条',
            'code' => 400,
        ],
        'test_business_exception' => [
            'message' => '测试业务异常 - 用于验证异常传播机制',
            'code' => 422,
        ],
    ],

    'contact' => [
        'not_found' => [
            'message' => '联系人不存在',
            'code' => 404,
        ],
        'mobile_exists' => [
            'message' => '该手机号已被其他联系人使用',
            'code' => 409,
        ],
    ],

    'user' => [
        'not_found' => [
            'message' => '用户不存在',
            'code' => 404,
        ],
        'permission_denied' => [
            'message' => '您没有权限执行此操作',
            'code' => 403,
        ],
        'lead_limit_exceeded' => [
            'message' => '该用户分配的线索已达上限',
            'code' => 422,
        ],
    ],

    'system' => [
        'internal_error' => [
            'message' => '系统内部错误',
            'code' => 500,
        ],
        'service_unavailable' => [
            'message' => '服务暂时不可用',
            'code' => 503,
        ],
        'validation_failed' => [
            'message' => '数据验证失败',
            'code' => 422,
        ],
    ],
];
